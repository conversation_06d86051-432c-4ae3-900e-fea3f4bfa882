library(survey)


#绘制图4按糖尿病的分层分析
#加载数据
load("data.RData")
data  = droplevels(data)
data$DIQ = factor(data$DIQ)
#用MLR_log计算出的HR过大(因为MLR的值太小了)，所以对其进行标准化处理
data$MLR_log = rescale(data$MLR_log, to=c(0,5))
data$Cd_log = rescale(data$Cd_log, to=c(0,5))
data$HDL_log = rescale(data$HDL_log, to=c(0,5))

data_design <- svydesign(ids=~SDMVPSU,   #集群，抽样单元PSU
                         strata=~SDMVSTRA,   #分层
                         weights=~WTMEC,
                         nest=TRUE,
                         survey.lonely.psu="adjust",  #抽样单元为1时不报错
                         data=data)


#定义计算不同情况下HR值和CI的函数
layer_analysise = function(treat, status, data_design){
  #variable是要分析的变量名称
  #status是结局变量名称
  #data_design是用到的数据框
  cor = "+ Age + Sex + Race + Educationlevel +
                 PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus +
                 Alcoholstatus+ Failingkidneys + Hypertension"
  f1 = as.formula(paste0("Surv(permth_int, ", status, ")", "~", treat, cor))
  
  #自变量和中介变量的加权cox回归模型
  fit1 = svycoxph(f1, design=data_design)

  #提取HR和置信区间
  CI = round(data.frame(summary(fit1)$conf.int)[treat,], digit=3)
  
  y = c(CI[,1], CI[,3], CI[,4], paste0(CI[,1], "(",  CI[,3], ",", CI[,4], ")"))
  return(y)
}


#生成用于绘图的表格
mylayer = data.frame(
  x1 = c("Strata"),
  x2 = c("NA"),
  x3 = c("NA"),
  x4 = c("NA"),
  x5 = c("HR(95% CI)"),
  x6 = c("P for interraction")
)

#全因死亡率
mylayer[nrow(mylayer) + 1,] <- c("All-cause mortality", "NA", "NA", "NA", "NA", "NA")
design_preDM = subset(data_design, DIQ == 1)#糖尿病前期样本
design_DM = subset(data_design, DIQ == 2)#糖尿病样本

line1 = layer_analysise("Cd_log", "Allcausemortstat", data_design)#总体情况
mylayer[nrow(mylayer) + 1,] <- c("Overall", line1, "NA")
#交互作用P值
fit4 <- svycoxph(Surv(permth_int, Allcausemortstat) ~ Cd_log + Age + Sex + Race + Educationlevel + 
                   PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
                   Alcoholstatus+ Failingkidneys + Hypertension + Cd_log*DIQ, design=data_design)

#计算交互作用P值的大小
a = summary(fit4)$coefficients["Cd_log:DIQ2", "Pr(>|z|)"]
mylayer[nrow(mylayer) + 1,] <- c("Participant status", "NA", "NA", "NA", "NA", format.pval(a,digit=3,eps=0.001))

line1 = layer_analysise("Cd_log", "Allcausemortstat", design_preDM)#糖尿病前期情况
mylayer[nrow(mylayer) + 1,] <- c("Prediabetes", line1, "NA")
line1 = layer_analysise("Cd_log", "Allcausemortstat", design_DM)#糖尿病情况
mylayer[nrow(mylayer) + 1,] <- c("diabetes", line1, "NA")


#CVD死亡率
mylayer[nrow(mylayer) + 1,] <- c("CVD mortality", "NA", "NA", "NA", "NA", "NA")
design_preDM = subset(data_design, DIQ == 1)#糖尿病前期样本
design_DM = subset(data_design, DIQ == 2)#糖尿病样本

line1 = layer_analysise("Cd_log", "CVDmortstat", data_design)#总体情况
mylayer[nrow(mylayer) + 1,] <- c("Overall", line1, "NA")
#交互作用P值
fit4 <- svycoxph(Surv(permth_int, CVDmortstat) ~ Cd_log + Age + Sex + Race + Educationlevel + 
                   PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
                   Alcoholstatus+ Failingkidneys + Hypertension + Cd_log*DIQ, design=data_design)

#计算交互作用P值的大小
a = summary(fit4)$coefficients["Cd_log:DIQ2", "Pr(>|z|)"]
mylayer[nrow(mylayer) + 1,] <- c("Participant status", "NA", "NA", "NA", "NA", format.pval(a,digit=3,eps=0.001))

line1 = layer_analysise("Cd_log", "CVDmortstat", design_preDM)#糖尿病前期情况
mylayer[nrow(mylayer) + 1,] <- c("Prediabetes", line1, "NA")
line1 = layer_analysise("Cd_log", "CVDmortstat", design_DM)#糖尿病情况
mylayer[nrow(mylayer) + 1,] <- c("diabetes", line1, "NA")

#CANSER死亡率
mylayer[nrow(mylayer) + 1,] <- c("CANSER mortality", "NA", "NA", "NA", "NA", "NA")
design_preDM = subset(data_design, DIQ == 1)#糖尿病前期样本
design_DM = subset(data_design, DIQ == 2)#糖尿病样本

line1 = layer_analysise("Cd_log", "CANSERmortstat", data_design)#总体情况
mylayer[nrow(mylayer) + 1,] <- c("Overall", line1, "NA")
#交互作用P值
fit4 <- svycoxph(Surv(permth_int, CANSERmortstat) ~ Cd_log + Age + Sex + Race + Educationlevel + 
                   PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
                   Alcoholstatus+ Failingkidneys + Hypertension + Cd_log*DIQ, design=data_design)

#计算交互作用P值的大小
a = summary(fit4)$coefficients["Cd_log:DIQ2", "Pr(>|z|)"]
mylayer[nrow(mylayer) + 1,] <- c("Participant status", "NA", "NA", "NA", "NA", format.pval(a,digit=3,eps=0.001))

line1 = layer_analysise("Cd_log", "CANSERmortstat", design_preDM)#糖尿病前期情况
mylayer[nrow(mylayer) + 1,] <- c("Prediabetes", line1, "NA")
line1 = layer_analysise("Cd_log", "CANSERmortstat", design_DM)#糖尿病情况
mylayer[nrow(mylayer) + 1,] <- c("diabetes", line1, "NA")



mylayer[mylayer == "NA"] = NA
mylayer$x2 = as.numeric(mylayer$x2)
mylayer$x3 = as.numeric(mylayer$x3)
mylayer$x4 = as.numeric(mylayer$x4)

library(forestplot)
library(forestploter)
# 定义绘图的主题
tm <- forest_theme(
  base_size = 10,       
  refline_col = "red4",
  refline_lwd = 2,
  #ci_col = "#1E9C76",
  refline_lty = "solid",
  arrow_type = "closed",    
  ci_lty = 1,
  ci_Theight = 0.2,
  ci_lwd = 2.3
)


#绘制图4
p = forestplot(labeltext = as.matrix(mylayer[,c(1,5,6)]),
           #设置用于文本展示的列，此处我们用数据的前四列作为文本，在图中展示
           mean = mylayer$x2, #设置均值
           lower = mylayer$x3, #设置均值的lowlimits限
           upper = mylayer$x4, #设置均值的uplimits限
           is.summary=c(T,T,F,F,F,F,T,F,F,F,F,T,F,F,F,F),
           #该参数接受一个逻辑向量，用于定义数据中每一行是否是汇总值，若是，则在对应位置设置为TRUE，若否，则设置为FALSE；设置为TRUE的行则以粗体出现
           zero = 1, #设置参照值，此处我们展示的是HR值，故参照值是1，而不是0
           boxsize = 0.2, #设置点估计的方形大小
           lineheight = unit(8,'mm'),#设置图形中的行距
           colgap = unit(6,'mm'),#设置图形中的列间距
           lwd.zero = 0,#设置参考线的粗细
           lwd.ci = 1,#设置区间估计线的粗细
           arrow_lab = c("Low risk", "High Risk"),
           graphwidth = unit(40,'mm'),#设置置信区间绘图宽度
           col=fpColors(box='black',summary="black",lines = 'black',zero = 'black'),
           #使用fpColors()函数定义图形元素的颜色，从左至右分别对应点估计方形，汇总值，区间估计线，参考线
           xlab="The estimates",#设置x轴标签
           lwd.xaxis=0,#设置X轴线的粗细
           lty.ci = "solid",
           theme = tm,   
           graph.pos = 2)#设置森林图的位置，此处设置为2，则出现在第2列

#保存图片为pdf
pdf("图4.pdf")
p
dev.off()



#图5


#生成用于绘图的表格,图s5糖尿病样本情况
mylayer = data.frame(
  x1 = c("NA"),
  x2 = c("NA"),
  x3 = c("NA"),
  x4 = c("NA"),
  x5 = c("Diabetes,HR(95% CI)"),
  x6 = c("P for interraction")
)

n_dm = sum(data$DIQ == 2)#糖尿病样本数，不加权
n_predm = sum(data$DIQ == 1)#糖尿病样本数，不加权
n_All = sum(data$Allcausemortstat == 1)#全因死亡人数
n_CVD = sum(data$CVDmortstat == 1)#CVD死亡人数
n_CANSER = sum(data$CANSERmortstat == 1)#CANSER死亡人数

#全因死亡率
mylayer[nrow(mylayer) + 1,] <- c("All-cause mortality,NO", "NA", "NA", "NA", paste0(n_All, "/", n_dm), "NA")

#GGT
fit4 <- svycoxph(Surv(permth_int, Allcausemortstat) ~ GGT_log + Age + Sex + Race + Educationlevel +
                   PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus +
                   Alcoholstatus+ Failingkidneys + Hypertension + GGT_log*DIQ, design=data_design)

a = summary(fit4)$coefficients["GGT_log:DIQ2", "Pr(>|z|)"]#计算交互作用P值的大小
line1 = layer_analysise("GGT_log", "Allcausemortstat", design_DM)#糖尿病情况
mylayer[nrow(mylayer) + 1,] <- c("GGT", line1, format.pval(a,digit=3,eps=0.001))

#UA
fit4 <- svycoxph(Surv(permth_int, Allcausemortstat) ~ UA_log + Age + Sex + Race + Educationlevel +
                   PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus +
                   Alcoholstatus+ Failingkidneys + Hypertension + UA_log*DIQ, design=data_design)

a = summary(fit4)$coefficients["UA_log:DIQ2", "Pr(>|z|)"]#计算交互作用P值的大小
line1 = layer_analysise("UA_log", "Allcausemortstat", design_DM)#糖尿病情况
mylayer[nrow(mylayer) + 1,] <- c("UA", line1, format.pval(a,digit=3,eps=0.001))

#HDL
fit4 <- svycoxph(Surv(permth_int, Allcausemortstat) ~ HDL_log + Age + Sex + Race + Educationlevel +
                   PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus +
                   Alcoholstatus+ Failingkidneys + Hypertension + HDL_log*DIQ, design=data_design)

a = summary(fit4)$coefficients["HDL_log:DIQ2", "Pr(>|z|)"]#计算交互作用P值的大小
line1 = layer_analysise("HDL_log", "Allcausemortstat", design_DM)#糖尿病情况
mylayer[nrow(mylayer) + 1,] <- c("HDL", line1, format.pval(a,digit=3,eps=0.001))

#UHR
fit4 <- svycoxph(Surv(permth_int, Allcausemortstat) ~ UHR_log + Age + Sex + Race + Educationlevel +
                   PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus +
                   Alcoholstatus+ Failingkidneys + Hypertension + UHR_log*DIQ, design=data_design)

a = summary(fit4)$coefficients["UHR_log:DIQ2", "Pr(>|z|)"]#计算交互作用P值的大小
line1 = layer_analysise("UHR_log", "Allcausemortstat", design_DM)#糖尿病情况
mylayer[nrow(mylayer) + 1,] <- c("UHR", line1, format.pval(a,digit=3,eps=0.001))

#NLR
fit4 <- svycoxph(Surv(permth_int, Allcausemortstat) ~ NLR_log + Age + Sex + Race + Educationlevel +
                   PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus +
                   Alcoholstatus+ Failingkidneys + Hypertension + NLR_log*DIQ, design=data_design)

a = summary(fit4)$coefficients["NLR_log:DIQ2", "Pr(>|z|)"]#计算交互作用P值的大小
line1 = layer_analysise("NLR_log", "Allcausemortstat", design_DM)#糖尿病情况
mylayer[nrow(mylayer) + 1,] <- c("NLR", line1, format.pval(a,digit=3,eps=0.001))

#MLR
fit4 <- svycoxph(Surv(permth_int, Allcausemortstat) ~ MLR_log + Age + Sex + Race + Educationlevel +
                   PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus +
                   Alcoholstatus+ Failingkidneys + Hypertension + MLR_log*DIQ, design=data_design)

a = summary(fit4)$coefficients["MLR_log:DIQ2", "Pr(>|z|)"]#计算交互作用P值的大小
line1 = layer_analysise("MLR_log", "Allcausemortstat", design_DM)#糖尿病情况
mylayer[nrow(mylayer) + 1,] <- c("MLR", line1, format.pval(a,digit=3,eps=0.001))

#NMLR
fit4 <- svycoxph(Surv(permth_int, Allcausemortstat) ~ NMLR_log + Age + Sex + Race + Educationlevel +
                   PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus +
                   Alcoholstatus+ Failingkidneys + Hypertension + NMLR_log*DIQ, design=data_design)

a = summary(fit4)$coefficients["NMLR_log:DIQ2", "Pr(>|z|)"]#计算交互作用P值的大小
line1 = layer_analysise("NMLR_log", "Allcausemortstat", design_DM)#糖尿病情况
mylayer[nrow(mylayer) + 1,] <- c("NMLR", line1, format.pval(a,digit=3,eps=0.001))

#SIRI
fit4 <- svycoxph(Surv(permth_int, Allcausemortstat) ~ SIRI_log + Age + Sex + Race + Educationlevel +
                   PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus +
                   Alcoholstatus+ Failingkidneys + Hypertension + SIRI_log*DIQ, design=data_design)

a = summary(fit4)$coefficients["SIRI_log:DIQ2", "Pr(>|z|)"]#计算交互作用P值的大小
line1 = layer_analysise("SIRI_log", "Allcausemortstat", design_DM)#糖尿病情况
mylayer[nrow(mylayer) + 1,] <- c("SIRI", line1, format.pval(a,digit=3,eps=0.001))

#SII
fit4 <- svycoxph(Surv(permth_int, Allcausemortstat) ~ SII_log + Age + Sex + Race + Educationlevel +
                   PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus +
                   Alcoholstatus+ Failingkidneys + Hypertension + SII_log*DIQ, design=data_design)

a = summary(fit4)$coefficients["SII_log:DIQ2", "Pr(>|z|)"]#计算交互作用P值的大小
line1 = layer_analysise("SII_log", "Allcausemortstat", design_DM)#糖尿病情况
mylayer[nrow(mylayer) + 1,] <- c("SII", line1, format.pval(a,digit=3,eps=0.001))



#CVD死亡率
mylayer[nrow(mylayer) + 1,] <- c("CVD mortality,NO", "NA", "NA", "NA", paste0(n_CVD, "/", n_dm), "NA")

#GGT
fit4 <- svycoxph(Surv(permth_int, CVDmortstat) ~ GGT_log + Age + Sex + Race + Educationlevel +
                   PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus +
                   Alcoholstatus+ Failingkidneys + Hypertension + GGT_log*DIQ, design=data_design)

a = summary(fit4)$coefficients["GGT_log:DIQ2", "Pr(>|z|)"]#计算交互作用P值的大小
line1 = layer_analysise("GGT_log", "CVDmortstat", design_DM)#糖尿病情况
mylayer[nrow(mylayer) + 1,] <- c("GGT", line1, format.pval(a,digit=3,eps=0.001))

#UA
fit4 <- svycoxph(Surv(permth_int, CVDmortstat) ~ UA_log + Age + Sex + Race + Educationlevel +
                   PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus +
                   Alcoholstatus+ Failingkidneys + Hypertension + UA_log*DIQ, design=data_design)

a = summary(fit4)$coefficients["UA_log:DIQ2", "Pr(>|z|)"]#计算交互作用P值的大小
line1 = layer_analysise("UA_log", "CVDmortstat", design_DM)#糖尿病情况
mylayer[nrow(mylayer) + 1,] <- c("UA", line1, format.pval(a,digit=3,eps=0.001))

#HDL
fit4 <- svycoxph(Surv(permth_int, CVDmortstat) ~ HDL_log + Age + Sex + Race + Educationlevel +
                   PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus +
                   Alcoholstatus+ Failingkidneys + Hypertension + HDL_log*DIQ, design=data_design)

a = summary(fit4)$coefficients["HDL_log:DIQ2", "Pr(>|z|)"]#计算交互作用P值的大小
line1 = layer_analysise("HDL_log", "CVDmortstat", design_DM)#糖尿病情况
mylayer[nrow(mylayer) + 1,] <- c("HDL", line1, format.pval(a,digit=3,eps=0.001))

#UHR
fit4 <- svycoxph(Surv(permth_int, CVDmortstat) ~ UHR_log + Age + Sex + Race + Educationlevel +
                   PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus +
                   Alcoholstatus+ Failingkidneys + Hypertension + UHR_log*DIQ, design=data_design)

a = summary(fit4)$coefficients["UHR_log:DIQ2", "Pr(>|z|)"]#计算交互作用P值的大小
line1 = layer_analysise("UHR_log", "CVDmortstat", design_DM)#糖尿病情况
mylayer[nrow(mylayer) + 1,] <- c("UHR", line1, format.pval(a,digit=3,eps=0.001))

#NLR
fit4 <- svycoxph(Surv(permth_int, CVDmortstat) ~ NLR_log + Age + Sex + Race + Educationlevel +
                   PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus +
                   Alcoholstatus+ Failingkidneys + Hypertension + NLR_log*DIQ, design=data_design)

a = summary(fit4)$coefficients["NLR_log:DIQ2", "Pr(>|z|)"]#计算交互作用P值的大小
line1 = layer_analysise("NLR_log", "CVDmortstat", design_DM)#糖尿病情况
mylayer[nrow(mylayer) + 1,] <- c("NLR", line1, format.pval(a,digit=3,eps=0.001))

#MLR
fit4 <- svycoxph(Surv(permth_int, CVDmortstat) ~ MLR_log + Age + Sex + Race + Educationlevel +
                   PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus +
                   Alcoholstatus+ Failingkidneys + Hypertension + MLR_log*DIQ, design=data_design)

a = summary(fit4)$coefficients["MLR_log:DIQ2", "Pr(>|z|)"]#计算交互作用P值的大小
line1 = layer_analysise("MLR_log", "CVDmortstat", design_DM)#糖尿病情况
mylayer[nrow(mylayer) + 1,] <- c("MLR", line1, format.pval(a,digit=3,eps=0.001))

#NMLR
fit4 <- svycoxph(Surv(permth_int, CVDmortstat) ~ NMLR_log + Age + Sex + Race + Educationlevel +
                   PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus +
                   Alcoholstatus+ Failingkidneys + Hypertension + NMLR_log*DIQ, design=data_design)

a = summary(fit4)$coefficients["NMLR_log:DIQ2", "Pr(>|z|)"]#计算交互作用P值的大小
line1 = layer_analysise("NMLR_log", "CVDmortstat", design_DM)#糖尿病情况
mylayer[nrow(mylayer) + 1,] <- c("NMLR", line1, format.pval(a,digit=3,eps=0.001))

#SIRI
fit4 <- svycoxph(Surv(permth_int, CVDmortstat) ~ SIRI_log + Age + Sex + Race + Educationlevel +
                   PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus +
                   Alcoholstatus+ Failingkidneys + Hypertension + SIRI_log*DIQ, design=data_design)

a = summary(fit4)$coefficients["SIRI_log:DIQ2", "Pr(>|z|)"]#计算交互作用P值的大小
line1 = layer_analysise("SIRI_log", "CVDmortstat", design_DM)#糖尿病情况
mylayer[nrow(mylayer) + 1,] <- c("SIRI", line1, format.pval(a,digit=3,eps=0.001))

#SII
fit4 <- svycoxph(Surv(permth_int, CVDmortstat) ~ SII_log + Age + Sex + Race + Educationlevel +
                   PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus +
                   Alcoholstatus+ Failingkidneys + Hypertension + SII_log*DIQ, design=data_design)

a = summary(fit4)$coefficients["SII_log:DIQ2", "Pr(>|z|)"]#计算交互作用P值的大小
line1 = layer_analysise("SII_log", "CVDmortstat", design_DM)#糖尿病情况
mylayer[nrow(mylayer) + 1,] <- c("SII", line1, format.pval(a,digit=3,eps=0.001))



#CANSER死亡率
mylayer[nrow(mylayer) + 1,] <- c("CANSER mortality,NO", "NA", "NA", "NA", paste0(n_CANSER, "/", n_dm), "NA")

#GGT
fit4 <- svycoxph(Surv(permth_int, CANSERmortstat) ~ GGT_log + Age + Sex + Race + Educationlevel +
                   PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus +
                   Alcoholstatus+ Failingkidneys + Hypertension + GGT_log*DIQ, design=data_design)

a = summary(fit4)$coefficients["GGT_log:DIQ2", "Pr(>|z|)"]#计算交互作用P值的大小
line1 = layer_analysise("GGT_log", "CANSERmortstat", design_DM)#糖尿病情况
mylayer[nrow(mylayer) + 1,] <- c("GGT", line1, format.pval(a,digit=3,eps=0.001))

#UA
fit4 <- svycoxph(Surv(permth_int, CANSERmortstat) ~ UA_log + Age + Sex + Race + Educationlevel +
                   PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus +
                   Alcoholstatus+ Failingkidneys + Hypertension + UA_log*DIQ, design=data_design)

a = summary(fit4)$coefficients["UA_log:DIQ2", "Pr(>|z|)"]#计算交互作用P值的大小
line1 = layer_analysise("UA_log", "CANSERmortstat", design_DM)#糖尿病情况
mylayer[nrow(mylayer) + 1,] <- c("UA", line1, format.pval(a,digit=3,eps=0.001))

#HDL
fit4 <- svycoxph(Surv(permth_int, CANSERmortstat) ~ HDL_log + Age + Sex + Race + Educationlevel +
                   PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus +
                   Alcoholstatus+ Failingkidneys + Hypertension + HDL_log*DIQ, design=data_design)

a = summary(fit4)$coefficients["HDL_log:DIQ2", "Pr(>|z|)"]#计算交互作用P值的大小
line1 = layer_analysise("HDL_log", "CANSERmortstat", design_DM)#糖尿病情况
mylayer[nrow(mylayer) + 1,] <- c("HDL", line1, format.pval(a,digit=3,eps=0.001))

#UHR
fit4 <- svycoxph(Surv(permth_int, CANSERmortstat) ~ UHR_log + Age + Sex + Race + Educationlevel +
                   PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus +
                   Alcoholstatus+ Failingkidneys + Hypertension + UHR_log*DIQ, design=data_design)

a = summary(fit4)$coefficients["UHR_log:DIQ2", "Pr(>|z|)"]#计算交互作用P值的大小
line1 = layer_analysise("UHR_log", "CANSERmortstat", design_DM)#糖尿病情况
mylayer[nrow(mylayer) + 1,] <- c("UHR", line1, format.pval(a,digit=3,eps=0.001))

#NLR
fit4 <- svycoxph(Surv(permth_int, CANSERmortstat) ~ NLR_log + Age + Sex + Race + Educationlevel +
                   PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus +
                   Alcoholstatus+ Failingkidneys + Hypertension + NLR_log*DIQ, design=data_design)

a = summary(fit4)$coefficients["NLR_log:DIQ2", "Pr(>|z|)"]#计算交互作用P值的大小
line1 = layer_analysise("NLR_log", "CANSERmortstat", design_DM)#糖尿病情况
mylayer[nrow(mylayer) + 1,] <- c("NLR", line1, format.pval(a,digit=3,eps=0.001))

#MLR
fit4 <- svycoxph(Surv(permth_int, CANSERmortstat) ~ MLR_log + Age + Sex + Race + Educationlevel +
                   PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus +
                   Alcoholstatus+ Failingkidneys + Hypertension + MLR_log*DIQ, design=data_design)

a = summary(fit4)$coefficients["MLR_log:DIQ2", "Pr(>|z|)"]#计算交互作用P值的大小
line1 = layer_analysise("MLR_log", "CANSERmortstat", design_DM)#糖尿病情况
mylayer[nrow(mylayer) + 1,] <- c("MLR", line1, format.pval(a,digit=3,eps=0.001))

#NMLR
fit4 <- svycoxph(Surv(permth_int, CANSERmortstat) ~ NMLR_log + Age + Sex + Race + Educationlevel +
                   PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus +
                   Alcoholstatus+ Failingkidneys + Hypertension + NMLR_log*DIQ, design=data_design)

a = summary(fit4)$coefficients["NMLR_log:DIQ2", "Pr(>|z|)"]#计算交互作用P值的大小
line1 = layer_analysise("NMLR_log", "CANSERmortstat", design_DM)#糖尿病情况
mylayer[nrow(mylayer) + 1,] <- c("NMLR", line1, format.pval(a,digit=3,eps=0.001))

#SIRI
fit4 <- svycoxph(Surv(permth_int, CANSERmortstat) ~ SIRI_log + Age + Sex + Race + Educationlevel +
                   PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus +
                   Alcoholstatus+ Failingkidneys + Hypertension + SIRI_log*DIQ, design=data_design)

a = summary(fit4)$coefficients["SIRI_log:DIQ2", "Pr(>|z|)"]#计算交互作用P值的大小
line1 = layer_analysise("SIRI_log", "CANSERmortstat", design_DM)#糖尿病情况
mylayer[nrow(mylayer) + 1,] <- c("SIRI", line1, format.pval(a,digit=3,eps=0.001))

#SII
fit4 <- svycoxph(Surv(permth_int, CANSERmortstat) ~ SII_log + Age + Sex + Race + Educationlevel +
                   PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus +
                   Alcoholstatus+ Failingkidneys + Hypertension + SII_log*DIQ, design=data_design)

a = summary(fit4)$coefficients["SII_log:DIQ2", "Pr(>|z|)"]#计算交互作用P值的大小
line1 = layer_analysise("SII_log", "CANSERmortstat", design_DM)#糖尿病情况
mylayer[nrow(mylayer) + 1,] <- c("SII", line1, format.pval(a,digit=3,eps=0.001))

mylayer[mylayer == "NA"] = NA
mylayer$x2 = as.numeric(mylayer$x2)
mylayer$x3 = as.numeric(mylayer$x3)
mylayer$x4 = as.numeric(mylayer$x4)

mylayer_left = mylayer




#生成用于绘图的表格，图s5糖尿病前期情况
mylayer = data.frame(
  x1 = c("NA"),
  x2 = c("NA"),
  x3 = c("NA"),
  x4 = c("NA"),
  x5 = c("preDiabetes,HR(95% CI)"),
  x6 = c("P for interraction")
)

n_dm = sum(data$DIQ == 2)#糖尿病前期样本数，不加权
n_predm = sum(data$DIQ == 1)#糖尿病前期样本数，不加权
n_All = sum(data$Allcausemortstat == 1)#全因死亡人数
n_CVD = sum(data$CVDmortstat == 1)#CVD死亡人数
n_CANSER = sum(data$CANSERmortstat == 1)#CANSER死亡人数

#全因死亡率
mylayer[nrow(mylayer) + 1,] <- c("All-cause mortality,NO", "NA", "NA", "NA", paste0(n_All, "/", n_predm), "NA")

#GGT
fit4 <- svycoxph(Surv(permth_int, Allcausemortstat) ~ GGT_log + Age + Sex + Race + Educationlevel + 
                   PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
                   Alcoholstatus+ Failingkidneys + Hypertension + GGT_log*DIQ, design=data_design)

a = summary(fit4)$coefficients["GGT_log:DIQ2", "Pr(>|z|)"]#计算交互作用P值的大小
line1 = layer_analysise("GGT_log", "Allcausemortstat", design_preDM)#糖尿病前期情况
mylayer[nrow(mylayer) + 1,] <- c("GGT", line1, format.pval(a,digit=3,eps=0.001))

#UA
fit4 <- svycoxph(Surv(permth_int, Allcausemortstat) ~ UA_log + Age + Sex + Race + Educationlevel + 
                   PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
                   Alcoholstatus+ Failingkidneys + Hypertension + UA_log*DIQ, design=data_design)

a = summary(fit4)$coefficients["UA_log:DIQ2", "Pr(>|z|)"]#计算交互作用P值的大小
line1 = layer_analysise("UA_log", "Allcausemortstat", design_preDM)#糖尿病前期情况
mylayer[nrow(mylayer) + 1,] <- c("UA", line1, format.pval(a,digit=3,eps=0.001))

#HDL
fit4 <- svycoxph(Surv(permth_int, Allcausemortstat) ~ HDL_log + Age + Sex + Race + Educationlevel + 
                   PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
                   Alcoholstatus+ Failingkidneys + Hypertension + HDL_log*DIQ, design=data_design)

a = summary(fit4)$coefficients["HDL_log:DIQ2", "Pr(>|z|)"]#计算交互作用P值的大小
line1 = layer_analysise("HDL_log", "Allcausemortstat", design_preDM)#糖尿病前期情况
mylayer[nrow(mylayer) + 1,] <- c("HDL", line1, format.pval(a,digit=3,eps=0.001))

#UHR
fit4 <- svycoxph(Surv(permth_int, Allcausemortstat) ~ UHR_log + Age + Sex + Race + Educationlevel + 
                   PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
                   Alcoholstatus+ Failingkidneys + Hypertension + UHR_log*DIQ, design=data_design)

a = summary(fit4)$coefficients["UHR_log:DIQ2", "Pr(>|z|)"]#计算交互作用P值的大小
line1 = layer_analysise("UHR_log", "Allcausemortstat", design_preDM)#糖尿病前期情况
mylayer[nrow(mylayer) + 1,] <- c("UHR", line1, format.pval(a,digit=3,eps=0.001))

#NLR
fit4 <- svycoxph(Surv(permth_int, Allcausemortstat) ~ NLR_log + Age + Sex + Race + Educationlevel + 
                   PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
                   Alcoholstatus+ Failingkidneys + Hypertension + NLR_log*DIQ, design=data_design)

a = summary(fit4)$coefficients["NLR_log:DIQ2", "Pr(>|z|)"]#计算交互作用P值的大小
line1 = layer_analysise("NLR_log", "Allcausemortstat", design_preDM)#糖尿病前期情况
mylayer[nrow(mylayer) + 1,] <- c("NLR", line1, format.pval(a,digit=3,eps=0.001))

#MLR
fit4 <- svycoxph(Surv(permth_int, Allcausemortstat) ~ MLR_log + Age + Sex + Race + Educationlevel + 
                   PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
                   Alcoholstatus+ Failingkidneys + Hypertension + MLR_log*DIQ, design=data_design)

a = summary(fit4)$coefficients["MLR_log:DIQ2", "Pr(>|z|)"]#计算交互作用P值的大小
line1 = layer_analysise("MLR_log", "Allcausemortstat", design_preDM)#糖尿病前期情况
mylayer[nrow(mylayer) + 1,] <- c("MLR", line1, format.pval(a,digit=3,eps=0.001))

#NMLR
fit4 <- svycoxph(Surv(permth_int, Allcausemortstat) ~ NMLR_log + Age + Sex + Race + Educationlevel + 
                   PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
                   Alcoholstatus+ Failingkidneys + Hypertension + NMLR_log*DIQ, design=data_design)

a = summary(fit4)$coefficients["NMLR_log:DIQ2", "Pr(>|z|)"]#计算交互作用P值的大小
line1 = layer_analysise("NMLR_log", "Allcausemortstat", design_preDM)#糖尿病前期情况
mylayer[nrow(mylayer) + 1,] <- c("NMLR", line1, format.pval(a,digit=3,eps=0.001))

#SIRI
fit4 <- svycoxph(Surv(permth_int, Allcausemortstat) ~ SIRI_log + Age + Sex + Race + Educationlevel + 
                   PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
                   Alcoholstatus+ Failingkidneys + Hypertension + SIRI_log*DIQ, design=data_design)

a = summary(fit4)$coefficients["SIRI_log:DIQ2", "Pr(>|z|)"]#计算交互作用P值的大小
line1 = layer_analysise("SIRI_log", "Allcausemortstat", design_preDM)#糖尿病前期情况
mylayer[nrow(mylayer) + 1,] <- c("SIRI", line1, format.pval(a,digit=3,eps=0.001))

#SII
fit4 <- svycoxph(Surv(permth_int, Allcausemortstat) ~ SII_log + Age + Sex + Race + Educationlevel + 
                   PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
                   Alcoholstatus+ Failingkidneys + Hypertension + SII_log*DIQ, design=data_design)

a = summary(fit4)$coefficients["SII_log:DIQ2", "Pr(>|z|)"]#计算交互作用P值的大小
line1 = layer_analysise("SII_log", "Allcausemortstat", design_preDM)#糖尿病前期情况
mylayer[nrow(mylayer) + 1,] <- c("SII", line1, format.pval(a,digit=3,eps=0.001))



#CVD死亡率
mylayer[nrow(mylayer) + 1,] <- c("CVD mortality,NO", "NA", "NA", "NA", paste0(n_CVD, "/", n_predm), "NA")

#GGT
fit4 <- svycoxph(Surv(permth_int, CVDmortstat) ~ GGT_log + Age + Sex + Race + Educationlevel + 
                   PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
                   Alcoholstatus+ Failingkidneys + Hypertension + GGT_log*DIQ, design=data_design)

a = summary(fit4)$coefficients["GGT_log:DIQ2", "Pr(>|z|)"]#计算交互作用P值的大小
line1 = layer_analysise("GGT_log", "CVDmortstat", design_preDM)#糖尿病前期情况
mylayer[nrow(mylayer) + 1,] <- c("GGT", line1, format.pval(a,digit=3,eps=0.001))

#UA
fit4 <- svycoxph(Surv(permth_int, CVDmortstat) ~ UA_log + Age + Sex + Race + Educationlevel + 
                   PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
                   Alcoholstatus+ Failingkidneys + Hypertension + UA_log*DIQ, design=data_design)

a = summary(fit4)$coefficients["UA_log:DIQ2", "Pr(>|z|)"]#计算交互作用P值的大小
line1 = layer_analysise("UA_log", "CVDmortstat", design_preDM)#糖尿病前期情况
mylayer[nrow(mylayer) + 1,] <- c("UA", line1, format.pval(a,digit=3,eps=0.001))

#HDL
fit4 <- svycoxph(Surv(permth_int, CVDmortstat) ~ HDL_log + Age + Sex + Race + Educationlevel + 
                   PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
                   Alcoholstatus+ Failingkidneys + Hypertension + HDL_log*DIQ, design=data_design)

a = summary(fit4)$coefficients["HDL_log:DIQ2", "Pr(>|z|)"]#计算交互作用P值的大小
line1 = layer_analysise("HDL_log", "CVDmortstat", design_preDM)#糖尿病前期情况
mylayer[nrow(mylayer) + 1,] <- c("HDL", line1, format.pval(a,digit=3,eps=0.001))

#UHR
fit4 <- svycoxph(Surv(permth_int, CVDmortstat) ~ UHR_log + Age + Sex + Race + Educationlevel + 
                   PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
                   Alcoholstatus+ Failingkidneys + Hypertension + UHR_log*DIQ, design=data_design)

a = summary(fit4)$coefficients["UHR_log:DIQ2", "Pr(>|z|)"]#计算交互作用P值的大小
line1 = layer_analysise("UHR_log", "CVDmortstat", design_preDM)#糖尿病前期情况
mylayer[nrow(mylayer) + 1,] <- c("UHR", line1, format.pval(a,digit=3,eps=0.001))

#NLR
fit4 <- svycoxph(Surv(permth_int, CVDmortstat) ~ NLR_log + Age + Sex + Race + Educationlevel + 
                   PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
                   Alcoholstatus+ Failingkidneys + Hypertension + NLR_log*DIQ, design=data_design)

a = summary(fit4)$coefficients["NLR_log:DIQ2", "Pr(>|z|)"]#计算交互作用P值的大小
line1 = layer_analysise("NLR_log", "CVDmortstat", design_preDM)#糖尿病前期情况
mylayer[nrow(mylayer) + 1,] <- c("NLR", line1, format.pval(a,digit=3,eps=0.001))

#MLR
fit4 <- svycoxph(Surv(permth_int, CVDmortstat) ~ MLR_log + Age + Sex + Race + Educationlevel + 
                   PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
                   Alcoholstatus+ Failingkidneys + Hypertension + MLR_log*DIQ, design=data_design)

a = summary(fit4)$coefficients["MLR_log:DIQ2", "Pr(>|z|)"]#计算交互作用P值的大小
line1 = layer_analysise("MLR_log", "CVDmortstat", design_preDM)#糖尿病前期情况
mylayer[nrow(mylayer) + 1,] <- c("MLR", line1, format.pval(a,digit=3,eps=0.001))

#NMLR
fit4 <- svycoxph(Surv(permth_int, CVDmortstat) ~ NMLR_log + Age + Sex + Race + Educationlevel + 
                   PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
                   Alcoholstatus+ Failingkidneys + Hypertension + NMLR_log*DIQ, design=data_design)

a = summary(fit4)$coefficients["NMLR_log:DIQ2", "Pr(>|z|)"]#计算交互作用P值的大小
line1 = layer_analysise("NMLR_log", "CVDmortstat", design_preDM)#糖尿病前期情况
mylayer[nrow(mylayer) + 1,] <- c("NMLR", line1, format.pval(a,digit=3,eps=0.001))

#SIRI
fit4 <- svycoxph(Surv(permth_int, CVDmortstat) ~ SIRI_log + Age + Sex + Race + Educationlevel + 
                   PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
                   Alcoholstatus+ Failingkidneys + Hypertension + SIRI_log*DIQ, design=data_design)

a = summary(fit4)$coefficients["SIRI_log:DIQ2", "Pr(>|z|)"]#计算交互作用P值的大小
line1 = layer_analysise("SIRI_log", "CVDmortstat", design_preDM)#糖尿病前期情况
mylayer[nrow(mylayer) + 1,] <- c("SIRI", line1, format.pval(a,digit=3,eps=0.001))

#SII
fit4 <- svycoxph(Surv(permth_int, CVDmortstat) ~ SII_log + Age + Sex + Race + Educationlevel + 
                   PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
                   Alcoholstatus+ Failingkidneys + Hypertension + SII_log*DIQ, design=data_design)

a = summary(fit4)$coefficients["SII_log:DIQ2", "Pr(>|z|)"]#计算交互作用P值的大小
line1 = layer_analysise("SII_log", "CVDmortstat", design_preDM)#糖尿病前期情况
mylayer[nrow(mylayer) + 1,] <- c("SII", line1, format.pval(a,digit=3,eps=0.001))



#CANSER死亡率
mylayer[nrow(mylayer) + 1,] <- c("CANSER mortality,NO", "NA", "NA", "NA", paste0(n_CANSER, "/", n_predm), "NA")

#GGT
fit4 <- svycoxph(Surv(permth_int, CANSERmortstat) ~ GGT_log + Age + Sex + Race + Educationlevel + 
                   PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
                   Alcoholstatus+ Failingkidneys + Hypertension + GGT_log*DIQ, design=data_design)

a = summary(fit4)$coefficients["GGT_log:DIQ2", "Pr(>|z|)"]#计算交互作用P值的大小
line1 = layer_analysise("GGT_log", "CANSERmortstat", design_preDM)#糖尿病前期情况
mylayer[nrow(mylayer) + 1,] <- c("GGT", line1, format.pval(a,digit=3,eps=0.001))

#UA
fit4 <- svycoxph(Surv(permth_int, CANSERmortstat) ~ UA_log + Age + Sex + Race + Educationlevel + 
                   PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
                   Alcoholstatus+ Failingkidneys + Hypertension + UA_log*DIQ, design=data_design)

a = summary(fit4)$coefficients["UA_log:DIQ2", "Pr(>|z|)"]#计算交互作用P值的大小
line1 = layer_analysise("UA_log", "CANSERmortstat", design_preDM)#糖尿病前期情况
mylayer[nrow(mylayer) + 1,] <- c("UA", line1, format.pval(a,digit=3,eps=0.001))

#HDL
fit4 <- svycoxph(Surv(permth_int, CANSERmortstat) ~ HDL_log + Age + Sex + Race + Educationlevel + 
                   PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
                   Alcoholstatus+ Failingkidneys + Hypertension + HDL_log*DIQ, design=data_design)

a = summary(fit4)$coefficients["HDL_log:DIQ2", "Pr(>|z|)"]#计算交互作用P值的大小
line1 = layer_analysise("HDL_log", "CANSERmortstat", design_preDM)#糖尿病前期情况
mylayer[nrow(mylayer) + 1,] <- c("HDL", line1, format.pval(a,digit=3,eps=0.001))

#UHR
fit4 <- svycoxph(Surv(permth_int, CANSERmortstat) ~ UHR_log + Age + Sex + Race + Educationlevel + 
                   PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
                   Alcoholstatus+ Failingkidneys + Hypertension + UHR_log*DIQ, design=data_design)

a = summary(fit4)$coefficients["UHR_log:DIQ2", "Pr(>|z|)"]#计算交互作用P值的大小
line1 = layer_analysise("UHR_log", "CANSERmortstat", design_preDM)#糖尿病前期情况
mylayer[nrow(mylayer) + 1,] <- c("UHR", line1, format.pval(a,digit=3,eps=0.001))

#NLR
fit4 <- svycoxph(Surv(permth_int, CANSERmortstat) ~ NLR_log + Age + Sex + Race + Educationlevel + 
                   PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
                   Alcoholstatus+ Failingkidneys + Hypertension + NLR_log*DIQ, design=data_design)

a = summary(fit4)$coefficients["NLR_log:DIQ2", "Pr(>|z|)"]#计算交互作用P值的大小
line1 = layer_analysise("NLR_log", "CANSERmortstat", design_preDM)#糖尿病前期情况
mylayer[nrow(mylayer) + 1,] <- c("NLR", line1, format.pval(a,digit=3,eps=0.001))

#MLR
fit4 <- svycoxph(Surv(permth_int, CANSERmortstat) ~ MLR_log + Age + Sex + Race + Educationlevel + 
                   PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
                   Alcoholstatus+ Failingkidneys + Hypertension + MLR_log*DIQ, design=data_design)

a = summary(fit4)$coefficients["MLR_log:DIQ2", "Pr(>|z|)"]#计算交互作用P值的大小
line1 = layer_analysise("MLR_log", "CANSERmortstat", design_preDM)#糖尿病前期情况
mylayer[nrow(mylayer) + 1,] <- c("MLR", line1, format.pval(a,digit=3,eps=0.001))

#NMLR
fit4 <- svycoxph(Surv(permth_int, CANSERmortstat) ~ NMLR_log + Age + Sex + Race + Educationlevel + 
                   PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
                   Alcoholstatus+ Failingkidneys + Hypertension + NMLR_log*DIQ, design=data_design)

a = summary(fit4)$coefficients["NMLR_log:DIQ2", "Pr(>|z|)"]#计算交互作用P值的大小
line1 = layer_analysise("NMLR_log", "CANSERmortstat", design_preDM)#糖尿病前期情况
mylayer[nrow(mylayer) + 1,] <- c("NMLR", line1, format.pval(a,digit=3,eps=0.001))

#SIRI
fit4 <- svycoxph(Surv(permth_int, CANSERmortstat) ~ SIRI_log + Age + Sex + Race + Educationlevel + 
                   PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
                   Alcoholstatus+ Failingkidneys + Hypertension + SIRI_log*DIQ, design=data_design)

a = summary(fit4)$coefficients["SIRI_log:DIQ2", "Pr(>|z|)"]#计算交互作用P值的大小
line1 = layer_analysise("SIRI_log", "CANSERmortstat", design_preDM)#糖尿病前期情况
mylayer[nrow(mylayer) + 1,] <- c("SIRI", line1, format.pval(a,digit=3,eps=0.001))

#SII
fit4 <- svycoxph(Surv(permth_int, CANSERmortstat) ~ SII_log + Age + Sex + Race + Educationlevel + 
                   PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
                   Alcoholstatus+ Failingkidneys + Hypertension + SII_log*DIQ, design=data_design)

a = summary(fit4)$coefficients["SII_log:DIQ2", "Pr(>|z|)"]#计算交互作用P值的大小
line1 = layer_analysise("SII_log", "CANSERmortstat", design_preDM)#糖尿病前期情况
mylayer[nrow(mylayer) + 1,] <- c("SII", line1, format.pval(a,digit=3,eps=0.001))

mylayer[mylayer == "NA"] = NA
mylayer$x2 = as.numeric(mylayer$x2)
mylayer$x3 = as.numeric(mylayer$x3)
mylayer$x4 = as.numeric(mylayer$x4)

mylayer_right = mylayer
mylayer_right$x6 = "                      "#通过空格调整列宽
mylayer_right$x7 = mylayer_left$x2
mylayer_right$x8 = mylayer_left$x3
mylayer_right$x9 = mylayer_left$x4
mylayer_right$x10 = mylayer_left$x5
mylayer_right$x11 = mylayer_left$x6

#绘制图5
p_right = forest(mylayer_right[,c(1,5,6,10,11)],
           #设置用于文本展示的列
           est = list(mylayer_right$x2, mylayer_right$x7), #设置均值
           lower = list(mylayer_right$x3, mylayer_right$x8), #设置均值的lowlimits限
           upper = list(mylayer_right$x4, mylayer_right$x9), #设置均值的uplimits限
           ci_column = 3,
           ref_line  = 1, #设置参照值，此处我们展示的是HR值，故参照值是1，而不是0
           colgap = unit(6,'mm'),#设置图形中的列间距
           theme = tm)#设置森林图的位置，此处设置为2，则出现在第2列
plot(p_right)

#保存图片为pdf
pdf("图5.pdf")
plot(p_right)
dev.off()




















#绘制图S5按性别的分层分析
#加载数据
load("data.RData")
data  = droplevels(data)
data$Sex = factor(data$Sex)
#用MLR_log计算出的HR过大(因为MLR的值太小了)，所以对其进行标准化处理
data$MLR_log = rescale(data$MLR_log, to=c(0,5))
data$Cd_log = rescale(data$Cd_log, to=c(0,5))
data$HDL_log = rescale(data$HDL_log, to=c(0,5))

data_design <- svydesign(ids=~SDMVPSU,   #集群，抽样单元PSU
                         strata=~SDMVSTRA,   #分层
                         weights=~WTMEC,
                         nest=TRUE,
                         survey.lonely.psu="adjust",  #抽样单元为1时不报错
                         data=data)


#定义计算不同情况下HR值和CI的函数
layer_analysise = function(treat, status, data_design){
  #variable是要分析的变量名称
  #status是结局变量名称
  #data_design是用到的数据框
  cor = "+ Age + Race + Educationlevel +
                 PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus +
                 Alcoholstatus+ Failingkidneys + Hypertension"
  f1 = as.formula(paste0("Surv(permth_int, ", status, ")", "~", treat, cor))
  
  #自变量和中介变量的加权cox回归模型
  fit1 = svycoxph(f1, design=data_design)
  
  #提取HR和置信区间
  CI = round(data.frame(summary(fit1)$conf.int)[treat,], digit=3)
  
  y = c(CI[,1], CI[,3], CI[,4], paste0(CI[,1], "(",  CI[,3], ",", CI[,4], ")"))
  return(y)
}


#生成用于绘图的表格
mylayer = data.frame(
  x1 = c("Strata"),
  x2 = c("NA"),
  x3 = c("NA"),
  x4 = c("NA"),
  x5 = c("HR(95% CI)"),
  x6 = c("P for interraction")
)

#全因死亡率
mylayer[nrow(mylayer) + 1,] <- c("All-cause mortality", "NA", "NA", "NA", "NA", "NA")
design_man = subset(data_design, Sex == 1)#男样本
design_woman = subset(data_design, Sex == 2)#女样本

line1 = layer_analysise("Cd_log", "Allcausemortstat", data_design)#总体情况
mylayer[nrow(mylayer) + 1,] <- c("Overall", line1, "NA")
#交互作用P值
fit4 <- svycoxph(Surv(permth_int, Allcausemortstat) ~ Cd_log + Age + Sex + Race + Educationlevel + 
                   PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
                   Alcoholstatus+ Failingkidneys + Hypertension + Cd_log*Sex, design=data_design)

#计算交互作用P值的大小
a = summary(fit4)$coefficients["Cd_log:Sex2", "Pr(>|z|)"]
mylayer[nrow(mylayer) + 1,] <- c("Participant status", "NA", "NA", "NA", "NA", format.pval(a,digit=3,eps=0.001))

line1 = layer_analysise("Cd_log", "Allcausemortstat", design_man)#男情况
mylayer[nrow(mylayer) + 1,] <- c("Prediabetes", line1, "NA")
line1 = layer_analysise("Cd_log", "Allcausemortstat", design_woman)#女情况
mylayer[nrow(mylayer) + 1,] <- c("diabetes", line1, "NA")


#CVD死亡率
mylayer[nrow(mylayer) + 1,] <- c("CVD mortality", "NA", "NA", "NA", "NA", "NA")
design_man = subset(data_design, Sex == 1)#男样本
design_woman = subset(data_design, Sex == 2)#女样本

line1 = layer_analysise("Cd_log", "CVDmortstat", data_design)#总体情况
mylayer[nrow(mylayer) + 1,] <- c("Overall", line1, "NA")
#交互作用P值
fit4 <- svycoxph(Surv(permth_int, CVDmortstat) ~ Cd_log + Age + Sex + Race + Educationlevel + 
                   PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
                   Alcoholstatus+ Failingkidneys + Hypertension + Cd_log*Sex, design=data_design)

#计算交互作用P值的大小
a = summary(fit4)$coefficients["Cd_log:Sex2", "Pr(>|z|)"]
mylayer[nrow(mylayer) + 1,] <- c("Participant status", "NA", "NA", "NA", "NA", format.pval(a,digit=3,eps=0.001))

line1 = layer_analysise("Cd_log", "CVDmortstat", design_man)#男情况
mylayer[nrow(mylayer) + 1,] <- c("Prediabetes", line1, "NA")
line1 = layer_analysise("Cd_log", "CVDmortstat", design_woman)#女情况
mylayer[nrow(mylayer) + 1,] <- c("diabetes", line1, "NA")

#CANSER死亡率
mylayer[nrow(mylayer) + 1,] <- c("CANSER mortality", "NA", "NA", "NA", "NA", "NA")
design_man = subset(data_design, Sex == 1)#男样本
design_woman = subset(data_design, Sex == 2)#女样本

line1 = layer_analysise("Cd_log", "CANSERmortstat", data_design)#总体情况
mylayer[nrow(mylayer) + 1,] <- c("Overall", line1, "NA")
#交互作用P值
fit4 <- svycoxph(Surv(permth_int, CANSERmortstat) ~ Cd_log + Age + Sex + Race + Educationlevel + 
                   PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
                   Alcoholstatus+ Failingkidneys + Hypertension + Cd_log*Sex, design=data_design)

#计算交互作用P值的大小
a = summary(fit4)$coefficients["Cd_log:Sex2", "Pr(>|z|)"]
mylayer[nrow(mylayer) + 1,] <- c("Participant status", "NA", "NA", "NA", "NA", format.pval(a,digit=3,eps=0.001))

line1 = layer_analysise("Cd_log", "CANSERmortstat", design_man)#男情况
mylayer[nrow(mylayer) + 1,] <- c("Prediabetes", line1, "NA")
line1 = layer_analysise("Cd_log", "CANSERmortstat", design_woman)#女情况
mylayer[nrow(mylayer) + 1,] <- c("diabetes", line1, "NA")



mylayer[mylayer == "NA"] = NA
mylayer$x2 = as.numeric(mylayer$x2)
mylayer$x3 = as.numeric(mylayer$x3)
mylayer$x4 = as.numeric(mylayer$x4)

library(forestplot)
library(forestploter)
# 定义绘图的主题
tm <- forest_theme(
  base_size = 10,       
  refline_col = "red4",
  refline_lwd = 2,
  #ci_col = "#1E9C76",
  refline_lty = "solid",
  arrow_type = "closed",    
  ci_lty = 1,
  ci_Theight = 0.2,
  ci_lwd = 2.3
)


#绘制图S5
p = forestplot(labeltext = as.matrix(mylayer[,c(1,5,6)]),
           #设置用于文本展示的列，此处我们用数据的前四列作为文本，在图中展示
           mean = mylayer$x2, #设置均值
           lower = mylayer$x3, #设置均值的lowlimits限
           upper = mylayer$x4, #设置均值的uplimits限
           is.summary=c(T,T,F,F,F,F,T,F,F,F,F,T,F,F,F,F),
           #该参数接受一个逻辑向量，用于定义数据中每一行是否是汇总值，若是，则在对应位置设置为TRUE，若否，则设置为FALSE；设置为TRUE的行则以粗体出现
           zero = 1, #设置参照值，此处我们展示的是HR值，故参照值是1，而不是0
           boxsize = 0.2, #设置点估计的方形大小
           lineheight = unit(8,'mm'),#设置图形中的行距
           colgap = unit(6,'mm'),#设置图形中的列间距
           lwd.zero = 0,#设置参考线的粗细
           lwd.ci = 1,#设置区间估计线的粗细
           arrow_lab = c("Low risk", "High Risk"),
           graphwidth = unit(40,'mm'),#设置置信区间绘图宽度
           col=fpColors(box='black',summary="black",lines = 'black',zero = 'black'),
           #使用fpColors()函数定义图形元素的颜色，从左至右分别对应点估计方形，汇总值，区间估计线，参考线
           xlab="The estimates",#设置x轴标签
           lwd.xaxis=0,#设置X轴线的粗细
           lty.ci = "solid",
           theme = tm,   
           graph.pos = 2)#设置森林图的位置，此处设置为2，则出现在第2列



#保存图片为pdf
pdf("图s5.pdf")
p
dev.off()







