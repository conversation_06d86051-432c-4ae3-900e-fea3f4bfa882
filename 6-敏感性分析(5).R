#血铅纳入协变量之后重做表20和表s5（cox回归和中介分析）对应表s20和s21
load("data.RData")
#生成表20
Tables20 = data.frame(
  x1 = c("NA"),
  x2 = c("Blood Cadmium Levels, HR (95% CI)"),
  x3 = c("NA"),
  x4 = c("NA"),
  x5 = c("NA"),
  x6 = c("P for trend"),
  x7 = c("Per ln-unit increase")
)
Tables20[nrow(Tables20) + 1,] <- c("NA","Blood Cadmium Levels, HR (95% CI)", "NA", "NA", "NA", "P for trend", "Per ln-unit increase")
Tables20[nrow(Tables20) + 1,] <- c("NA","Quantile 1","Quantile 2","Quantile 3","Quantile 4", "NA", "NA")
Q1 = data.frame(summary(data$Cd_4))["Quantile1",]
Q2 = data.frame(summary(data$Cd_4))["Quantile2",]
Q3 = data.frame(summary(data$Cd_4))["Quantile3",]
Q4 = data.frame(summary(data$Cd_4))["Quantile4",]


library(rms)
library(survey)
data  = droplevels(data)

#将Cd进行对数转换,为了不出现赋值，加1后再转换
data$Cd_log = rescale(log(data$Cd + 1),to=c(0,2))
data$Sex = factor(data$Sex, levels=c(1,2), labels=c("Male", "Female"))
data$Race = factor(data$Race, levels=c(1,2,3,4,5), labels=c("Non-Hispanic White","Non-Hispanic Black","Mexican American", "Other Hispanic","Other Race - Including Multi-Racial"))
data$Cd_level = as.numeric(data$Cd_4)
data$permth_int = as.numeric(data$permth_int)

data_design <- svydesign(ids=~SDMVPSU,   #集群，抽样单元PSU
                         strata=~SDMVSTRA,   #分层
                         weights=~WTMEC,
                         nest=TRUE,
                         survey.lonely.psu="adjust",  #抽样单元为1时不报错
                         data=data)

cor_all = "+ Cd_log + Age + Sex + Race + Educationlevel +
                 PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus +
                 Alcoholstatus+ Failingkidneys + Hypertension"
cor_part = "+ Cd_log + Age + Sex + Race"
#计算HR，CI，P trend的函数
Cox_analysise = function(treat, treat_level, treat_con, status, data_design, cor){
  #treat分类变量，treat_level按1-n的连续变量，treat_con按实际值的连续变量
  #cor调整变量
  f1 = as.formula(paste0("Surv(permth_int, ", status, ")", "~", treat, cor))#做分类变量
  fit1 = svycoxph(f1, design = data_design)
  CI = round(data.frame(summary(fit1)$conf.int), digit=3)#提取HR和置信区间
  
  f2 = as.formula(paste0("Surv(permth_int, ", status, ")", "~", treat_level, cor))#计算p trend
  fit2 = svycoxph(f2, design = data_design)
  P = format.pval(data.frame(summary(fit2)$coefficients)[treat_level, "Pr...z.."],digit=3,eps=0.001)
  
  f3 = as.formula(paste0("Surv(permth_int, ", status, ")", "~", treat_con, cor))#做连续变量
  fit3 = svycoxph(f3, design = data_design)
  CI3 = round(data.frame(summary(fit3)$conf.int), digit=3)#提取HR和置信区间
  
  y = c("1.00 (Ref.) ",
        paste0(CI["Cd_4Quantile2", "exp.coef."], "(", CI["Cd_4Quantile2", "lower..95"],",", CI["Cd_4Quantile2", "upper..95"],")"),
        paste0(CI["Cd_4Quantile3", "exp.coef."], "(", CI["Cd_4Quantile3", "lower..95"],",", CI["Cd_4Quantile3", "upper..95"],")"),
        paste0(CI["Cd_4Quantile4", "exp.coef."], "(", CI["Cd_4Quantile4", "lower..95"],",", CI["Cd_4Quantile4", "upper..95"],")"),
        P,
        paste0(CI3[treat_con, "exp.coef."], "(", CI3[treat_con, "lower..95"],",",CI3[treat_con, "upper..95"],")")
  )
  
  return(y)
}


#全因死亡率
All_Q1 = sum(data$Cd_4 == "Quantile1" & data$Allcausemortstat == 1)
All_Q2 = sum(data$Cd_4 == "Quantile2" & data$Allcausemortstat == 1)
All_Q3 = sum(data$Cd_4 == "Quantile3" & data$Allcausemortstat == 1)
All_Q4 = sum(data$Cd_4 == "Quantile4" & data$Allcausemortstat == 1)
Tables20[nrow(Tables20) + 1,] <- c("All-cause mortality, No",paste0(All_Q1,"/",Q1),paste0(All_Q2,"/",Q2),paste0(All_Q3,"/",Q3),paste0(All_Q4,"/",Q4), "NA", "NA")
line = Cox_analysise("Cd_4", "Cd_level", "Cd", "Allcausemortstat",data_design,cor_part)#全因死亡率，部分调整
Tables20[nrow(Tables20) + 1,] <-c("Partially adjusted", line)#将数据写入表20中
line = Cox_analysise("Cd_4", "Cd_level", "Cd", "Allcausemortstat",data_design,cor_all)#全因死亡率，全部调整
Tables20[nrow(Tables20) + 1,] <-c("Fully adjusted", line)#将数据写入表20中

#CVD死亡率
CVD_Q1 = sum(data$Cd_4 == "Quantile1" & data$CVDmortstat == 1)
CVD_Q2 = sum(data$Cd_4 == "Quantile2" & data$CVDmortstat == 1)
CVD_Q3 = sum(data$Cd_4 == "Quantile3" & data$CVDmortstat == 1)
CVD_Q4 = sum(data$Cd_4 == "Quantile4" & data$CVDmortstat == 1)
Tables20[nrow(Tables20) + 1,] <- c("CVD mortality, No",paste0(CVD_Q1,"/",Q1),paste0(CVD_Q2,"/",Q2),paste0(CVD_Q3,"/",Q3),paste0(CVD_Q4,"/",Q4), "NA", "NA")
line = Cox_analysise("Cd_4", "Cd_level", "Cd", "CVDmortstat",data_design,cor_part)#CVD死亡率，部分调整
Tables20[nrow(Tables20) + 1,] <-c("Partially adjusted", line)#将数据写入表20中
line = Cox_analysise("Cd_4", "Cd_level", "Cd", "CVDmortstat",data_design,cor_all)#CVD死亡率，全部调整
Tables20[nrow(Tables20) + 1,] <-c("Fully adjusted", line)#将数据写入表20中

#CANSER死亡率
CANSER_Q1 = sum(data$Cd_4 == "Quantile1" & data$CANSERmortstat == 1)
CANSER_Q2 = sum(data$Cd_4 == "Quantile2" & data$CANSERmortstat == 1)
CANSER_Q3 = sum(data$Cd_4 == "Quantile3" & data$CANSERmortstat == 1)
CANSER_Q4 = sum(data$Cd_4 == "Quantile4" & data$CANSERmortstat == 1)
Tables20[nrow(Tables20) + 1,] <- c("CANSER mortality, No",paste0(CANSER_Q1,"/",Q1),paste0(CANSER_Q2,"/",Q2),paste0(CANSER_Q3,"/",Q3),paste0(CANSER_Q4,"/",Q4), "NA", "NA")
line = Cox_analysise("Cd_4", "Cd_level", "Cd", "CANSERmortstat",data_design,cor_part)#CANSER死亡率，部分调整
Tables20[nrow(Tables20) + 1,] <-c("Partially adjusted", line)#将数据写入表20中
line = Cox_analysise("Cd_4", "Cd_level", "Cd", "CANSERmortstat",data_design,cor_all)#CANSER死亡率，全部调整
Tables20[nrow(Tables20) + 1,] <-c("Fully adjusted", line)#将数据写入表20中

write.csv(Tables20, file = "Tables20.csv")













#血铅纳入协变量重做表s5，生成表s21


data_design <- svydesign(ids=~SDMVPSU,   #集群，抽样单元PSU
                         strata=~SDMVSTRA,   #分层
                         weights=~WTMEC,
                         nest=TRUE,
                         survey.lonely.psu="adjust",  #抽样单元为1时不报错
                         data=data)
dd<-datadist(data)
options(datadist='dd')

cor = "+ Cd_log + Age + Sex + Race + Educationlevel +
                 PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus +
                 Alcoholstatus+ Failingkidneys + Hypertension"
options(survey.lonely.psu = "adjust")

#定义计算中介效应的函数
Median_alalysiae = function(data,indices){
  
  d <- data[indices, ]  # 重抽样数据
  d_design= svydesign(ids=~SDMVPSU,   #集群，抽样单元PSU
                      strata=~SDMVSTRA,   #分层
                      weights=~WTMEC,
                      nest=TRUE,
                      survey.lonely.psu="adjust",  #抽样单元为1时不报错
                      data=d)
  
  cor = "+ Cd_log + Age + Sex + Race + Educationlevel +
                 PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus +
                 Alcoholstatus+ Failingkidneys + Hypertension"
  f1 = as.formula(paste0("Surv(permth_int, ", status, ")", "~", treat, cor))
  f2 = as.formula(paste0(mediator, "~", treat, cor))
  f3 = as.formula(paste0("Surv(permth_int, ", status, ")", "~", treat, "+", mediator, cor))
  
  fit_svy<- svycoxph(f1, design=d_design)
  #weight变量是权重,1总效应模型（X→结局）,Total Effect
  model_total<- coxph(f1, x=TRUE, y=TRUE,data=d, weights = (1/fit_svy$survey.design$prob)/mean(1/fit_svy$survey.design$prob))
  #提取HR和置信区间
  #total_result = ShowRegTable(model_total, exp = FALSE,digits = 3,pDigits = 3,printToggle=FALSE,quote = FALSE, ciFun = confint)
  beta_total <- coef(model_total)[treat]  # 总效应系数
  
  # 2：X→M的关系
  # 用线性回归
  model_XM <- lm(f2, data = d)
  #提取HR和置信区间
  #XM_result = ShowRegTable(model_XM, exp = FALSE,digits = 3,pDigits = 3,printToggle=FALSE,quote = FALSE, ciFun = confint)
  a <- coef(model_XM)[treat]  # X→M的效应
  
  # 3：（X和M共同预测结局）ADE（Average Direct Effects）
  model_direct <- coxph(f3, data = d,weights = (1/fit_svy$survey.design$prob)/mean(1/fit_svy$survey.design$prob))
  #提取HR和置信区间
  #direct_result = ShowRegTable(model_direct, exp = FALSE,digits = 3,pDigits = 3,printToggle=FALSE,quote = FALSE, ciFun = confint)
  beta_direct <- coef(model_direct)[treat]  # 直接效应系数
  b <- coef(model_direct)[mediator]  # M→结局的效应
  
  # 4. 计算效应量 ACME
  indirect_effect <- a * b  # 中介效应 (乘积法)
  proportion_mediated <- indirect_effect / beta_total  # 中介效应占比,PE
  return(c(beta_total, beta_direct, indirect_effect, proportion_mediated))
}


#得到最终的ACME，ADE，PE等和置信区间
Get_result = function(R = 10, data_design=data_design){
  boot_results <- boot(data = data, statistic = Median_alalysiae, R = R)#进行Bootstrap，1000次计算速度太慢了,次数越多，计算的P值越准确
  # 计算置信区间 (95%)
  ci_total <- boot.ci(boot_results, type = "norm", index = 1)  # 总效应CI,TE
  ci_direct <- boot.ci(boot_results, type = "norm", index = 2)  # 直接效应CI,ADE
  ci_indirect <- boot.ci(boot_results, type = "norm", index = 3)  # 中介效应CI,ACME
  ci_pm <- boot.ci(boot_results, type = "norm", index = 4)  # 中介效应占比CI,PE
  print(paste0("分析", treat, "对", status, "的中介效应，中介变量为", mediator))
  
  # 计算观测统计量
  obs_stat  = Median_alalysiae(data,seq(from=1, to=nrow(data)))
  
  # 计算Bootstrap P值（双侧检验）
  #计算ADE值
  cor = "+ Cd_log + Age + Sex + Race + Educationlevel +
                 PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus +
                 Alcoholstatus+ Failingkidneys + Hypertension"
  f3 = as.formula(paste0("Surv(permth_int, ", status, ")", "~", treat, "+", mediator, cor))
  fit_svy<- svycoxph(f3, design=data_design)
  model_direct <- coxph(f3, data = data,weights = (1/fit_svy$survey.design$prob)/mean(1/fit_svy$survey.design$prob))
  #提取HR和置信区间
  p_direct = data.frame(ShowRegTable(model_direct, exp = FALSE,digits = 3,pDigits = 3,printToggle=FALSE,quote = FALSE, ciFun = confint))["Cd", "p"]
  
  #p_total = 1-boot.pval(boot_results,theta_null = obs_stat[1], alternative = "two.sided", index=1,pval_precision=0.00001)
  #p_direct = 1-boot.pval(boot_results,theta_null = obs_stat[2], alternative = "two.sided", index=2,pval_precision=0.00001)
  p_indirect = 1-boot.pval(boot_results,theta_null = obs_stat[3], alternative = "two.sided", index=3,pval_precision=0.001)
  p_pm = 1-boot.pval(boot_results,theta_null = obs_stat[4], alternative = "two.sided", index=4,pval_precision=0.001)
  
  y = c(paste0(round(boot_results$t0[3],digit=3), "(",round(ci_indirect$normal[2],digit=3), ",", round(ci_indirect$normal[3],digit=3), ")"),p_indirect,
        paste0(round(boot_results$t0[2],digit=3), "(",round(ci_direct$normal[2],digit=3), ",", round(ci_direct$normal[3],digit=3), ")"), p_direct,
        paste0(round(boot_results$t0[4],digit=3), "(",round(ci_pm$normal[2],digit=3), ",", round(ci_pm$normal[3],digit=3), ")"), p_pm)
  
  return(y)
}

#生成表s21
Tables21 = data.frame(
  x1 = c("NA"),
  x2 = c("ACME"),
  x3 = c("ACME"),
  x4 = c("ADE"),
  x5 = c("ADE"),  
  x6 = c("PE"),
  x7 = c("PE")
)

#将数据写入tableS5
Tables21[nrow(Tables21) + 1,] <- c("NA","Estimate (95% CI)","P value","Estimate (95% CI)","P value","Estimate (95% CI)","P value")

#结局为全因死亡率
Tables21[nrow(Tables21) + 1,] <- c("All-cause mortality","NA","NA","NA","NA","NA","NA")

#GGT
treat="Cd";mediator="GGT"; status="Allcausemortstat";l1 = Get_result(data_design = data_design)#分析GGT变量的中介效应
Tables21[nrow(Tables21) + 1,] <- c(mediator,l1)#将数据写入Tables21
#UA
treat="Cd";mediator="UA"; status="Allcausemortstat";l1 = Get_result(data_design = data_design)#分析UA变量的中介效应
Tables21[nrow(Tables21) + 1,] <- c(mediator,l1)#将数据写入Tables21
#HDL
treat="Cd";mediator="HDL"; status="Allcausemortstat";l1 = Get_result(data_design = data_design)#分析HDL变量的中介效应
Tables21[nrow(Tables21) + 1,] <- c(mediator,l1)#将数据写入Tables21
#UHR
treat="Cd";mediator="UHR"; status="Allcausemortstat";l1 = Get_result(data_design = data_design)#分析UHR变量的中介效应
Tables21[nrow(Tables21) + 1,] <- c(mediator,l1)#将数据写入Tables21
#NLR
treat="Cd";mediator="NLR"; status="Allcausemortstat";l1 = Get_result(data_design = data_design)#分析NLR变量的中介效应
Tables21[nrow(Tables21) + 1,] <- c(mediator,l1)#将数据写入Tables21
#MLR
treat="Cd";mediator="MLR"; status="Allcausemortstat";l1 = Get_result(data_design = data_design)#分析MLR变量的中介效应
Tables21[nrow(Tables21) + 1,] <- c(mediator,l1)#将数据写入Tables21
#NMLR
treat="Cd";mediator="NMLR"; status="Allcausemortstat";l1 = Get_result(data_design = data_design)#分析NMLR变量的中介效应
Tables21[nrow(Tables21) + 1,] <- c(mediator,l1)#将数据写入Tables21
#SIRI
treat="Cd";mediator="SIRI"; status="Allcausemortstat";l1 = Get_result(data_design = data_design)#分析SIRI变量的中介效应
Tables21[nrow(Tables21) + 1,] <- c(mediator,l1)#将数据写入Tables21
#SII
treat="Cd";mediator="SII"; status="Allcausemortstat";l1 = Get_result(data_design = data_design)#分析SII变量的中介效应
Tables21[nrow(Tables21) + 1,] <- c(mediator,l1)#将数据写入Tables21

#结局为CVD死亡率
Tables21[nrow(Tables21) + 1,] <- c("CVD mortality","NA","NA","NA","NA","NA","NA")

#GGT
treat="Cd";mediator="GGT"; status="CVDmortstat";l1 = Get_result(data_design = data_design)#分析GGT变量的中介效应
Tables21[nrow(Tables21) + 1,] <- c(mediator,l1)#将数据写入Tables21
#UA
treat="Cd";mediator="UA"; status="CVDmortstat";l1 = Get_result(data_design = data_design)#分析UA变量的中介效应
Tables21[nrow(Tables21) + 1,] <- c(mediator,l1)#将数据写入Tables21
#HDL
treat="Cd";mediator="HDL"; status="CVDmortstat";l1 = Get_result(data_design = data_design)#分析HDL变量的中介效应
Tables21[nrow(Tables21) + 1,] <- c(mediator,l1)#将数据写入Tables21
#UHR
treat="Cd";mediator="UHR"; status="CVDmortstat";l1 = Get_result(data_design = data_design)#分析UHR变量的中介效应
Tables21[nrow(Tables21) + 1,] <- c(mediator,l1)#将数据写入Tables21
#NLR
treat="Cd";mediator="NLR"; status="CVDmortstat";l1 = Get_result(data_design = data_design)#分析NLR变量的中介效应
Tables21[nrow(Tables21) + 1,] <- c(mediator,l1)#将数据写入Tables21
#MLR
treat="Cd";mediator="MLR"; status="CVDmortstat";l1 = Get_result(data_design = data_design)#分析MLR变量的中介效应
Tables21[nrow(Tables21) + 1,] <- c(mediator,l1)#将数据写入Tables21
#NMLR
treat="Cd";mediator="NMLR"; status="CVDmortstat";l1 = Get_result(data_design = data_design)#分析NMLR变量的中介效应
Tables21[nrow(Tables21) + 1,] <- c(mediator,l1)#将数据写入Tables21
#SIRI
treat="Cd";mediator="SIRI"; status="CVDmortstat";l1 = Get_result(data_design = data_design)#分析SIRI变量的中介效应
Tables21[nrow(Tables21) + 1,] <- c(mediator,l1)#将数据写入Tables21
#SII
treat="Cd";mediator="SII"; status="CVDmortstat";l1 = Get_result(data_design = data_design)#分析SII变量的中介效应
Tables21[nrow(Tables21) + 1,] <- c(mediator,l1)#将数据写入Tables21


#结局为CANSER死亡率
Tables21[nrow(Tables21) + 1,] <- c("CVD mortality","NA","NA","NA","NA","NA","NA")

#GGT
treat="Cd";mediator="GGT"; status="CANSERmortstat";l1 = Get_result(data_design = data_design)#分析GGT变量的中介效应
Tables21[nrow(Tables21) + 1,] <- c(mediator,l1)#将数据写入Tables21
#UA
treat="Cd";mediator="UA"; status="CANSERmortstat";l1 = Get_result(data_design = data_design)#分析UA变量的中介效应
Tables21[nrow(Tables21) + 1,] <- c(mediator,l1)#将数据写入Tables21
#HDL
treat="Cd";mediator="HDL"; status="CANSERmortstat";l1 = Get_result(data_design = data_design)#分析HDL变量的中介效应
Tables21[nrow(Tables21) + 1,] <- c(mediator,l1)#将数据写入Tables21
#UHR
treat="Cd";mediator="UHR"; status="CANSERmortstat";l1 = Get_result(data_design = data_design)#分析UHR变量的中介效应
Tables21[nrow(Tables21) + 1,] <- c(mediator,l1)#将数据写入Tables21
#NLR
treat="Cd";mediator="NLR"; status="CANSERmortstat";l1 = Get_result(data_design = data_design)#分析NLR变量的中介效应
Tables21[nrow(Tables21) + 1,] <- c(mediator,l1)#将数据写入Tables21
#MLR
treat="Cd";mediator="MLR"; status="CANSERmortstat";l1 = Get_result(data_design = data_design)#分析MLR变量的中介效应
Tables21[nrow(Tables21) + 1,] <- c(mediator,l1)#将数据写入Tables21
#NMLR
treat="Cd";mediator="NMLR"; status="CANSERmortstat";l1 = Get_result(data_design = data_design)#分析NMLR变量的中介效应
Tables21[nrow(Tables21) + 1,] <- c(mediator,l1)#将数据写入Tables21
#SIRI
treat="Cd";mediator="SIRI"; status="CANSERmortstat";l1 = Get_result(data_design = data_design)#分析SIRI变量的中介效应
Tables21[nrow(Tables21) + 1,] <- c(mediator,l1)#将数据写入Tables21
#SII
treat="Cd";mediator="SII"; status="CANSERmortstat";l1 = Get_result(data_design = data_design)#分析SII变量的中介效应
Tables21[nrow(Tables21) + 1,] <- c(mediator,l1)#将数据写入Tables21


write.csv(Tables21, file = "Tables21.csv")









#生成表22，职业纳入协变量
Tables22 = data.frame(
  x1 = c("NA"),
  x2 = c("Blood Cadmium Levels, HR (95% CI)"),
  x3 = c("NA"),
  x4 = c("NA"),
  x5 = c("NA"),
  x6 = c("P for trend"),
  x7 = c("Per ln-unit increase")
)
Tables22[nrow(Tables22) + 1,] <- c("NA","Blood Cadmium Levels, HR (95% CI)", "NA", "NA", "NA", "P for trend", "Per ln-unit increase")
Tables22[nrow(Tables22) + 1,] <- c("NA","Quantile 1","Quantile 2","Quantile 3","Quantile 4", "NA", "NA")
Q1 = data.frame(summary(data$Cd_4))["Quantile1",]
Q2 = data.frame(summary(data$Cd_4))["Quantile2",]
Q3 = data.frame(summary(data$Cd_4))["Quantile3",]
Q4 = data.frame(summary(data$Cd_4))["Quantile4",]


library(rms)
library(survey)
load("data.RData")
data  = droplevels(data)

#将Cd进行对数转换,为了不出现赋值，加1后再转换
data$Cd_log = rescale(log(data$Cd + 1),to=c(0,2))
data$Sex = factor(data$Sex, levels=c(1,2), labels=c("Male", "Female"))
data$Race = factor(data$Race, levels=c(1,2,3,4,5), labels=c("Non-Hispanic White","Non-Hispanic Black","Mexican American", "Other Hispanic","Other Race - Including Multi-Racial"))
data$Cd_level = as.numeric(data$Cd_4)

data_design <- svydesign(ids=~SDMVPSU,   #集群，抽样单元PSU
                         strata=~SDMVSTRA,   #分层
                         weights=~WTMEC,
                         nest=TRUE,
                         survey.lonely.psu="adjust",  #抽样单元为1时不报错
                         data=data)

cor_all = "+ OCQ260 + Age + Sex + Race + Educationlevel +
                 PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus +
                 Alcoholstatus+ Failingkidneys + Hypertension"
cor_part = "+ OCQ260  + Age + Sex + Race"
#计算HR，CI，P trend的函数
Cox_analysise = function(treat, treat_level, treat_con, status, data_design, cor){
  #treat分类变量，treat_level按1-n的连续变量，treat_con按实际值的连续变量
  #cor调整变量
  f1 = as.formula(paste0("Surv(permth_int, ", status, ")", "~", treat, cor))#做分类变量
  fit1 = svycoxph(f1, design = data_design)
  CI = round(data.frame(summary(fit1)$conf.int), digit=3)#提取HR和置信区间
  
  f2 = as.formula(paste0("Surv(permth_int, ", status, ")", "~", treat_level, cor))#计算p trend
  fit2 = svycoxph(f2, design = data_design)
  P = format.pval(data.frame(summary(fit2)$coefficients)[treat_level, "Pr...z.."],digit=3,eps=0.001)
  
  f3 = as.formula(paste0("Surv(permth_int, ", status, ")", "~", treat_con, cor))#做连续变量
  fit3 = svycoxph(f3, design = data_design)
  CI3 = round(data.frame(summary(fit3)$conf.int), digit=3)#提取HR和置信区间
  
  y = c("1.00 (Ref.) ",
        paste0(CI["Cd_4Quantile2", "exp.coef."], "(", CI["Cd_4Quantile2", "lower..95"],",", CI["Cd_4Quantile2", "upper..95"],")"),
        paste0(CI["Cd_4Quantile3", "exp.coef."], "(", CI["Cd_4Quantile3", "lower..95"],",", CI["Cd_4Quantile3", "upper..95"],")"),
        paste0(CI["Cd_4Quantile4", "exp.coef."], "(", CI["Cd_4Quantile4", "lower..95"],",", CI["Cd_4Quantile4", "upper..95"],")"),
        P,
        paste0(CI3[treat_con, "exp.coef."], "(", CI3[treat_con, "lower..95"],",",CI3[treat_con, "upper..95"],")")
  )
  
  return(y)
}


#全因死亡率
All_Q1 = sum(data$Cd_4 == "Quantile1" & data$Allcausemortstat == 1)
All_Q2 = sum(data$Cd_4 == "Quantile2" & data$Allcausemortstat == 1)
All_Q3 = sum(data$Cd_4 == "Quantile3" & data$Allcausemortstat == 1)
All_Q4 = sum(data$Cd_4 == "Quantile4" & data$Allcausemortstat == 1)
Tables22[nrow(Tables22) + 1,] <- c("All-cause mortality, No",paste0(All_Q1,"/",Q1),paste0(All_Q2,"/",Q2),paste0(All_Q3,"/",Q3),paste0(All_Q4,"/",Q4), "NA", "NA")
line = Cox_analysise("Cd_4", "Cd_level", "Cd", "Allcausemortstat",data_design,cor_part)#全因死亡率，部分调整
Tables22[nrow(Tables22) + 1,] <-c("Partially adjusted", line)#将数据写入表22中
line = Cox_analysise("Cd_4", "Cd_level", "Cd", "Allcausemortstat",data_design,cor_all)#全因死亡率，全部调整
Tables22[nrow(Tables22) + 1,] <-c("Fully adjusted", line)#将数据写入表22中

#CVD死亡率
CVD_Q1 = sum(data$Cd_4 == "Quantile1" & data$CVDmortstat == 1)
CVD_Q2 = sum(data$Cd_4 == "Quantile2" & data$CVDmortstat == 1)
CVD_Q3 = sum(data$Cd_4 == "Quantile3" & data$CVDmortstat == 1)
CVD_Q4 = sum(data$Cd_4 == "Quantile4" & data$CVDmortstat == 1)
Tables22[nrow(Tables22) + 1,] <- c("CVD mortality, No",paste0(CVD_Q1,"/",Q1),paste0(CVD_Q2,"/",Q2),paste0(CVD_Q3,"/",Q3),paste0(CVD_Q4,"/",Q4), "NA", "NA")
line = Cox_analysise("Cd_4", "Cd_level", "Cd", "CVDmortstat",data_design,cor_part)#CVD死亡率，部分调整
Tables22[nrow(Tables22) + 1,] <-c("Partially adjusted", line)#将数据写入表22中
line = Cox_analysise("Cd_4", "Cd_level", "Cd", "CVDmortstat",data_design,cor_all)#CVD死亡率，全部调整
Tables22[nrow(Tables22) + 1,] <-c("Fully adjusted", line)#将数据写入表22中

#CANSER死亡率
CANSER_Q1 = sum(data$Cd_4 == "Quantile1" & data$CANSERmortstat == 1)
CANSER_Q2 = sum(data$Cd_4 == "Quantile2" & data$CANSERmortstat == 1)
CANSER_Q3 = sum(data$Cd_4 == "Quantile3" & data$CANSERmortstat == 1)
CANSER_Q4 = sum(data$Cd_4 == "Quantile4" & data$CANSERmortstat == 1)
Tables22[nrow(Tables22) + 1,] <- c("CANSER mortality, No",paste0(CANSER_Q1,"/",Q1),paste0(CANSER_Q2,"/",Q2),paste0(CANSER_Q3,"/",Q3),paste0(CANSER_Q4,"/",Q4), "NA", "NA")
line = Cox_analysise("Cd_4", "Cd_level", "Cd", "CANSERmortstat",data_design,cor_part)#CANSER死亡率，部分调整
Tables22[nrow(Tables22) + 1,] <-c("Partially adjusted", line)#将数据写入表22中
line = Cox_analysise("Cd_4", "Cd_level", "Cd", "CANSERmortstat",data_design,cor_all)#CANSER死亡率，全部调整
Tables22[nrow(Tables22) + 1,] <-c("Fully adjusted", line)#将数据写入表22中

write.csv(Tables22, file = "Tables22.csv")





#职业纳入协变量重做表s5，生成表s23


data_design <- svydesign(ids=~SDMVPSU,   #集群，抽样单元PSU
                         strata=~SDMVSTRA,   #分层
                         weights=~WTMEC,
                         nest=TRUE,
                         survey.lonely.psu="adjust",  #抽样单元为1时不报错
                         data=data)
dd<-datadist(data)
options(datadist='dd')

cor = "+ OCQ260 + Age + Sex + Race + Educationlevel +
                 PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus +
                 Alcoholstatus+ Failingkidneys + Hypertension"
options(survey.lonely.psu = "adjust")

#定义计算中介效应的函数
Median_alalysiae = function(data,indices){
  
  d <- data[indices, ]  # 重抽样数据
  d_design= svydesign(ids=~SDMVPSU,   #集群，抽样单元PSU
                      strata=~SDMVSTRA,   #分层
                      weights=~WTMEC,
                      nest=TRUE,
                      survey.lonely.psu="adjust",  #抽样单元为1时不报错
                      data=d)
  
  cor = "+ OCQ260 + Age + Sex + Race + Educationlevel +
                 PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus +
                 Alcoholstatus+ Failingkidneys + Hypertension"
  f1 = as.formula(paste0("Surv(permth_int, ", status, ")", "~", treat, cor))
  f2 = as.formula(paste0(mediator, "~", treat, cor))
  f3 = as.formula(paste0("Surv(permth_int, ", status, ")", "~", treat, "+", mediator, cor))
  
  fit_svy<- svycoxph(f1, design=d_design)
  #weight变量是权重,1总效应模型（X→结局）,Total Effect
  model_total<- coxph(f1, x=TRUE, y=TRUE,data=d, weights = (1/fit_svy$survey.design$prob)/mean(1/fit_svy$survey.design$prob))
  #提取HR和置信区间
  #total_result = ShowRegTable(model_total, exp = FALSE,digits = 3,pDigits = 3,printToggle=FALSE,quote = FALSE, ciFun = confint)
  beta_total <- coef(model_total)[treat]  # 总效应系数
  
  # 2：X→M的关系
  # 用线性回归
  model_XM <- lm(f2, data = d)
  #提取HR和置信区间
  #XM_result = ShowRegTable(model_XM, exp = FALSE,digits = 3,pDigits = 3,printToggle=FALSE,quote = FALSE, ciFun = confint)
  a <- coef(model_XM)[treat]  # X→M的效应
  
  # 3：（X和M共同预测结局）ADE（Average Direct Effects）
  model_direct <- coxph(f3, data = d,weights = (1/fit_svy$survey.design$prob)/mean(1/fit_svy$survey.design$prob))
  #提取HR和置信区间
  #direct_result = ShowRegTable(model_direct, exp = FALSE,digits = 3,pDigits = 3,printToggle=FALSE,quote = FALSE, ciFun = confint)
  beta_direct <- coef(model_direct)[treat]  # 直接效应系数
  b <- coef(model_direct)[mediator]  # M→结局的效应
  
  # 4. 计算效应量 ACME
  indirect_effect <- a * b  # 中介效应 (乘积法)
  proportion_mediated <- indirect_effect / beta_total  # 中介效应占比,PE
  return(c(beta_total, beta_direct, indirect_effect, proportion_mediated))
}


#得到最终的ACME，ADE，PE等和置信区间
Get_result = function(R = 10, data_design=data_design){
  boot_results <- boot(data = data, statistic = Median_alalysiae, R = R)#进行Bootstrap，1000次计算速度太慢了,次数越多，计算的P值越准确
  # 计算置信区间 (95%)
  ci_total <- boot.ci(boot_results, type = "norm", index = 1)  # 总效应CI,TE
  ci_direct <- boot.ci(boot_results, type = "norm", index = 2)  # 直接效应CI,ADE
  ci_indirect <- boot.ci(boot_results, type = "norm", index = 3)  # 中介效应CI,ACME
  ci_pm <- boot.ci(boot_results, type = "norm", index = 4)  # 中介效应占比CI,PE
  print(paste0("分析", treat, "对", status, "的中介效应，中介变量为", mediator))
  
  # 计算观测统计量
  obs_stat  = Median_alalysiae(data,seq(from=1, to=nrow(data)))
  
  # 计算Bootstrap P值（双侧检验）
  #计算ADE值
  cor = "+ OCQ260 + Age + Sex + Race + Educationlevel +
                 PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus +
                 Alcoholstatus+ Failingkidneys + Hypertension"
  f3 = as.formula(paste0("Surv(permth_int, ", status, ")", "~", treat, "+", mediator, cor))
  fit_svy<- svycoxph(f3, design=data_design)
  model_direct <- coxph(f3, data = data,weights = (1/fit_svy$survey.design$prob)/mean(1/fit_svy$survey.design$prob))
  #提取HR和置信区间
  p_direct = data.frame(ShowRegTable(model_direct, exp = FALSE,digits = 3,pDigits = 3,printToggle=FALSE,quote = FALSE, ciFun = confint))["Cd", "p"]
  
  #p_total = 1-boot.pval(boot_results,theta_null = obs_stat[1], alternative = "two.sided", index=1,pval_precision=0.00001)
  #p_direct = 1-boot.pval(boot_results,theta_null = obs_stat[2], alternative = "two.sided", index=2,pval_precision=0.00001)
  p_indirect = 1-boot.pval(boot_results,theta_null = obs_stat[3], alternative = "two.sided", index=3,pval_precision=0.001)
  p_pm = 1-boot.pval(boot_results,theta_null = obs_stat[4], alternative = "two.sided", index=4,pval_precision=0.001)
  
  y = c(paste0(round(boot_results$t0[3],digit=3), "(",round(ci_indirect$normal[2],digit=3), ",", round(ci_indirect$normal[3],digit=3), ")"),p_indirect,
        paste0(round(boot_results$t0[2],digit=3), "(",round(ci_direct$normal[2],digit=3), ",", round(ci_direct$normal[3],digit=3), ")"), p_direct,
        paste0(round(boot_results$t0[4],digit=3), "(",round(ci_pm$normal[2],digit=3), ",", round(ci_pm$normal[3],digit=3), ")"), p_pm)
  
  return(y)
}

#生成表s23
Tables23 = data.frame(
  x1 = c("NA"),
  x2 = c("ACME"),
  x3 = c("ACME"),
  x4 = c("ADE"),
  x5 = c("ADE"),  
  x6 = c("PE"),
  x7 = c("PE")
)

#将数据写入tableS5
Tables23[nrow(Tables23) + 1,] <- c("NA","Estimate (95% CI)","P value","Estimate (95% CI)","P value","Estimate (95% CI)","P value")

#结局为全因死亡率
Tables23[nrow(Tables23) + 1,] <- c("All-cause mortality","NA","NA","NA","NA","NA","NA")

#GGT
treat="Cd";mediator="GGT"; status="Allcausemortstat";l1 = Get_result(data_design = data_design)#分析GGT变量的中介效应
Tables23[nrow(Tables23) + 1,] <- c(mediator,l1)#将数据写入Tables23
#UA
treat="Cd";mediator="UA"; status="Allcausemortstat";l1 = Get_result(data_design = data_design)#分析UA变量的中介效应
Tables23[nrow(Tables23) + 1,] <- c(mediator,l1)#将数据写入Tables23
#HDL
treat="Cd";mediator="HDL"; status="Allcausemortstat";l1 = Get_result(data_design = data_design)#分析HDL变量的中介效应
Tables23[nrow(Tables23) + 1,] <- c(mediator,l1)#将数据写入Tables23
#UHR
treat="Cd";mediator="UHR"; status="Allcausemortstat";l1 = Get_result(data_design = data_design)#分析UHR变量的中介效应
Tables23[nrow(Tables23) + 1,] <- c(mediator,l1)#将数据写入Tables23
#NLR
treat="Cd";mediator="NLR"; status="Allcausemortstat";l1 = Get_result(data_design = data_design)#分析NLR变量的中介效应
Tables23[nrow(Tables23) + 1,] <- c(mediator,l1)#将数据写入Tables23
#MLR
treat="Cd";mediator="MLR"; status="Allcausemortstat";l1 = Get_result(data_design = data_design)#分析MLR变量的中介效应
Tables23[nrow(Tables23) + 1,] <- c(mediator,l1)#将数据写入Tables23
#NMLR
treat="Cd";mediator="NMLR"; status="Allcausemortstat";l1 = Get_result(data_design = data_design)#分析NMLR变量的中介效应
Tables23[nrow(Tables23) + 1,] <- c(mediator,l1)#将数据写入Tables23
#SIRI
treat="Cd";mediator="SIRI"; status="Allcausemortstat";l1 = Get_result(data_design = data_design)#分析SIRI变量的中介效应
Tables23[nrow(Tables23) + 1,] <- c(mediator,l1)#将数据写入Tables23
#SII
treat="Cd";mediator="SII"; status="Allcausemortstat";l1 = Get_result(data_design = data_design)#分析SII变量的中介效应
Tables23[nrow(Tables23) + 1,] <- c(mediator,l1)#将数据写入Tables23

#结局为CVD死亡率
Tables23[nrow(Tables23) + 1,] <- c("CVD mortality","NA","NA","NA","NA","NA","NA")

#GGT
treat="Cd";mediator="GGT"; status="CVDmortstat";l1 = Get_result(data_design = data_design)#分析GGT变量的中介效应
Tables23[nrow(Tables23) + 1,] <- c(mediator,l1)#将数据写入Tables23
#UA
treat="Cd";mediator="UA"; status="CVDmortstat";l1 = Get_result(data_design = data_design)#分析UA变量的中介效应
Tables23[nrow(Tables23) + 1,] <- c(mediator,l1)#将数据写入Tables23
#HDL
treat="Cd";mediator="HDL"; status="CVDmortstat";l1 = Get_result(data_design = data_design)#分析HDL变量的中介效应
Tables23[nrow(Tables23) + 1,] <- c(mediator,l1)#将数据写入Tables23
#UHR
treat="Cd";mediator="UHR"; status="CVDmortstat";l1 = Get_result(data_design = data_design)#分析UHR变量的中介效应
Tables23[nrow(Tables23) + 1,] <- c(mediator,l1)#将数据写入Tables23
#NLR
treat="Cd";mediator="NLR"; status="CVDmortstat";l1 = Get_result(data_design = data_design)#分析NLR变量的中介效应
Tables23[nrow(Tables23) + 1,] <- c(mediator,l1)#将数据写入Tables23
#MLR
treat="Cd";mediator="MLR"; status="CVDmortstat";l1 = Get_result(data_design = data_design)#分析MLR变量的中介效应
Tables23[nrow(Tables23) + 1,] <- c(mediator,l1)#将数据写入Tables23
#NMLR
treat="Cd";mediator="NMLR"; status="CVDmortstat";l1 = Get_result(data_design = data_design)#分析NMLR变量的中介效应
Tables23[nrow(Tables23) + 1,] <- c(mediator,l1)#将数据写入Tables23
#SIRI
treat="Cd";mediator="SIRI"; status="CVDmortstat";l1 = Get_result(data_design = data_design)#分析SIRI变量的中介效应
Tables23[nrow(Tables23) + 1,] <- c(mediator,l1)#将数据写入Tables23
#SII
treat="Cd";mediator="SII"; status="CVDmortstat";l1 = Get_result(data_design = data_design)#分析SII变量的中介效应
Tables23[nrow(Tables23) + 1,] <- c(mediator,l1)#将数据写入Tables23


#结局为CANSER死亡率
Tables23[nrow(Tables23) + 1,] <- c("CVD mortality","NA","NA","NA","NA","NA","NA")

#GGT
treat="Cd";mediator="GGT"; status="CANSERmortstat";l1 = Get_result(data_design = data_design)#分析GGT变量的中介效应
Tables23[nrow(Tables23) + 1,] <- c(mediator,l1)#将数据写入Tables23
#UA
treat="Cd";mediator="UA"; status="CANSERmortstat";l1 = Get_result(data_design = data_design)#分析UA变量的中介效应
Tables23[nrow(Tables23) + 1,] <- c(mediator,l1)#将数据写入Tables23
#HDL
treat="Cd";mediator="HDL"; status="CANSERmortstat";l1 = Get_result(data_design = data_design)#分析HDL变量的中介效应
Tables23[nrow(Tables23) + 1,] <- c(mediator,l1)#将数据写入Tables23
#UHR
treat="Cd";mediator="UHR"; status="CANSERmortstat";l1 = Get_result(data_design = data_design)#分析UHR变量的中介效应
Tables23[nrow(Tables23) + 1,] <- c(mediator,l1)#将数据写入Tables23
#NLR
treat="Cd";mediator="NLR"; status="CANSERmortstat";l1 = Get_result(data_design = data_design)#分析NLR变量的中介效应
Tables23[nrow(Tables23) + 1,] <- c(mediator,l1)#将数据写入Tables23
#MLR
treat="Cd";mediator="MLR"; status="CANSERmortstat";l1 = Get_result(data_design = data_design)#分析MLR变量的中介效应
Tables23[nrow(Tables23) + 1,] <- c(mediator,l1)#将数据写入Tables23
#NMLR
treat="Cd";mediator="NMLR"; status="CANSERmortstat";l1 = Get_result(data_design = data_design)#分析NMLR变量的中介效应
Tables23[nrow(Tables23) + 1,] <- c(mediator,l1)#将数据写入Tables23
#SIRI
treat="Cd";mediator="SIRI"; status="CANSERmortstat";l1 = Get_result(data_design = data_design)#分析SIRI变量的中介效应
Tables23[nrow(Tables23) + 1,] <- c(mediator,l1)#将数据写入Tables23
#SII
treat="Cd";mediator="SII"; status="CANSERmortstat";l1 = Get_result(data_design = data_design)#分析SII变量的中介效应
Tables23[nrow(Tables23) + 1,] <- c(mediator,l1)#将数据写入Tables23


write.csv(Tables23, file = "Tables23.csv")






#生成表24，协变量去掉高血压和肾衰竭
Tables24 = data.frame(
  x1 = c("NA"),
  x2 = c("Blood Cadmium Levels, HR (95% CI)"),
  x3 = c("NA"),
  x4 = c("NA"),
  x5 = c("NA"),
  x6 = c("P for trend"),
  x7 = c("Per ln-unit increase")
)
Tables24[nrow(Tables24) + 1,] <- c("NA","Blood Cadmium Levels, HR (95% CI)", "NA", "NA", "NA", "P for trend", "Per ln-unit increase")
Tables24[nrow(Tables24) + 1,] <- c("NA","Quantile 1","Quantile 2","Quantile 3","Quantile 4", "NA", "NA")
Q1 = data.frame(summary(data$Cd_4))["Quantile1",]
Q2 = data.frame(summary(data$Cd_4))["Quantile2",]
Q3 = data.frame(summary(data$Cd_4))["Quantile3",]
Q4 = data.frame(summary(data$Cd_4))["Quantile4",]

load("data.RData")
library(rms)
library(survey)
data  = droplevels(data)

#将Cd进行对数转换,为了不出现赋值，加1后再转换
data$Cd_log = rescale(log(data$Cd + 1),to=c(0,2))
data$Sex = factor(data$Sex, levels=c(1,2), labels=c("Male", "Female"))
data$Race = factor(data$Race, levels=c(1,2,3,4,5), labels=c("Non-Hispanic White","Non-Hispanic Black","Mexican American", "Other Hispanic","Other Race - Including Multi-Racial"))
data$Cd_level = as.numeric(data$Cd_4)

data_design <- svydesign(ids=~SDMVPSU,   #集群，抽样单元PSU
                         strata=~SDMVSTRA,   #分层
                         weights=~WTMEC,
                         nest=TRUE,
                         survey.lonely.psu="adjust",  #抽样单元为1时不报错
                         data=data)

cor_all = " + Age + Sex + Race + Educationlevel +
                 PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus +
                 Alcoholstatus"
cor_part = " + Age + Sex + Race"
#计算HR，CI，P trend的函数
Cox_analysise = function(treat, treat_level, treat_con, status, data_design, cor){
  #treat分类变量，treat_level按1-n的连续变量，treat_con按实际值的连续变量
  #cor调整变量
  f1 = as.formula(paste0("Surv(permth_int, ", status, ")", "~", treat, cor))#做分类变量
  fit1 = svycoxph(f1, design = data_design)
  CI = round(data.frame(summary(fit1)$conf.int), digit=3)#提取HR和置信区间
  
  f2 = as.formula(paste0("Surv(permth_int, ", status, ")", "~", treat_level, cor))#计算p trend
  fit2 = svycoxph(f2, design = data_design)
  P = format.pval(data.frame(summary(fit2)$coefficients)[treat_level, "Pr...z.."],digit=3,eps=0.001)
  
  f3 = as.formula(paste0("Surv(permth_int, ", status, ")", "~", treat_con, cor))#做连续变量
  fit3 = svycoxph(f3, design = data_design)
  CI3 = round(data.frame(summary(fit3)$conf.int), digit=3)#提取HR和置信区间
  
  y = c("1.00 (Ref.) ",
        paste0(CI["Cd_4Quantile2", "exp.coef."], "(", CI["Cd_4Quantile2", "lower..95"],",", CI["Cd_4Quantile2", "upper..95"],")"),
        paste0(CI["Cd_4Quantile3", "exp.coef."], "(", CI["Cd_4Quantile3", "lower..95"],",", CI["Cd_4Quantile3", "upper..95"],")"),
        paste0(CI["Cd_4Quantile4", "exp.coef."], "(", CI["Cd_4Quantile4", "lower..95"],",", CI["Cd_4Quantile4", "upper..95"],")"),
        P,
        paste0(CI3[treat_con, "exp.coef."], "(", CI3[treat_con, "lower..95"],",",CI3[treat_con, "upper..95"],")")
  )
  
  return(y)
}


#全因死亡率
All_Q1 = sum(data$Cd_4 == "Quantile1" & data$Allcausemortstat == 1)
All_Q2 = sum(data$Cd_4 == "Quantile2" & data$Allcausemortstat == 1)
All_Q3 = sum(data$Cd_4 == "Quantile3" & data$Allcausemortstat == 1)
All_Q4 = sum(data$Cd_4 == "Quantile4" & data$Allcausemortstat == 1)
Tables24[nrow(Tables24) + 1,] <- c("All-cause mortality, No",paste0(All_Q1,"/",Q1),paste0(All_Q2,"/",Q2),paste0(All_Q3,"/",Q3),paste0(All_Q4,"/",Q4), "NA", "NA")
line = Cox_analysise("Cd_4", "Cd_level", "Cd", "Allcausemortstat",data_design,cor_part)#全因死亡率，部分调整
Tables24[nrow(Tables24) + 1,] <-c("Partially adjusted", line)#将数据写入表24中
line = Cox_analysise("Cd_4", "Cd_level", "Cd", "Allcausemortstat",data_design,cor_all)#全因死亡率，全部调整
Tables24[nrow(Tables24) + 1,] <-c("Fully adjusted", line)#将数据写入表24中

#CVD死亡率
CVD_Q1 = sum(data$Cd_4 == "Quantile1" & data$CVDmortstat == 1)
CVD_Q2 = sum(data$Cd_4 == "Quantile2" & data$CVDmortstat == 1)
CVD_Q3 = sum(data$Cd_4 == "Quantile3" & data$CVDmortstat == 1)
CVD_Q4 = sum(data$Cd_4 == "Quantile4" & data$CVDmortstat == 1)
Tables24[nrow(Tables24) + 1,] <- c("CVD mortality, No",paste0(CVD_Q1,"/",Q1),paste0(CVD_Q2,"/",Q2),paste0(CVD_Q3,"/",Q3),paste0(CVD_Q4,"/",Q4), "NA", "NA")
line = Cox_analysise("Cd_4", "Cd_level", "Cd", "CVDmortstat",data_design,cor_part)#CVD死亡率，部分调整
Tables24[nrow(Tables24) + 1,] <-c("Partially adjusted", line)#将数据写入表24中
line = Cox_analysise("Cd_4", "Cd_level", "Cd", "CVDmortstat",data_design,cor_all)#CVD死亡率，全部调整
Tables24[nrow(Tables24) + 1,] <-c("Fully adjusted", line)#将数据写入表24中

#CANSER死亡率
CANSER_Q1 = sum(data$Cd_4 == "Quantile1" & data$CANSERmortstat == 1)
CANSER_Q2 = sum(data$Cd_4 == "Quantile2" & data$CANSERmortstat == 1)
CANSER_Q3 = sum(data$Cd_4 == "Quantile3" & data$CANSERmortstat == 1)
CANSER_Q4 = sum(data$Cd_4 == "Quantile4" & data$CANSERmortstat == 1)
Tables24[nrow(Tables24) + 1,] <- c("CANSER mortality, No",paste0(CANSER_Q1,"/",Q1),paste0(CANSER_Q2,"/",Q2),paste0(CANSER_Q3,"/",Q3),paste0(CANSER_Q4,"/",Q4), "NA", "NA")
line = Cox_analysise("Cd_4", "Cd_level", "Cd", "CANSERmortstat",data_design,cor_part)#CANSER死亡率，部分调整
Tables24[nrow(Tables24) + 1,] <-c("Partially adjusted", line)#将数据写入表24中
line = Cox_analysise("Cd_4", "Cd_level", "Cd", "CANSERmortstat",data_design,cor_all)#CANSER死亡率，全部调整
Tables24[nrow(Tables24) + 1,] <-c("Fully adjusted", line)#将数据写入表24中

write.csv(Tables24, file = "Tables24.csv")





#协变量去掉高血压和肾衰竭重做表s5，生成表s25


data_design <- svydesign(ids=~SDMVPSU,   #集群，抽样单元PSU
                         strata=~SDMVSTRA,   #分层
                         weights=~WTMEC,
                         nest=TRUE,
                         survey.lonely.psu="adjust",  #抽样单元为1时不报错
                         data=data)
dd<-datadist(data)
options(datadist='dd')

cor = "+ Cd_log + Age + Sex + Race + Educationlevel +
                 PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus +
                 Alcoholstatus+ Failingkidneys + Hypertension"
options(survey.lonely.psu = "adjust")

#定义计算中介效应的函数
Median_alalysiae = function(data,indices){
  
  d <- data[indices, ]  # 重抽样数据
  d_design= svydesign(ids=~SDMVPSU,   #集群，抽样单元PSU
                      strata=~SDMVSTRA,   #分层
                      weights=~WTMEC,
                      nest=TRUE,
                      survey.lonely.psu="adjust",  #抽样单元为1时不报错
                      data=d)
  
  cor = "+ Age + Sex + Race + Educationlevel +
                 PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus +
                 Alcoholstatus"
  f1 = as.formula(paste0("Surv(permth_int, ", status, ")", "~", treat, cor))
  f2 = as.formula(paste0(mediator, "~", treat, cor))
  f3 = as.formula(paste0("Surv(permth_int, ", status, ")", "~", treat, "+", mediator, cor))
  
  fit_svy<- svycoxph(f1, design=d_design)
  #weight变量是权重,1总效应模型（X→结局）,Total Effect
  model_total<- coxph(f1, x=TRUE, y=TRUE,data=d, weights = (1/fit_svy$survey.design$prob)/mean(1/fit_svy$survey.design$prob))
  #提取HR和置信区间
  #total_result = ShowRegTable(model_total, exp = FALSE,digits = 3,pDigits = 3,printToggle=FALSE,quote = FALSE, ciFun = confint)
  beta_total <- coef(model_total)[treat]  # 总效应系数
  
  # 2：X→M的关系
  # 用线性回归
  model_XM <- lm(f2, data = d)
  #提取HR和置信区间
  #XM_result = ShowRegTable(model_XM, exp = FALSE,digits = 3,pDigits = 3,printToggle=FALSE,quote = FALSE, ciFun = confint)
  a <- coef(model_XM)[treat]  # X→M的效应
  
  # 3：（X和M共同预测结局）ADE（Average Direct Effects）
  model_direct <- coxph(f3, data = d,weights = (1/fit_svy$survey.design$prob)/mean(1/fit_svy$survey.design$prob))
  #提取HR和置信区间
  #direct_result = ShowRegTable(model_direct, exp = FALSE,digits = 3,pDigits = 3,printToggle=FALSE,quote = FALSE, ciFun = confint)
  beta_direct <- coef(model_direct)[treat]  # 直接效应系数
  b <- coef(model_direct)[mediator]  # M→结局的效应
  
  # 4. 计算效应量 ACME
  indirect_effect <- a * b  # 中介效应 (乘积法)
  proportion_mediated <- indirect_effect / beta_total  # 中介效应占比,PE
  return(c(beta_total, beta_direct, indirect_effect, proportion_mediated))
}


#得到最终的ACME，ADE，PE等和置信区间
Get_result = function(R = 10, data_design=data_design){
  boot_results <- boot(data = data, statistic = Median_alalysiae, R = R)#进行Bootstrap，1000次计算速度太慢了,次数越多，计算的P值越准确
  # 计算置信区间 (95%)
  ci_total <- boot.ci(boot_results, type = "norm", index = 1)  # 总效应CI,TE
  ci_direct <- boot.ci(boot_results, type = "norm", index = 2)  # 直接效应CI,ADE
  ci_indirect <- boot.ci(boot_results, type = "norm", index = 3)  # 中介效应CI,ACME
  ci_pm <- boot.ci(boot_results, type = "norm", index = 4)  # 中介效应占比CI,PE
  print(paste0("分析", treat, "对", status, "的中介效应，中介变量为", mediator))
  
  # 计算观测统计量
  obs_stat  = Median_alalysiae(data,seq(from=1, to=nrow(data)))
  
  # 计算Bootstrap P值（双侧检验）
  #计算ADE值
  cor = "+ Age + Sex + Race + Educationlevel +
                 PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus +
                 Alcoholstatus"
  f3 = as.formula(paste0("Surv(permth_int, ", status, ")", "~", treat, "+", mediator, cor))
  fit_svy<- svycoxph(f3, design=data_design)
  model_direct <- coxph(f3, data = data,weights = (1/fit_svy$survey.design$prob)/mean(1/fit_svy$survey.design$prob))
  #提取HR和置信区间
  p_direct = data.frame(ShowRegTable(model_direct, exp = FALSE,digits = 3,pDigits = 3,printToggle=FALSE,quote = FALSE, ciFun = confint))["Cd", "p"]
  
  #p_total = 1-boot.pval(boot_results,theta_null = obs_stat[1], alternative = "two.sided", index=1,pval_precision=0.00001)
  #p_direct = 1-boot.pval(boot_results,theta_null = obs_stat[2], alternative = "two.sided", index=2,pval_precision=0.00001)
  p_indirect = 1-boot.pval(boot_results,theta_null = obs_stat[3], alternative = "two.sided", index=3,pval_precision=0.001)
  p_pm = 1-boot.pval(boot_results,theta_null = obs_stat[4], alternative = "two.sided", index=4,pval_precision=0.001)
  
  y = c(paste0(round(boot_results$t0[3],digit=3), "(",round(ci_indirect$normal[2],digit=3), ",", round(ci_indirect$normal[3],digit=3), ")"),p_indirect,
        paste0(round(boot_results$t0[2],digit=3), "(",round(ci_direct$normal[2],digit=3), ",", round(ci_direct$normal[3],digit=3), ")"), p_direct,
        paste0(round(boot_results$t0[4],digit=3), "(",round(ci_pm$normal[2],digit=3), ",", round(ci_pm$normal[3],digit=3), ")"), p_pm)
  
  return(y)
}

#生成表s25
Tables25 = data.frame(
  x1 = c("NA"),
  x2 = c("ACME"),
  x3 = c("ACME"),
  x4 = c("ADE"),
  x5 = c("ADE"),  
  x6 = c("PE"),
  x7 = c("PE")
)

#将数据写入tableS25
Tables25[nrow(Tables25) + 1,] <- c("NA","Estimate (95% CI)","P value","Estimate (95% CI)","P value","Estimate (95% CI)","P value")

#结局为全因死亡率
Tables25[nrow(Tables25) + 1,] <- c("All-cause mortality","NA","NA","NA","NA","NA","NA")

#GGT
treat="Cd";mediator="GGT"; status="Allcausemortstat";l1 = Get_result(data_design = data_design)#分析GGT变量的中介效应
Tables25[nrow(Tables25) + 1,] <- c(mediator,l1)#将数据写入Tables25
#UA
treat="Cd";mediator="UA"; status="Allcausemortstat";l1 = Get_result(data_design = data_design)#分析UA变量的中介效应
Tables25[nrow(Tables25) + 1,] <- c(mediator,l1)#将数据写入Tables25
#HDL
treat="Cd";mediator="HDL"; status="Allcausemortstat";l1 = Get_result(data_design = data_design)#分析HDL变量的中介效应
Tables25[nrow(Tables25) + 1,] <- c(mediator,l1)#将数据写入Tables25
#UHR
treat="Cd";mediator="UHR"; status="Allcausemortstat";l1 = Get_result(data_design = data_design)#分析UHR变量的中介效应
Tables25[nrow(Tables25) + 1,] <- c(mediator,l1)#将数据写入Tables25
#NLR
treat="Cd";mediator="NLR"; status="Allcausemortstat";l1 = Get_result(data_design = data_design)#分析NLR变量的中介效应
Tables25[nrow(Tables25) + 1,] <- c(mediator,l1)#将数据写入Tables25
#MLR
treat="Cd";mediator="MLR"; status="Allcausemortstat";l1 = Get_result(data_design = data_design)#分析MLR变量的中介效应
Tables25[nrow(Tables25) + 1,] <- c(mediator,l1)#将数据写入Tables25
#NMLR
treat="Cd";mediator="NMLR"; status="Allcausemortstat";l1 = Get_result(data_design = data_design)#分析NMLR变量的中介效应
Tables25[nrow(Tables25) + 1,] <- c(mediator,l1)#将数据写入Tables25
#SIRI
treat="Cd";mediator="SIRI"; status="Allcausemortstat";l1 = Get_result(data_design = data_design)#分析SIRI变量的中介效应
Tables25[nrow(Tables25) + 1,] <- c(mediator,l1)#将数据写入Tables25
#SII
treat="Cd";mediator="SII"; status="Allcausemortstat";l1 = Get_result(data_design = data_design)#分析SII变量的中介效应
Tables25[nrow(Tables25) + 1,] <- c(mediator,l1)#将数据写入Tables25

#结局为CVD死亡率
Tables25[nrow(Tables25) + 1,] <- c("CVD mortality","NA","NA","NA","NA","NA","NA")

#GGT
treat="Cd";mediator="GGT"; status="CVDmortstat";l1 = Get_result(data_design = data_design)#分析GGT变量的中介效应
Tables25[nrow(Tables25) + 1,] <- c(mediator,l1)#将数据写入Tables25
#UA
treat="Cd";mediator="UA"; status="CVDmortstat";l1 = Get_result(data_design = data_design)#分析UA变量的中介效应
Tables25[nrow(Tables25) + 1,] <- c(mediator,l1)#将数据写入Tables25
#HDL
treat="Cd";mediator="HDL"; status="CVDmortstat";l1 = Get_result(data_design = data_design)#分析HDL变量的中介效应
Tables25[nrow(Tables25) + 1,] <- c(mediator,l1)#将数据写入Tables25
#UHR
treat="Cd";mediator="UHR"; status="CVDmortstat";l1 = Get_result(data_design = data_design)#分析UHR变量的中介效应
Tables25[nrow(Tables25) + 1,] <- c(mediator,l1)#将数据写入Tables25
#NLR
treat="Cd";mediator="NLR"; status="CVDmortstat";l1 = Get_result(data_design = data_design)#分析NLR变量的中介效应
Tables25[nrow(Tables25) + 1,] <- c(mediator,l1)#将数据写入Tables25
#MLR
treat="Cd";mediator="MLR"; status="CVDmortstat";l1 = Get_result(data_design = data_design)#分析MLR变量的中介效应
Tables25[nrow(Tables25) + 1,] <- c(mediator,l1)#将数据写入Tables25
#NMLR
treat="Cd";mediator="NMLR"; status="CVDmortstat";l1 = Get_result(data_design = data_design)#分析NMLR变量的中介效应
Tables25[nrow(Tables25) + 1,] <- c(mediator,l1)#将数据写入Tables25
#SIRI
treat="Cd";mediator="SIRI"; status="CVDmortstat";l1 = Get_result(data_design = data_design)#分析SIRI变量的中介效应
Tables25[nrow(Tables25) + 1,] <- c(mediator,l1)#将数据写入Tables25
#SII
treat="Cd";mediator="SII"; status="CVDmortstat";l1 = Get_result(data_design = data_design)#分析SII变量的中介效应
Tables25[nrow(Tables25) + 1,] <- c(mediator,l1)#将数据写入Tables25


#结局为CANSER死亡率
Tables25[nrow(Tables25) + 1,] <- c("CVD mortality","NA","NA","NA","NA","NA","NA")

#GGT
treat="Cd";mediator="GGT"; status="CANSERmortstat";l1 = Get_result(data_design = data_design)#分析GGT变量的中介效应
Tables25[nrow(Tables25) + 1,] <- c(mediator,l1)#将数据写入Tables25
#UA
treat="Cd";mediator="UA"; status="CANSERmortstat";l1 = Get_result(data_design = data_design)#分析UA变量的中介效应
Tables25[nrow(Tables25) + 1,] <- c(mediator,l1)#将数据写入Tables25
#HDL
treat="Cd";mediator="HDL"; status="CANSERmortstat";l1 = Get_result(data_design = data_design)#分析HDL变量的中介效应
Tables25[nrow(Tables25) + 1,] <- c(mediator,l1)#将数据写入Tables25
#UHR
treat="Cd";mediator="UHR"; status="CANSERmortstat";l1 = Get_result(data_design = data_design)#分析UHR变量的中介效应
Tables25[nrow(Tables25) + 1,] <- c(mediator,l1)#将数据写入Tables25
#NLR
treat="Cd";mediator="NLR"; status="CANSERmortstat";l1 = Get_result(data_design = data_design)#分析NLR变量的中介效应
Tables25[nrow(Tables25) + 1,] <- c(mediator,l1)#将数据写入Tables25
#MLR
treat="Cd";mediator="MLR"; status="CANSERmortstat";l1 = Get_result(data_design = data_design)#分析MLR变量的中介效应
Tables25[nrow(Tables25) + 1,] <- c(mediator,l1)#将数据写入Tables25
#NMLR
treat="Cd";mediator="NMLR"; status="CANSERmortstat";l1 = Get_result(data_design = data_design)#分析NMLR变量的中介效应
Tables25[nrow(Tables25) + 1,] <- c(mediator,l1)#将数据写入Tables25
#SIRI
treat="Cd";mediator="SIRI"; status="CANSERmortstat";l1 = Get_result(data_design = data_design)#分析SIRI变量的中介效应
Tables25[nrow(Tables25) + 1,] <- c(mediator,l1)#将数据写入Tables25
#SII
treat="Cd";mediator="SII"; status="CANSERmortstat";l1 = Get_result(data_design = data_design)#分析SII变量的中介效应
Tables25[nrow(Tables25) + 1,] <- c(mediator,l1)#将数据写入Tables25


write.csv(Tables25, file = "Tables25.csv")
