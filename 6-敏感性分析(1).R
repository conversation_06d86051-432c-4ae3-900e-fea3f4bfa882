library(cmprsk)
library(survey)
library(jskm)
library (tidyr)
library(survival)

load("data.RData")


#做图S6的A
data$status2 <- data$CVDmortstat # 创建新的 status2 变量，并将其初始值设为 CVDmortstat 变量的值
data$status2 <- ifelse((data$CANSERmortstat == 1), 2, data$status2) # 将CANSERmortstat死亡数据的 status2 值设为2，表示竞争风险
data$status2 <- factor(data$status2) # 将 status2 转换为因子类型
data = data %>% drop_na(permth_int)
data$permth_int = as.numeric(data$permth_int)

data_design <- svydesign(ids=~SDMVPSU,   #集群，抽样单元PSU
                         strata=~SDMVSTRA,   #分层
                         weights=~WTMEC,
                         nest=TRUE,
                         survey.lonely.psu="adjust",  #抽样单元为1时不报错
                         data=data)
dd<-datadist(data)
options(datadist='dd')
#构建带权重的生存模型
fit_svy<- svycoxph(Surv(permth_int, CVDmortstat) ~ Cd_4, design=data_design)
# 使用 survfit 函数拟合生存曲线
fit1 <- survfit(Surv(permth_int, status2) ~ Cd_4, data = data,weights = (1/fit_svy$survey.design$prob)/mean(1/fit_svy$survey.design$prob))


# 竞争风险分析
risk_model= jskm(fit1, 
     mark = F, # 是否在生存曲线上标记审查点，F表示不标记
     status.cmprsk = "1", # 指定竞争风险状态为"1"
     xlab = "Follow uo time(months)",
     ylim = c(0,0.2),
     pval = T,
     data=data
)

risk_model
#保存图片为pdf
pdf("图s6a.pdf")
risk_model
dev.off()

#做图S6的B
data$status2 <- data$CANSERmortstat # 创建新的 status2 变量，并将其初始值设为 CANSERmortstat 变量的值
data$status2 <- ifelse((data$CVDmortstat == 1), 2, data$status2) # 将CVDmortstat死亡数据的 status2 值设为2，表示竞争风险
data$status2 <- factor(data$status2) # 将 status2 转换为因子类型
data = data %>% drop_na(permth_int)
data$permth_int = as.numeric(data$permth_int)

data_design <- svydesign(ids=~SDMVPSU,   #集群，抽样单元PSU
                         strata=~SDMVSTRA,   #分层
                         weights=~WTMEC,
                         nest=TRUE,
                         survey.lonely.psu="adjust",  #抽样单元为1时不报错
                         data=data)
dd<-datadist(data)
options(datadist='dd')
#构建带权重的生存模型
fit_svy<- svycoxph(Surv(permth_int, CANSERmortstat) ~ Cd_4, design=data_design)
# 使用 survfit 函数拟合生存曲线
fit1 <- survfit(Surv(permth_int, status2) ~ Cd_4, data = data,weights = (1/fit_svy$survey.design$prob)/mean(1/fit_svy$survey.design$prob))


# 竞争风险分析
risk_model= jskm(fit1, 
     mark = F, # 是否在生存曲线上标记审查点，F表示不标记
     status.cmprsk = "1", # 指定竞争风险状态为"1"
     xlab = "Follow uo time(months)",
     ylim = c(0,0.2),
     pval = T,
     data=data
)



#保存图片为pdf
pdf("图s6b.pdf")
risk_model
dev.off()





#做图s6C,CVD死亡

#图S6C需要的表格
FiigureS6_C = data.frame(
  x1 = c("variable"),
  x2 = c("NA"),
  x3 = c("NA"),
  x4 = c("NA"),
  x5 = c("HR(95% CI)"),  
  x6 = c("P-Value")
)

#将数据写入FiigureS6_C
FiigureS6_C[nrow(FiigureS6_C) + 1,] <- c("CVD mortality,No","NA","NA","NA", paste0(sum(data$CVDmortstat == 1),"/",nrow(data)),"NA")

data$status2 <- data$CANSERmortstat # 创建新的 status2 变量，并将其初始值设为 CANSERmortstat 变量的值
data$status2 <- ifelse((data$CVDmortstat == 1), 2, data$status2) # 将CVDmortstat死亡数据的 status2 值设为2，表示竞争风险
data$status2 <- factor(data$status2) # 将 status2 转换为因子类型
# pdata <- finegray(Surv(permth_int, status2) ~ Cd_4 + Age + Sex + Race + Educationlevel +
#                     PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus +
#                     Alcoholstatus+ Failingkidneys + Hypertension, 
#                   data=data, weights = (1/fit_svy$survey.design$prob)/mean(1/fit_svy$survey.design$prob))
#Cd做分类变量
cov_matrix <- model.matrix(
  ~ Cd_4 + Age + Sex + Race + Educationlevel +
    PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus +
    Alcoholstatus+ Failingkidneys + Hypertension,  # 纳入多个协变量
  data = data
)[, -1]  # 移除截距项（模型默认不包含截距
crr_mod1 <- crr(ftime = data$permth_int, fstatus = data$status2,cov1 = cov_matrix)
HR = round(data.frame(summary(crr_mod1)$conf.int), digit=3)
p = data.frame(summary(crr_mod1)$coef)
#写入表格
FiigureS6_C[nrow(FiigureS6_C) + 1,] <- c("Quantile 1",1,1,1,"1.00(Ref)","NA")
FiigureS6_C[nrow(FiigureS6_C) + 1,] <- c("Quantile 2",
                                         HR["Cd_4Quantile2", "exp.coef."],
                                         HR["Cd_4Quantile2", "X2.5."],
                                         HR["Cd_4Quantile2", "X97.5."],
                                         paste0(HR["Cd_4Quantile2", "exp.coef."], "(", HR["Cd_4Quantile2", "X2.5."], ",", HR["Cd_4Quantile2", "X97.5."], ")"), format.pval(p["Cd_4Quantile2", "p.value"],digit=3,eps=0.001))
FiigureS6_C[nrow(FiigureS6_C) + 1,] <- c("Quantile 3",
                                         HR["Cd_4Quantile3", "exp.coef."],
                                         HR["Cd_4Quantile3", "X2.5."],
                                         HR["Cd_4Quantile3", "X97.5."],
                                         paste0(HR["Cd_4Quantile2", "exp.coef."], "(", HR["Cd_4Quantile3", "X2.5."], ",", HR["Cd_4Quantile3", "X97.5."], ")"), format.pval(p["Cd_4Quantile3", "p.value"],digit=3,eps=0.001))
FiigureS6_C[nrow(FiigureS6_C) + 1,] <- c("Quantile 4",
                                         HR["Cd_4Quantile4", "exp.coef."],
                                         HR["Cd_4Quantile4", "X2.5."],
                                         HR["Cd_4Quantile4", "X97.5."],
                                         paste0(HR["Cd_4Quantile2", "exp.coef."], "(", HR["Cd_4Quantile4", "X2.5."], ",", HR["Cd_4Quantile4", "X97.5."], ")"), format.pval(p["Cd_4Quantile4", "p.value"],digit=3,eps=0.001))





#Cd做连续变量
cov_matrix <- model.matrix(
  ~ Cd + Age + Sex + Race + Educationlevel +
    PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus +
    Alcoholstatus+ Failingkidneys + Hypertension,  # 纳入多个协变量
  data = data
)[, -1]  # 移除截距项（模型默认不包含截距
crr_mod2 <- crr(ftime = data$permth_int, fstatus = data$status2,cov1 = cov_matrix)
HR = round(data.frame(summary(crr_mod2)$conf.int), digit=3)
p = data.frame(summary(crr_mod2)$coef)
#写入表格
FiigureS6_C[nrow(FiigureS6_C) + 1,] <- c("Per in-unit increase",
                                         HR["Cd", "exp.coef."],
                                         HR["Cd", "X2.5."],
                                         HR["Cd", "X97.5."],
                                         paste0(HR["Cd", "exp.coef."], "(", HR["Cd", "X2.5."], ",", HR["Cd", "X97.5."], ")"), format.pval(p["Cd", "p.value"],digit=3,eps=0.001))



#计算Hazard for trend
data$Cd_trend = as.numeric(data$Cd_4)
cov_matrix <- model.matrix(
  ~ Cd_trend + Age + Sex + Race + Educationlevel +
    PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus +
    Alcoholstatus+ Failingkidneys + Hypertension,  # 纳入多个协变量
  data = data
)[, -1]  # 移除截距项（模型默认不包含截距
crr_mod3 <- crr(ftime = data$permth_int, fstatus = data$status2,cov1 = cov_matrix)
HR = round(data.frame(summary(crr_mod3)$conf.int), digit=3)
p = data.frame(summary(crr_mod3)$coef)
#写入表格
FiigureS6_C[nrow(FiigureS6_C) + 1,] <- c("Hazard for trend",
                                         HR["Cd_trend", "exp.coef."],
                                         HR["Cd_trend", "X2.5."],
                                         HR["Cd_trend", "X97.5."],
                                         paste0(HR["Cd_trend", "exp.coef."], "(", HR["Cd_trend", "X2.5."], ",", HR["Cd_trend", "X97.5."], ")"), format.pval(p["Cd_trend", "p.value"],digit=3,eps=0.001))






#做图s6C,CANSER死亡
data$status2 <- data$CVDmortstat # 创建新的 status2 变量，并将其初始值设为 CVDmortstat 变量的值
data$status2 <- ifelse((data$CANSERmortstat == 1), 2, data$status2) # 将CANSERmortstat死亡数据的 status2 值设为2，表示竞争风险
data$status2 <- factor(data$status2) # 将 status2 转换为因子类型
# pdata <- finegray(Surv(permth_int, status2) ~ Cd_4 + Age + Sex + Race + Educationlevel +
#                     PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus +
#                     Alcoholstatus+ Failingkidneys + Hypertension, 
#                   data=data, weights = (1/fit_svy$survey.design$prob)/mean(1/fit_svy$survey.design$prob))

#Cd做分类变量
cov_matrix <- model.matrix(
  ~ Cd_4 + Age + Sex + Race + Educationlevel +
    PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus +
    Alcoholstatus+ Failingkidneys + Hypertension,  # 纳入多个协变量
  data = data
)[, -1]  # 移除截距项（模型默认不包含截距
crr_mod4 <- crr(ftime = data$permth_int, fstatus = data$status2,cov1 = cov_matrix)
#写入表格
FiigureS6_C[nrow(FiigureS6_C) + 1,] <- c("CANSER mortality,No",1,1,1, paste0(sum(data$CANSERmortstat == 1),"/",nrow(data)),"NA")

HR = round(data.frame(summary(crr_mod4)$conf.int), digit=3)
p = data.frame(summary(crr_mod4)$coef)

FiigureS6_C[nrow(FiigureS6_C) + 1,] <- c("Quantile 2",
                                         HR["Cd_4Quantile2", "exp.coef."],
                                         HR["Cd_4Quantile2", "X2.5."],
                                         HR["Cd_4Quantile2", "X97.5."],
                                         paste0(HR["Cd_4Quantile2", "exp.coef."], "(", HR["Cd_4Quantile2", "X2.5."], ",", HR["Cd_4Quantile2", "X97.5."], ")"), format.pval(p["Cd_4Quantile2", "p.value"],digit=3,eps=0.001))
FiigureS6_C[nrow(FiigureS6_C) + 1,] <- c("Quantile 3",
                                         HR["Cd_4Quantile3", "exp.coef."],
                                         HR["Cd_4Quantile3", "X2.5."],
                                         HR["Cd_4Quantile3", "X97.5."],
                                         paste0(HR["Cd_4Quantile2", "exp.coef."], "(", HR["Cd_4Quantile3", "X2.5."], ",", HR["Cd_4Quantile3", "X97.5."], ")"), format.pval(p["Cd_4Quantile3", "p.value"],digit=3,eps=0.001))
FiigureS6_C[nrow(FiigureS6_C) + 1,] <- c("Quantile 4",
                                         HR["Cd_4Quantile4", "exp.coef."],
                                         HR["Cd_4Quantile4", "X2.5."],
                                         HR["Cd_4Quantile4", "X97.5."],
                                         paste0(HR["Cd_4Quantile2", "exp.coef."], "(", HR["Cd_4Quantile4", "X2.5."], ",", HR["Cd_4Quantile4", "X97.5."], ")"), format.pval(p["Cd_4Quantile4", "p.value"],digit=3,eps=0.001))





#Cd做连续变量
cov_matrix <- model.matrix(
  ~ Cd + Age + Sex + Race + Educationlevel +
    PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus +
    Alcoholstatus+ Failingkidneys + Hypertension,  # 纳入多个协变量
  data = data
)[, -1]  # 移除截距项（模型默认不包含截距
crr_mod5 <- crr(ftime = data$permth_int, fstatus = data$status2,cov1 = cov_matrix)
HR = round(data.frame(summary(crr_mod5)$conf.int), digit=3)
p = data.frame(summary(crr_mod5)$coef)
#写入表格
FiigureS6_C[nrow(FiigureS6_C) + 1,] <- c("Per in-unit increase",
                                         HR["Cd", "exp.coef."],
                                         HR["Cd", "X2.5."],
                                         HR["Cd", "X97.5."],
                                         paste0(HR["Cd", "exp.coef."], "(", HR["Cd", "X2.5."], ",", HR["Cd", "X97.5."], ")"), format.pval(p["Cd", "p.value"],digit=3,eps=0.001))




#计算Hazard for trend
data$Cd_trend = as.numeric(data$Cd_4)
cov_matrix <- model.matrix(
  ~ Cd_trend + Age + Sex + Race + Educationlevel +
    PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus +
    Alcoholstatus+ Failingkidneys + Hypertension,  # 纳入多个协变量
  data = data
)[, -1]  # 移除截距项（模型默认不包含截距
crr_mod6 <- crr(ftime = data$permth_int, fstatus = data$status2,cov1 = cov_matrix)
HR = round(data.frame(summary(crr_mod6)$conf.int), digit=3)
p = data.frame(summary(crr_mod6)$coef)
#写入表格
FiigureS6_C[nrow(FiigureS6_C) + 1,] <- c("Hazard for trend",
                                         HR["Cd_trend", "exp.coef."],
                                         HR["Cd_trend", "X2.5."],
                                         HR["Cd_trend", "X97.5."],
                                         paste0(HR["Cd_trend", "exp.coef."], "(", HR["Cd_trend", "X2.5."], ",", HR["Cd_trend", "X97.5."], ")"), format.pval(p["Cd_trend", "p.value"],digit=3,eps=0.001))


#绘制图S6C
FiigureS6_C[FiigureS6_C == "NA"] = NA
FiigureS6_C$x2 = as.numeric(FiigureS6_C$x2)
FiigureS6_C$x3 = as.numeric(FiigureS6_C$x3)
FiigureS6_C$x4 = as.numeric(FiigureS6_C$x4)

library(forestplot)
library(forestploter)
# 定义绘图的主题
tm <- forest_theme(
  base_size = 10,       
  refline_col = "red4",
  refline_lwd = 2,
  #ci_col = "#1E9C76",
  refline_lty = "solid",
  arrow_type = "closed",    
  ci_lty = 1,
  ci_Theight = 0.2,
  ci_lwd = 2.3
)


#绘制图S6c
p = forestplot(labeltext = as.matrix(FiigureS6_C[,c(1,5,6)]),
           #设置用于文本展示的列，此处我们用数据的前四列作为文本，在图中展示
           mean = FiigureS6_C$x2, #设置均值
           lower = FiigureS6_C$x3, #设置均值的lowlimits限
           upper = FiigureS6_C$x4, #设置均值的uplimits限
           #is.summary=c(T,T,F,F,F,F,T,F,F,F,F,T,F,F,F,F),
           #该参数接受一个逻辑向量，用于定义数据中每一行是否是汇总值，若是，则在对应位置设置为TRUE，若否，则设置为FALSE；设置为TRUE的行则以粗体出现
           zero = 1, #设置参照值，此处我们展示的是HR值，故参照值是1，而不是0
           boxsize = 0.2, #设置点估计的方形大小
           lineheight = unit(8,'mm'),#设置图形中的行距
           colgap = unit(6,'mm'),#设置图形中的列间距
           lwd.zero = 0.2,#设置参考线的粗细
           lwd.ci = 1,#设置区间估计线的粗细
           arrow_lab = c("Low risk", "High Risk"),
           graphwidth = unit(40,'mm'),#设置置信区间绘图宽度
           col=fpColors(box='black',summary="black",lines = 'black',zero = 'black'),
           #使用fpColors()函数定义图形元素的颜色，从左至右分别对应点估计方形，汇总值，区间估计线，参考线
           xlab="The estimates",#设置x轴标签
           lwd.xaxis=0,#设置X轴线的粗细
           lty.ci = "solid",
           theme = tm,   
           graph.pos = 2)#设置森林图的位置，此处设置为2，则出现在第2列




#保存图片为pdf
pdf("图s6c.pdf")
p
dev.off()








