library(rms)#RCS
library(survminer)#曲线
library(ggplot2)#画图
library(survival)
library("scales")
library(plotRCS)#限制性立方样条绘图
library(tableone)
library(survey)

#加载数据
load("data.RData")
data  = droplevels(data)
#将Cd进行对数转换,为了不出现负值，加1后再转换
data$Cd_log = log(data$Cd + 1)

dd<-datadist(data)
options(datadist='dd')

data_design <- svydesign(ids=~SDMVPSU,   #集群，抽样单元PSU
                         strata=~SDMVSTRA,   #分层
                         weights=~WTMEC,
                         nest=TRUE,
                         survey.lonely.psu="adjust",  #抽样单元为1时不报错
                         data=data)

#画图2A
#构建带权重的生存模型
fit_svy<- svycoxph(Surv(permth_int, Allcausemortstat) ~ rcs(Cd_log,4) + Age + Sex + Race + Educationlevel + 
                 PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
                 Alcoholstatus+ Failingkidneys + Hypertension, design=data_design)
#weight变量是权重
fit<- cph(Surv(permth_int, Allcausemortstat) ~ rcs(Cd_log,4) + Age + Sex + Race + Educationlevel + 
            PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
            Alcoholstatus+ Failingkidneys + Hypertension, x=TRUE, y=TRUE,data=data, weights = (1/fit_svy$survey.design$prob)/mean(1/fit_svy$survey.design$prob))
#计算预测值
Pre0<-rms::Predict(fit,Cd_log=data$Cd_log,fun=exp,type="predictions",ref.zero=T,conf.int= 0.95,digits=2)
Pre0<-as.data.frame(Pre0)#预测值转换为df必须转换，不然作不了图

#计算p overall和p nonliner
fit_p = anova(fit)
fit_over = format.pval(fit_p[1,3],digit=3,eps=0.001)
fit_nonl = format.pval(fit_p[2,3],digit=3,eps=0.001)

#计算直方图的密度，按照0.1的宽度进划分区间，并计算区间的最大密度和最小密度，用于绘制坐标轴
d<-density(data$Cd_log, n=as.integer((max(data$Cd_log) - min(data$Cd_log)) / 0.1))
dmin<-as.numeric(min(d[["y"]])) / sum(d[["y"]])##density
dmax<-as.numeric(max(d[["y"]])) / sum(d[["y"]])##density
CI_upper = max(Pre0$upper)
Pre0$Cd_log = exp(Pre0$Cd_log) - 1#取消对对数转换方便画图

#画图
p = ggplot(Pre0,aes(x=Cd_log))+
  geom_histogram(aes(x=Cd_log,y =rescale(after_stat(density),c(0,CI_upper))),binwidth = 0.1,fill="#FCC060",colour="white")+#画直方图
  geom_line(data=Pre0,aes(Cd_log,yhat),linetype=1,linewidth=1,alpha = 0.9,colour="red")+#画HR曲线
  geom_ribbon(data=Pre0,aes(ymin = lower, ymax = upper),linetype=2,alpha = 0.3,fill="grey",colour="red")+#画置信区间
  geom_vline(aes(xintercept=0.37), colour="#BB0000", linetype="dashed")+#在HR曲线与y=1交点画一条x轴的红色虚线
  geom_hline(yintercept=1, linetype="dashed",linewidth=1)+#在HR曲线与y=1交点画一条y轴的红色虚线
  theme_bw()+
  #scale_x_log10()+#设置x轴对数坐标
  scale_x_continuous(trans = log_trans(),n.breaks = 6,labels = label_number(accuracy = 0.01))+#设置x轴对数坐标
  #ylim(0, 6)+
  theme(panel.grid.major = element_blank(),panel.grid.minor = element_blank(),
        axis.text.x = element_text(size=14),axis.text.y = element_text(size=14),
        axis.title.x = element_text(size=14),axis.title.y = element_text(size=14))+#设置网格，字体大小
  xlab("Cd(ug/l)")+ylab("Hazard ratio(95%CI)")+#设置x和y轴标签
  ggtitle('All-cause mortality')+#添加标题
  annotate(geom="text",x=0,y=CI_upper,label=paste('P for overall',fit_over,'\nP for nonlinearity',fit_nonl),hjust=-0.1,size=5,color="black")+#添加文字
  #annotate(geom="text",x=0,y=CI_upper,label=paste('P for nonlinearity',fit_nonl),vjust=3,hjust=-0.5,size=5,color="black")+#添加文字
  geom_text(x=0.37,y=1,label='Ref Cd(ug/l)=0.37',vjust=1.5,hjust=2.3,size=5,color="red")+#添加交点标注
  scale_y_continuous(sec.axis = sec_axis( ~rescale(.,c(dmin,dmax)), name = "Density, %"))#添加右侧坐标轴

#保存图片为pdf
pdf("图2A.pdf")
p
dev.off()

#画图2B
#构建生存模型
#构建带权重的生存模型
fit_svy<- svycoxph(Surv(permth_int, CVDmortstat) ~ rcs(Cd_log,4) + Age + Sex + Race + Educationlevel + 
                     PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
                     Alcoholstatus+ Failingkidneys + Hypertension, design=data_design)
#weight变量是权重
fit<- cph(Surv(permth_int, CVDmortstat) ~ rcs(Cd_log,4) + Age + Sex + Race + Educationlevel + 
            PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
            Alcoholstatus+ Failingkidneys + Hypertension, x=TRUE, y=TRUE,data=data, weights = (1/fit_svy$survey.design$prob)/mean(1/fit_svy$survey.design$prob))
#计算预测值
Pre0<-rms::Predict(fit,Cd_log=data$Cd_log,fun=exp,type="predictions",ref.zero=T,conf.int= 0.95,digits=2)
Pre0<-as.data.frame(Pre0)#预测值转换为df必须转换，不然作不了图

#计算p overall和p nonliner
fit_p = anova(fit)
fit_over = format.pval(fit_p[1,3],digit=3,eps=0.001)
fit_nonl = format.pval(fit_p[2,3],digit=3,eps=0.001)

#计算直方图的密度，按照0.1的宽度进划分区间，并计算区间的最大密度和最小密度，用于绘制坐标轴
d<-density(data$Cd_log, n=as.integer((max(data$Cd_log) - min(data$Cd_log)) / 0.1))
dmin<-as.numeric(min(d[["y"]])) / sum(d[["y"]])##density
dmax<-as.numeric(max(d[["y"]])) / sum(d[["y"]])##density
CI_upper = max(Pre0$upper)
Pre0$Cd_log = exp(Pre0$Cd_log) - 1#取消对对数转换方便画图

#画图
p = ggplot(Pre0,aes(x=Cd_log))+
  geom_histogram(aes(x=Cd_log,y =rescale(after_stat(density),c(0,CI_upper))),binwidth = 0.1,fill="#FCC060",colour="white")+#画直方图
  geom_line(data=Pre0,aes(Cd_log,yhat),linetype=1,linewidth=1,alpha = 0.9,colour="red")+#画HR曲线
  geom_ribbon(data=Pre0,aes(ymin = lower, ymax = upper),linetype=2,alpha = 0.3,fill="grey",colour="red")+#画置信区间
  geom_vline(aes(xintercept=0.37), colour="#BB0000", linetype="dashed")+#在HR曲线与y=1交点画一条x轴的红色虚线
  geom_hline(yintercept=1, linetype="dashed",linewidth=1)+#在HR曲线与y=1交点画一条y轴的红色虚线
  theme_bw()+
  #scale_x_log10()+#设置x轴对数坐标
  scale_x_continuous(trans = log_trans(),n.breaks = 6,labels = label_number(accuracy = 0.01))+#设置x轴对数坐标
  #ylim(0, 6)+
  theme(panel.grid.major = element_blank(),panel.grid.minor = element_blank(),
        axis.text.x = element_text(size=14),axis.text.y = element_text(size=14),
        axis.title.x = element_text(size=14),axis.title.y = element_text(size=14))+#设置网格，字体大小
  xlab("Cd(ug/l)")+ylab("Hazard ratio(95%CI)")+#设置x和y轴标签
  ggtitle('CVD-cause mortality')+#添加标题
  annotate(geom="text",x=0,y=CI_upper,label=paste('P for overall',fit_over,'\nP for nonlinearity',fit_nonl),hjust=-0.1,size=5,color="black")+#添加文字
  #annotate(geom="text",x=0,y=CI_upper,label=paste('P for nonlinearity',fit_nonl),vjust=3,hjust=-0.5,size=5,color="black")+#添加文字
  geom_text(x=0.37,y=1,label='Ref Cd(ug/l)=0.37',vjust=1.5,hjust=2.3,size=5,color="red")+#添加交点标注
  scale_y_continuous(sec.axis = sec_axis( ~rescale(.,c(dmin,dmax)), name = "Density, %"))#添加右侧坐标轴

#保存图片为pdf
pdf("图2B.pdf")
p
dev.off()

#画图2c
#构建生存模型
#构建带权重的生存模型
fit_svy<- svycoxph(Surv(permth_int, CANSERmortstat) ~ rcs(Cd_log,4) + Age + Sex + Race + Educationlevel + 
                     PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
                     Alcoholstatus+ Failingkidneys + Hypertension, design=data_design)
#weight变量是权重
fit<- cph(Surv(permth_int, CANSERmortstat) ~ rcs(Cd_log,4) + Age + Sex + Race + Educationlevel + 
            PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
            Alcoholstatus+ Failingkidneys + Hypertension, x=TRUE, y=TRUE,data=data, weights = (1/fit_svy$survey.design$prob)/mean(1/fit_svy$survey.design$prob))
#计算预测值
Pre0<-rms::Predict(fit,Cd_log=data$Cd_log,fun=exp,type="predictions",ref.zero=T,conf.int= 0.95,digits=2)
Pre0<-as.data.frame(Pre0)#预测值转换为df必须转换，不然作不了图

#计算p overall和p nonliner
fit_p = anova(fit)
fit_over = format.pval(fit_p[1,3],digit=3,eps=0.001)
fit_nonl = format.pval(fit_p[2,3],digit=3,eps=0.001)

#计算直方图的密度，按照0.1的宽度进划分区间，并计算区间的最大密度和最小密度，用于绘制坐标轴
d<-density(data$Cd_log, n=as.integer((max(data$Cd_log) - min(data$Cd_log)) / 0.1))
dmin<-as.numeric(min(d[["y"]])) / sum(d[["y"]])##density
dmax<-as.numeric(max(d[["y"]])) / sum(d[["y"]])##density
CI_upper = max(Pre0$upper)
Pre0$Cd_log = exp(Pre0$Cd_log) - 1#取消对对数转换方便画图

#画图
p = ggplot(Pre0,aes(x=Cd_log))+
  geom_histogram(aes(x=Cd_log,y =rescale(after_stat(density),c(0,CI_upper))),binwidth = 0.1,fill="#FCC060",colour="white")+#画直方图
  geom_line(data=Pre0,aes(Cd_log,yhat),linetype=1,linewidth=1,alpha = 0.9,colour="red")+#画HR曲线
  geom_ribbon(data=Pre0,aes(ymin = lower, ymax = upper),linetype=2,alpha = 0.3,fill="grey",colour="red")+#画置信区间
  geom_vline(aes(xintercept=0.37), colour="#BB0000", linetype="dashed")+#在HR曲线与y=1交点画一条x轴的红色虚线
  geom_hline(yintercept=1, linetype="dashed",linewidth=1)+#在HR曲线与y=1交点画一条y轴的红色虚线
  theme_bw()+
  #scale_x_log10()+#设置x轴对数坐标
  scale_x_continuous(trans = log_trans(),n.breaks = 6,labels = label_number(accuracy = 0.01))+#设置x轴对数坐标
  #ylim(0, 6)+
  theme(panel.grid.major = element_blank(),panel.grid.minor = element_blank(),
        axis.text.x = element_text(size=14),axis.text.y = element_text(size=14),
        axis.title.x = element_text(size=14),axis.title.y = element_text(size=14))+#设置网格，字体大小
  xlab("Cd(ug/l)")+ylab("Hazard ratio(95%CI)")+#设置x和y轴标签
  ggtitle('CANSER-cause mortality')+#添加标题
  annotate(geom="text",x=0,y=CI_upper,label=paste('P for overall',fit_over,'\nP for nonlinearity',fit_nonl),hjust=-0.1,size=5,color="black")+#添加文字
  #annotate(geom="text",x=0,y=CI_upper,label=paste('P for nonlinearity',fit_nonl),vjust=3,hjust=-0.5,size=5,color="black")+#添加文字
  geom_text(x=0.37,y=1,label='Ref Cd(ug/l)=0.37',vjust=1.5,hjust=2.3,size=5,color="red")+#添加交点标注
  scale_y_continuous(sec.axis = sec_axis( ~rescale(.,c(dmin,dmax)), name = "Density, %"))#添加右侧坐标轴

#保存图片为pdf
pdf("图2c.pdf")
p
dev.off()

#计算表3的数据,首先进行对数转换
data$GGT_log= log(data$GGT + 1)
data$UA_log= log(data$UA + 1)
data$HDL_log= log(data$HDL + 1)
data$UHR_log= log(data$UHR + 1)
data$NLR_log= log(data$NLR + 1)
data$MLR_log= log(data$MLR + 1)
data$NMLR_log= log(data$NMLR + 1)
data$SIRI_log= log(data$SIRI + 1)
data$SII_log= log(data$SII + 1)
save(data, file="data.RData")

#变量标准化处理
data$GGT_log = rescale(data$GGT_log, to=c(0,1))
data$UA_log = rescale(data$UA_log,  to=c(0,1))
data$HDL_log = rescale(data$HDL_log,  to=c(0,1))
data$UHR_log = rescale(data$UHR_log,  to=c(0,1))
data$NLR_log = rescale(data$NLR_log,  to=c(0,1))
data$MLR_log = rescale(data$MLR_log,  to=c(0,1))
data$NMLR_log = rescale(data$NMLR_log,  to=c(0,1))
data$SIRI_log = rescale(data$SIRI_log,  to=c(0,1))
data$SII_log = rescale(data$SII_log,  to=c(0,1))
data$Cd_log = rescale(data$Cd_log,  to=c(0,1))

data_design <- svydesign(ids=~SDMVPSU,   #集群，抽样单元PSU
                         strata=~SDMVSTRA,   #分层
                         weights=~WTMEC,
                         nest=TRUE,
                         survey.lonely.psu="adjust",  #抽样单元为1时不报错
                         data=data)

dd<-datadist(data)
options(datadist='dd')



#部分调整GGT
fit1_GGT <- svyglm(GGT_log ~ Cd_log + Age + Sex + Race, x=TRUE, y=TRUE,design=data_design)
#全调整GGT
fit2_GGT <- svyglm(GGT_log ~ Cd_log + Age + Sex + Race + Educationlevel + 
                  PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
                  Alcoholstatus+ Failingkidneys + Hypertension, x=TRUE, y=TRUE,design=data_design)
#提取变量的估计值
B1_GGT = round(fit1_GGT$coefficients["Cd_log"], digit=4)
B2_GGT = round(fit2_GGT$coefficients["Cd_log"], digit=4)
#提取变量的p值
P1_GGT = format.pval(summary(fit1_GGT)$coefficients["Cd_log","Pr(>|t|)"],digit=3,eps=0.001)
P2_GGT = format.pval(summary(fit2_GGT)$coefficients["Cd_log","Pr(>|t|)"],digit=3,eps=0.001)
#提取变量的置信区间
CI1_GGT = c(round(confint(fit1_GGT)[2,1], digit=4),round(confint(fit1_GGT)[2,2], digit=4))
CI2_GGT = c(round(confint(fit2_GGT)[2,1], digit=4),round(confint(fit2_GGT)[2,2], digit=4))


#部分调整UA
fit1_UA <- svyglm(UA_log ~ Cd_log + Age + Sex + Race, x=TRUE, y=TRUE,design=data_design)
#全调整UA
fit2_UA <- svyglm(UA_log ~ Cd_log + Age + Sex + Race + Educationlevel + 
                 PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
                 Alcoholstatus+ Failingkidneys + Hypertension, x=TRUE, y=TRUE,design=data_design)
#提取变量的估计值
B1_UA = round(fit1_UA$coefficients["Cd_log"], digit=4)
B2_UA = round(fit2_UA$coefficients["Cd_log"], digit=4)
#提取变量的p值
P1_UA = format.pval(summary(fit1_UA)$coefficients["Cd_log","Pr(>|t|)"],digit=3,eps=0.001)
P2_UA = format.pval(summary(fit2_UA)$coefficients["Cd_log","Pr(>|t|)"],digit=3,eps=0.001)
#提取变量的置信区间
CI1_UA = c(round(confint(fit1_UA)[2,1], digit=4),round(confint(fit1_UA)[2,2], digit=4))
CI2_UA = c(round(confint(fit2_UA)[2,1], digit=4),round(confint(fit2_UA)[2,2], digit=4))

#部分调整HDL
fit1_HDL <- svyglm(HDL_log ~ Cd_log + Age + Sex + Race, x=TRUE, y=TRUE,design=data_design)
#全调整HDL
fit2_HDL <- svyglm(HDL_log ~ Cd_log + Age + Sex + Race + Educationlevel + 
                  PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
                  Alcoholstatus+ Failingkidneys + Hypertension, x=TRUE, y=TRUE,design=data_design)
#提取变量的估计值
B1_HDL = round(fit1_HDL$coefficients["Cd_log"], digit=4)
B2_HDL = round(fit2_HDL$coefficients["Cd_log"], digit=4)
#提取变量的p值
P1_HDL = format.pval(summary(fit1_HDL)$coefficients["Cd_log","Pr(>|t|)"],digit=3,eps=0.001)
P2_HDL = format.pval(summary(fit2_HDL)$coefficients["Cd_log","Pr(>|t|)"],digit=3,eps=0.001)
#提取变量的置信区间
CI1_HDL = c(round(confint(fit1_HDL)[2,1], digit=4),round(confint(fit1_HDL)[2,2], digit=4))
CI2_HDL = c(round(confint(fit2_HDL)[2,1], digit=4),round(confint(fit2_HDL)[2,2], digit=4))

#部分调整UHR
fit1_UHR <- svyglm(UHR_log ~ Cd_log + Age + Sex + Race, x=TRUE, y=TRUE,design=data_design)
#全调整UHR
fit2_UHR <- svyglm(UHR_log ~ Cd_log + Age + Sex + Race + Educationlevel + 
                  PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
                  Alcoholstatus+ Failingkidneys + Hypertension, x=TRUE, y=TRUE,design=data_design)
#提取变量的估计值
B1_UHR = round(fit1_UHR$coefficients["Cd_log"], digit=4)
B2_UHR = round(fit2_UHR$coefficients["Cd_log"], digit=4)
#提取变量的p值
P1_UHR = format.pval(summary(fit1_UHR)$coefficients["Cd_log","Pr(>|t|)"],digit=3,eps=0.001)
P2_UHR = format.pval(summary(fit2_UHR)$coefficients["Cd_log","Pr(>|t|)"],digit=3,eps=0.001)
#提取变量的置信区间
CI1_UHR = c(round(confint(fit1_UHR)[2,1], digit=4),round(confint(fit1_UHR)[2,2], digit=4))
CI2_UHR = c(round(confint(fit2_UHR)[2,1], digit=4),round(confint(fit2_UHR)[2,2], digit=4))

#部分调整NLR
fit1_NLR <- svyglm(NLR_log ~ Cd_log + Age + Sex + Race, x=TRUE, y=TRUE,design=data_design)
#全调整NLR
fit2_NLR <- svyglm(NLR_log ~ Cd_log + Age + Sex + Race + Educationlevel + 
                  PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
                  Alcoholstatus+ Failingkidneys + Hypertension, x=TRUE, y=TRUE,design=data_design)
#提取变量的估计值
B1_NLR = round(fit1_NLR$coefficients["Cd_log"], digit=4)
B2_NLR = round(fit2_NLR$coefficients["Cd_log"], digit=4)
#提取变量的p值
P1_NLR = format.pval(summary(fit1_NLR)$coefficients["Cd_log","Pr(>|t|)"],digit=3,eps=0.001)
P2_NLR = format.pval(summary(fit2_NLR)$coefficients["Cd_log","Pr(>|t|)"],digit=3,eps=0.001)
#提取变量的置信区间
CI1_NLR = c(round(confint(fit1_NLR)[2,1], digit=4),round(confint(fit1_NLR)[2,2], digit=4))
CI2_NLR = c(round(confint(fit2_NLR)[2,1], digit=4),round(confint(fit2_NLR)[2,2], digit=4))

#部分调整MLR
fit1_MLR <- svyglm(MLR_log ~ Cd_log + Age + Sex + Race, x=TRUE, y=TRUE,design=data_design)
#全调整MLR
fit2_MLR <- svyglm(MLR_log ~ Cd_log + Age + Sex + Race + Educationlevel + 
                  PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
                  Alcoholstatus+ Failingkidneys + Hypertension, x=TRUE, y=TRUE,design=data_design)
#提取变量的估计值
B1_MLR = round(fit1_MLR$coefficients["Cd_log"], digit=4)
B2_MLR = round(fit2_MLR$coefficients["Cd_log"], digit=4)
#提取变量的p值
P1_MLR = format.pval(summary(fit1_MLR)$coefficients["Cd_log","Pr(>|t|)"],digit=3,eps=0.001)
P2_MLR = format.pval(summary(fit2_MLR)$coefficients["Cd_log","Pr(>|t|)"],digit=3,eps=0.001)
#提取变量的置信区间
CI1_MLR = c(round(confint(fit1_MLR)[2,1], digit=4),round(confint(fit1_MLR)[2,2], digit=4))
CI2_MLR = c(round(confint(fit2_MLR)[2,1], digit=4),round(confint(fit2_MLR)[2,2], digit=4))

#部分调整NMLR
fit1_NMLR <- svyglm(NMLR_log ~ Cd_log + Age + Sex + Race, x=TRUE, y=TRUE,design=data_design)
#全调整NMLR
fit2_NMLR <- svyglm(NMLR_log ~ Cd_log + Age + Sex + Race + Educationlevel + 
                   PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
                   Alcoholstatus+ Failingkidneys + Hypertension, x=TRUE, y=TRUE,design=data_design)
#提取变量的估计值
B1_NMLR = round(fit1_NMLR$coefficients["Cd_log"], digit=4)
B2_NMLR = round(fit2_NMLR$coefficients["Cd_log"], digit=4)
#提取变量的p值
P1_NMLR = format.pval(summary(fit1_NMLR)$coefficients["Cd_log","Pr(>|t|)"],digit=3,eps=0.001)
P2_NMLR = format.pval(summary(fit2_NMLR)$coefficients["Cd_log","Pr(>|t|)"],digit=3,eps=0.001)
#提取变量的置信区间
CI1_NMLR = c(round(confint(fit1_NMLR)[2,1], digit=4),round(confint(fit1_NMLR)[2,2], digit=4))
CI2_NMLR = c(round(confint(fit2_NMLR)[2,1], digit=4),round(confint(fit2_NMLR)[2,2], digit=4))

#部分调整SIRI
fit1_SIRI <- svyglm(SIRI_log ~ Cd_log + Age + Sex + Race, x=TRUE, y=TRUE,design=data_design)
#全调整SIRI
fit2_SIRI <- svyglm(SIRI_log ~ Cd_log + Age + Sex + Race + Educationlevel + 
                   PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
                   Alcoholstatus+ Failingkidneys + Hypertension, x=TRUE, y=TRUE,design=data_design)
#提取变量的估计值
B1_SIRI = round(fit1_SIRI$coefficients["Cd_log"], digit=4)
B2_SIRI = round(fit2_SIRI$coefficients["Cd_log"], digit=4)
#提取变量的p值
P1_SIRI = format.pval(summary(fit1_SIRI)$coefficients["Cd_log","Pr(>|t|)"],digit=3,eps=0.001)
P2_SIRI = format.pval(summary(fit2_SIRI)$coefficients["Cd_log","Pr(>|t|)"],digit=3,eps=0.001)
#提取变量的置信区间
CI1_SIRI = c(round(confint(fit1_SIRI)[2,1], digit=4),round(confint(fit1_SIRI)[2,2], digit=4))
CI2_SIRI = c(round(confint(fit2_SIRI)[2,1], digit=4),round(confint(fit2_SIRI)[2,2], digit=4))

#部分调整SII
fit1_SII <- svyglm(SII_log ~ Cd_log + Age + Sex + Race, x=TRUE, y=TRUE,design=data_design)
#全调整SII
fit2_SII <- svyglm(SII_log ~ Cd_log + Age + Sex + Race + Educationlevel + 
                  PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
                  Alcoholstatus+ Failingkidneys + Hypertension, x=TRUE, y=TRUE,design=data_design)
#提取变量的估计值
B1_SII = round(fit1_SII$coefficients["Cd_log"], digit=4)
B2_SII = round(fit2_SII$coefficients["Cd_log"], digit=4)
#提取变量的p值
P1_SII = format.pval(summary(fit1_SII)$coefficients["Cd_log","Pr(>|t|)"],digit=3,eps=0.001)
P2_SII = format.pval(summary(fit2_SII)$coefficients["Cd_log","Pr(>|t|)"],digit=3,eps=0.001)
#提取变量的置信区间
CI1_SII = c(round(confint(fit1_SII)[2,1], digit=4),round(confint(fit1_SII)[2,2], digit=4))
CI2_SII = c(round(confint(fit2_SII)[2,1], digit=4),round(confint(fit2_SII)[2,2], digit=4))

#生成表3
Table3 = data.frame(
  x1 = c("NA"),
  x2 = c("Partially adjusted β (95% CI)"),
  x3 = c("P value"),
  x4 = c("Fully adjusted β (95% CI)"),
  x5 = c("P value")
)
#将数据写入table3
Table3[nrow(Table3) + 1,] <- c("Oxidative stress index","NA","NA","NA","NA")
Table3[nrow(Table3) + 1,] <- c("GGT",paste0(B1_GGT,"(", CI1_GGT[1], ",", CI1_GGT[2],")"),P1_GGT,paste0(B2_GGT,"(", CI2_GGT[1], ",", CI2_GGT[2],")"),P2_GGT)
Table3[nrow(Table3) + 1,] <- c("UA",paste0(B1_UA,"(", CI1_UA[1], ",", CI1_UA[2],")"),P1_UA,paste0(B2_UA,"(", CI2_UA[1], ",", CI2_UA[2],")"),P2_UA)
Table3[nrow(Table3) + 1,] <- c("HDL",paste0(B1_HDL,"(", CI1_HDL[1], ",", CI1_HDL[2],")"),P1_HDL,paste0(B2_HDL,"(", CI2_HDL[1], ",", CI2_HDL[2],")"),P2_HDL)
Table3[nrow(Table3) + 1,] <- c("UHR",paste0(B1_UHR,"(", CI1_UHR[1], ",", CI1_UHR[2],")"),P1_UHR,paste0(B2_UHR,"(", CI2_UHR[1], ",", CI2_UHR[2],")"),P2_UHR)
Table3[nrow(Table3) + 1,] <- c(" Inflammatory indicators","NA","NA","NA","NA")
Table3[nrow(Table3) + 1,] <- c("NLR",paste0(B1_NLR,"(", CI1_NLR[1], ",", CI1_NLR[2],")"),P1_NLR,paste0(B2_NLR,"(", CI2_NLR[1], ",", CI2_NLR[2],")"),P2_NLR)
Table3[nrow(Table3) + 1,] <- c("MLR",paste0(B1_MLR,"(", CI1_MLR[1], ",", CI1_MLR[2],")"),P1_MLR,paste0(B2_MLR,"(", CI2_MLR[1], ",", CI2_MLR[2],")"),P2_GGT)
Table3[nrow(Table3) + 1,] <- c("NMLR",paste0(B1_NMLR,"(", CI1_NMLR[1], ",", CI1_NMLR[2],")"),P1_NMLR,paste0(B2_NMLR,"(", CI2_NMLR[1], ",", CI2_NMLR[2],")"),P2_NMLR)
Table3[nrow(Table3) + 1,] <- c("SIRI",paste0(B1_SIRI,"(", CI1_SIRI[1], ",", CI1_SIRI[2],")"),P1_SIRI,paste0(B2_SIRI,"(", CI2_SIRI[1], ",", CI2_SIRI[2],")"),P2_SIRI)
Table3[nrow(Table3) + 1,] <- c("SII",paste0(B1_SII,"(", CI1_SII[1], ",", CI1_SII[2],")"),P1_SII,paste0(B2_SII,"(", CI2_SII[1], ",", CI2_SII[2],")"),P2_SII)


# 保存为 CSV 格式文件
write.csv(Table3, file = "Table3.csv")













#计算表4的数据,分连续变量和分类变量、全调整和部分调整、3种死亡率进行分析
Table4 = data.frame(
  x1 = c("Variable"),
  x2 = c(" All-cause mortality, HR (95% CI)"),
  x3 = c(" All-cause mortality, HR (95% CI)"),
  x4 = c("CVD mortality, HR (95% CI)"),
  x5 = c("CVD mortality, HR (95% CI)"),
  x6 = c("Cancer mortality, HR (95% CI)"),
  x7 = c("Cancer mortality, HR (95% CI)")
)
Table4[nrow(Table4) + 1,] <- c("NA","Partially adjusted","Fully adjusted","Partially adjusted","Fully adjusted","Partially adjusted","Fully adjusted")
Table4[nrow(Table4) + 1,] <- c("Oxidative stress index","NA","NA","NA","NA","NA","NA")

line1_below = c("Below","1.00 (Ref.)","1.00 (Ref.)","1.00 (Ref.)","1.00 (Ref.)","1.00 (Ref.)","1.00 (Ref.)")

# #定义计算不同情况下HR值和CI的函数
# Cox_analysise = function(variable,status, data){
#   #variable是要分析的变量名称
#   #status是结局变量名称
#   #data是用到的数据框
#   line1 = c()
#   #拟合全因死亡率variable连续变量部分调整模型
#   fit1 <- coxph(Surv(data[,"permth_int"], data[,status]) ~ data[,variable] + data[,"Age"]+
#                   data[,"Sex"] + data[,"Race"], x=TRUE, y=TRUE,data=data)
#   #提取HR和置信区间
#   fit1_result = ShowRegTable(fit1, #写模型
#                exp = TRUE,#表示指数转化，提取HR
#                digits = 2,#表示HR的小数位为2位
#                pDigits = 3, #表示p值得位数为3位
#                printToggle=FALSE,quote = FALSE, ciFun = confint)
# 
#   line1 = append(line1, data.frame(fit1_result)["data[, variable]","exp.coef...confint."])
# 
#   #拟合全因死亡率variable连续变量全调整模型
#   fit1 <- coxph(Surv(data[,"permth_int"], data[,status]) ~ data[,variable] + data[,"Age"] + data[,"Sex"] +
#                        data[,"Race"] + data[,"Educationlevel"] + data[,"PIR"] + data[,"BMI"] +
#                        data[,"WWI"] + data[,"Maritalstatus"] + data[,"Physicalactivity"] +
#                        data[,"Smokingstatus"] +data[,"Alcoholstatus"]+ data[,"Failingkidneys"] +
#                        data[,"Hypertension"], x=TRUE, y=TRUE,data=data)
# 
#    #提取HR和置信区间
#   fit1_result = ShowRegTable(fit1, #写模型
#                exp = TRUE,#表示指数转化，提取HR
#                digits = 2,#表示HR的小数位为2位
#                pDigits = 3, #表示p值得位数为3位
#                printToggle=FALSE,quote = FALSE, ciFun = confint)
# 
#   line1 = append(line1, data.frame(fit1_result)["data[, variable]","exp.coef...confint."])
#   return(line1)
# }


#定义计算不同情况下HR值和CI的函数
Cox_analysise = function(variable,status, design){
  line1 = c()
  #拟合全因死亡率variable连续变量部分调整模型
  cor = "+ Age + Sex + Race"
  f1 = as.formula(paste0("Surv(permth_int, ", status, ")", "~", variable, cor))
  fit1 <- svycoxph(f1,design=design)
  #提取HR和置信区间
  fit1_result = ShowRegTable(fit1, #写模型
                             exp = TRUE,#表示指数转化，提取HR
                             digits = 2,#表示HR的小数位为2位
                             pDigits = 3, #表示p值得位数为3位
                             printToggle=FALSE,quote = FALSE, ciFun = confint)

  line1 = append(line1, data.frame(fit1_result)[variable,"exp.coef...confint."])

  #拟合全因死亡率variable连续变量全调整模型
  cor = "+ Age + Sex + Race + Educationlevel +
                 PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus +
                 Alcoholstatus+ Failingkidneys + Hypertension"
  f1 = as.formula(paste0("Surv(permth_int, ", status, ")", "~", variable, cor))
  fit1 <- svycoxph(f1,design=design)
  #提取HR和置信区间
  fit1_result = ShowRegTable(fit1, #写模型
                             exp = TRUE,#表示指数转化，提取HR
                             digits = 2,#表示HR的小数位为2位
                             pDigits = 3, #表示p值得位数为3位
                             printToggle=FALSE,quote = FALSE, ciFun = confint)

  line1 = append(line1, data.frame(fit1_result)[variable,"exp.coef...confint."])
  return(line1)
}

load("data.RData")

#按加权二分位数对9个变量进行分类
result <- wtd.quantile(data$GGT, weights = data$WTMEC, probs = c(0, 0.5,1))
data$GGT_2 = cut(data$GGT, breaks = c(0,result[2], Inf), labels = c("Quantile1","Quantile2"), right=FALSE)
result <- wtd.quantile(data$UA, weights = data$WTMEC, probs = c(0, 0.5,1))
data$UA_2 = cut(data$UA, breaks = c(0,result[2], Inf), labels = c("Quantile1","Quantile2"), right=FALSE)
result <- wtd.quantile(data$HDL, weights = data$WTMEC, probs = c(0, 0.5,1))
data$HDL_2 = cut(data$HDL, breaks = c(0,result[2], Inf), labels = c("Quantile1","Quantile2"), right=FALSE)
result <- wtd.quantile(data$UHR, weights = data$WTMEC, probs = c(0, 0.5,1))
data$UHR_2 = cut(data$UHR, breaks = c(0,result[2], Inf), labels = c("Quantile1","Quantile2"), right=FALSE)
result <- wtd.quantile(data$NLR, weights = data$WTMEC, probs = c(0, 0.5,1))
data$NLR_2 = cut(data$NLR, breaks = c(0,result[2], Inf), labels = c("Quantile1","Quantile2"), right=FALSE)
result <- wtd.quantile(data$MLR, weights = data$WTMEC, probs = c(0, 0.5,1))
data$MLR_2 = cut(data$MLR, breaks = c(0,result[2], Inf), labels = c("Quantile1","Quantile2"), right=FALSE)
result <- wtd.quantile(data$NMLR, weights = data$WTMEC, probs = c(0, 0.5,1))
data$NMLR_2 = cut(data$NMLR, breaks = c(0,result[2], Inf), labels = c("Quantile1","Quantile2"), right=FALSE)
result <- wtd.quantile(data$SIRI, weights = data$WTMEC, probs = c(0, 0.5,1))
data$SIRI_2 = cut(data$SIRI, breaks = c(0,result[2], Inf), labels = c("Quantile1","Quantile2"), right=FALSE)
result <- wtd.quantile(data$SII, weights = data$WTMEC, probs = c(0, 0.5,1))
data$SII_2 = cut(data$SII, breaks = c(0,result[2], Inf), labels = c("Quantile1","Quantile2"), right=FALSE)

#用MLR_log计算出的HR过大(因为MLR的值太小了)，所以对其进行标准化处理
data$MLR_log = rescale(data$MLR_log, to=c(0,5))
data_design <- svydesign(ids=~SDMVPSU,   #集群，抽样单元PSU
                         strata=~SDMVSTRA,   #分层
                         weights=~WTMEC,
                         nest=TRUE,
                         survey.lonely.psu="adjust",  #抽样单元为1时不报错
                         data=data)


dd<-datadist(data)
options(datadist='dd')

#分析GGT变量
result1 = Cox_analysise(variable="GGT_log", status="Allcausemortstat",design=data_design)#分析GGT变量(连续)与量全因死亡率的关系
result2 = Cox_analysise(variable="GGT_log", status="CVDmortstat",design=data_design)#分析GGT变量(连续)与量CVD死亡率的关系
result3 = Cox_analysise(variable="GGT_log", status="CANSERmortstat",design=data_design)#分析GGT变量(连续)与量CANBSER死亡率的关系
Table4[nrow(Table4) + 1,] <- c("GGT",result1[1],result1[2],result2[1],result2[2],result3[1],result3[2])

Table4[nrow(Table4) + 1,] <- line1_below

result1 = Cox_analysise(variable="GGT_2", status="Allcausemortstat",design=data_design)#分析GGT变量(分类)与量全因死亡率的关系
result2 = Cox_analysise(variable="GGT_2", status="CVDmortstat",design=data_design)#分析GGT变量(分类)与量CVD死亡率的关系
result3 = Cox_analysise(variable="GGT_2", status="CANSERmortstat",design=data_design)#分析GGT变量(分类)与量CANBSER死亡率的关系
Table4[nrow(Table4) + 1,] <- c("Above",result1[1],result1[2],result2[1],result2[2],result3[1],result3[2])

#分析UA变量
result1 = Cox_analysise(variable="UA_log", status="Allcausemortstat",design=data_design)#分析UA变量(连续)与量全因死亡率的关系
result2 = Cox_analysise(variable="UA_log", status="CVDmortstat",design=data_design)#分析UA变量(连续)与量CVD死亡率的关系
result3 = Cox_analysise(variable="UA_log", status="CANSERmortstat",design=data_design)#分析UA变量(连续)与量CANBSER死亡率的关系
Table4[nrow(Table4) + 1,] <- c("UA",result1[1],result1[2],result2[1],result2[2],result3[1],result3[2])

Table4[nrow(Table4) + 1,] <- line1_below

result1 = Cox_analysise(variable="UA_2", status="Allcausemortstat",design=data_design)#分析UA变量(分类)与量全因死亡率的关系
result2 = Cox_analysise(variable="UA_2", status="CVDmortstat",design=data_design)#分析UA变量(分类)与量CVD死亡率的关系
result3 = Cox_analysise(variable="UA_2", status="CANSERmortstat",design=data_design)#分析UA变量(分类)与量CANBSER死亡率的关系
Table4[nrow(Table4) + 1,] <- c("Above",result1[1],result1[2],result2[1],result2[2],result3[1],result3[2])

#分析HDL变量
result1 = Cox_analysise(variable="HDL_log", status="Allcausemortstat",design=data_design)#分析HDL变量(连续)与量全因死亡率的关系
result2 = Cox_analysise(variable="HDL_log", status="CVDmortstat",design=data_design)#分析HDL变量(连续)与量CVD死亡率的关系
result3 = Cox_analysise(variable="HDL_log", status="CANSERmortstat",design=data_design)#分析HDL变量(连续)与量CANBSER死亡率的关系
Table4[nrow(Table4) + 1,] <- c("HDL",result1[1],result1[2],result2[1],result2[2],result3[1],result3[2])

Table4[nrow(Table4) + 1,] <- line1_below

result1 = Cox_analysise(variable="HDL_2", status="Allcausemortstat",design=data_design)#分析HDL变量(分类)与量全因死亡率的关系
result2 = Cox_analysise(variable="HDL_2", status="CVDmortstat",design=data_design)#分析HDL变量(分类)与量CVD死亡率的关系
result3 = Cox_analysise(variable="HDL_2", status="CANSERmortstat",design=data_design)#分析HDL变量(分类)与量CANBSER死亡率的关系
Table4[nrow(Table4) + 1,] <- c("Above",result1[1],result1[2],result2[1],result2[2],result3[1],result3[2])

#分析UHR变量
result1 = Cox_analysise(variable="UHR_log", status="Allcausemortstat",design=data_design)#分析UHR变量(连续)与量全因死亡率的关系
result2 = Cox_analysise(variable="UHR_log", status="CVDmortstat",design=data_design)#分析UHR变量(连续)与量CVD死亡率的关系
result3 = Cox_analysise(variable="UHR_log", status="CANSERmortstat",design=data_design)#分析UHR变量(连续)与量CANBSER死亡率的关系
Table4[nrow(Table4) + 1,] <- c("UHR",result1[1],result1[2],result2[1],result2[2],result3[1],result3[2])

Table4[nrow(Table4) + 1,] <- line1_below

result1 = Cox_analysise(variable="UHR_2", status="Allcausemortstat",design=data_design)#分析UHR变量(分类)与量全因死亡率的关系
result2 = Cox_analysise(variable="UHR_2", status="CVDmortstat",design=data_design)#分析UHR变量(分类)与量CVD死亡率的关系
result3 = Cox_analysise(variable="UHR_2", status="CANSERmortstat",design=data_design)#分析UHR变量(分类)与量CANBSER死亡率的关系
Table4[nrow(Table4) + 1,] <- c("Above",result1[1],result1[2],result2[1],result2[2],result3[1],result3[2])

#分析NLR变量
result1 = Cox_analysise(variable="NLR_log", status="Allcausemortstat",design=data_design)#分析NLR变量(连续)与量全因死亡率的关系
result2 = Cox_analysise(variable="NLR_log", status="CVDmortstat",design=data_design)#分析NLR变量(连续)与量CVD死亡率的关系
result3 = Cox_analysise(variable="NLR_log", status="CANSERmortstat",design=data_design)#分析NLR变量(连续)与量CANBSER死亡率的关系
Table4[nrow(Table4) + 1,] <- c("NLR",result1[1],result1[2],result2[1],result2[2],result3[1],result3[2])

Table4[nrow(Table4) + 1,] <- line1_below

result1 = Cox_analysise(variable="NLR_2", status="Allcausemortstat",design=data_design)#分析NLR变量(分类)与量全因死亡率的关系
result2 = Cox_analysise(variable="NLR_2", status="CVDmortstat",design=data_design)#分析NLR变量(分类)与量CVD死亡率的关系
result3 = Cox_analysise(variable="NLR_2", status="CANSERmortstat",design=data_design)#分析NLR变量(分类)与量CANBSER死亡率的关系
Table4[nrow(Table4) + 1,] <- c("Above",result1[1],result1[2],result2[1],result2[2],result3[1],result3[2])

#分析MLR变量
result1 = Cox_analysise(variable="MLR_log", status="Allcausemortstat",design=data_design)#分析MLR变量(连续)与量全因死亡率的关系
result2 = Cox_analysise(variable="MLR_log", status="CVDmortstat",design=data_design)#分析MLR变量(连续)与量CVD死亡率的关系
result3 = Cox_analysise(variable="MLR_log", status="CANSERmortstat",design=data_design)#分析MLR变量(连续)与量CANBSER死亡率的关系
Table4[nrow(Table4) + 1,] <- c("MLR",result1[1],result1[2],result2[1],result2[2],result3[1],result3[2])

Table4[nrow(Table4) + 1,] <- line1_below

result1 = Cox_analysise(variable="MLR_2", status="Allcausemortstat",design=data_design)#分析MLR变量(分类)与量全因死亡率的关系
result2 = Cox_analysise(variable="MLR_2", status="CVDmortstat",design=data_design)#分析MLR变量(分类)与量CVD死亡率的关系
result3 = Cox_analysise(variable="MLR_2", status="CANSERmortstat",design=data_design)#分析MLR变量(分类)与量CANBSER死亡率的关系
Table4[nrow(Table4) + 1,] <- c("Above",result1[1],result1[2],result2[1],result2[2],result3[1],result3[2])

#分析NMLR变量
result1 = Cox_analysise(variable="NMLR_log", status="Allcausemortstat",design=data_design)#分析NMLR变量(连续)与量全因死亡率的关系
result2 = Cox_analysise(variable="NMLR_log", status="CVDmortstat",design=data_design)#分析NMLR变量(连续)与量CVD死亡率的关系
result3 = Cox_analysise(variable="NMLR_log", status="CANSERmortstat",design=data_design)#分析NMLR变量(连续)与量CANBSER死亡率的关系
Table4[nrow(Table4) + 1,] <- c("NMLR",result1[1],result1[2],result2[1],result2[2],result3[1],result3[2])

Table4[nrow(Table4) + 1,] <- line1_below

result1 = Cox_analysise(variable="NMLR_2", status="Allcausemortstat",design=data_design)#分析NMLR变量(分类)与量全因死亡率的关系
result2 = Cox_analysise(variable="NMLR_2", status="CVDmortstat",design=data_design)#分析NMLR变量(分类)与量CVD死亡率的关系
result3 = Cox_analysise(variable="NMLR_2", status="CANSERmortstat",design=data_design)#分析NMLR变量(分类)与量CANBSER死亡率的关系
Table4[nrow(Table4) + 1,] <- c("Above",result1[1],result1[2],result2[1],result2[2],result3[1],result3[2])

#分析SIRI变量
result1 = Cox_analysise(variable="SIRI_log", status="Allcausemortstat",design=data_design)#分析SIRI变量(连续)与量全因死亡率的关系
result2 = Cox_analysise(variable="SIRI_log", status="CVDmortstat",design=data_design)#分析SIRI变量(连续)与量CVD死亡率的关系
result3 = Cox_analysise(variable="SIRI_log", status="CANSERmortstat",design=data_design)#分析SIRI变量(连续)与量CANBSER死亡率的关系
Table4[nrow(Table4) + 1,] <- c("SIRI",result1[1],result1[2],result2[1],result2[2],result3[1],result3[2])

Table4[nrow(Table4) + 1,] <- line1_below

result1 = Cox_analysise(variable="SIRI_2", status="Allcausemortstat",design=data_design)#分析SIRI变量(分类)与量全因死亡率的关系
result2 = Cox_analysise(variable="SIRI_2", status="CVDmortstat",design=data_design)#分析SIRI变量(分类)与量CVD死亡率的关系
result3 = Cox_analysise(variable="SIRI_2", status="CANSERmortstat",design=data_design)#分析SIRI变量(分类)与量CANBSER死亡率的关系
Table4[nrow(Table4) + 1,] <- c("Above",result1[1],result1[2],result2[1],result2[2],result3[1],result3[2])

#分析SII变量
result1 = Cox_analysise(variable="SII_log", status="Allcausemortstat",design=data_design)#分析SII变量(连续)与量全因死亡率的关系
result2 = Cox_analysise(variable="SII_log", status="CVDmortstat",design=data_design)#分析SII变量(连续)与量CVD死亡率的关系
result3 = Cox_analysise(variable="SII_log", status="CANSERmortstat",design=data_design)#分析SII变量(连续)与量CANBSER死亡率的关系
Table4[nrow(Table4) + 1,] <- c("SII",result1[1],result1[2],result2[1],result2[2],result3[1],result3[2])

Table4[nrow(Table4) + 1,] <- line1_below

result1 = Cox_analysise(variable="SII_2", status="Allcausemortstat",design=data_design)#分析SII变量(分类)与量全因死亡率的关系
result2 = Cox_analysise(variable="SII_2", status="CVDmortstat",design=data_design)#分析SII变量(分类)与量CVD死亡率的关系
result3 = Cox_analysise(variable="SII_2", status="CANSERmortstat",design=data_design)#分析SII变量(分类)与量CANBSER死亡率的关系
Table4[nrow(Table4) + 1,] <- c("Above",result1[1],result1[2],result2[1],result2[2],result3[1],result3[2])


# 保存为 CSV 格式文件
write.csv(Table4, file = "Table4.csv")





#绘制图S4的rcs曲线图，9个变量和3个死亡率分别绘制
#画图S4-GGT变量-Allcausemortstat
#构建生存模型
#构建带权重的生存模型
fit_svy<- svycoxph(Surv(permth_int, Allcausemortstat) ~ rcs(GGT_log,4) + Age + Sex + Race + Educationlevel + 
                     PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
                     Alcoholstatus+ Failingkidneys + Hypertension, design=data_design)
#weight变量是权重
fit<- cph(Surv(permth_int, Allcausemortstat) ~ rcs(GGT_log,4) + Age + Sex + Race + Educationlevel + 
            PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
            Alcoholstatus+ Failingkidneys + Hypertension, x=TRUE, y=TRUE,data=data, weights = (1/fit_svy$survey.design$prob)/mean(1/fit_svy$survey.design$prob))
#计算预测值
Pre0<-rms::Predict(fit,GGT_log=data$GGT_log,fun=exp,type="predictions",ref.zero=T,conf.int= 0.95,digits=2)
Pre0<-as.data.frame(Pre0)#预测值转换为df必须转换，不然作不了图

#计算p overall和p nonliner
fit_p = anova(fit)
fit_over = format.pval(fit_p[1,3],digit=3,eps=0.001)
fit_nonl = format.pval(fit_p[2,3],digit=3,eps=0.001)

#计算直方图的密度，按照0.1的宽度进划分区间，并计算区间的最大密度和最小密度，用于绘制坐标轴
d<-density(data$GGT_log, n=as.integer((max(data$GGT_log) - min(data$GGT_log)) / 0.1))
dmin<-as.numeric(min(d[["y"]])) / sum(d[["y"]])##density
dmax<-as.numeric(max(d[["y"]])) / sum(d[["y"]])##density
CI_upper = max(Pre0$upper)

#画图
p = ggplot(Pre0,aes(x=GGT_log))+
  geom_histogram(aes(x=GGT_log,y =rescale(after_stat(density),c(0,CI_upper))),binwidth = 0.1,fill="#FCC060",colour="white")+#画直方图
  geom_line(data=Pre0,aes(GGT_log,yhat),linetype=1,linewidth=1,alpha = 0.9,colour="red")+#画HR曲线
  geom_ribbon(data=Pre0,aes(ymin = lower, ymax = upper),linetype=2,alpha = 0.3,fill="grey",colour="red")+#画置信区间
  geom_hline(yintercept=1, linetype="dashed",linewidth=1)+#在HR曲线与y=1交点画一条y轴的红色虚线
  theme_bw()+
  scale_x_continuous(n.breaks = 6,labels = label_number(accuracy = 0.01))+#设置x轴对数坐标
  xlim(min(data$GGT_log), max(data$GGT_log))+#设置x轴范围
  theme(panel.grid.major = element_blank(),panel.grid.minor = element_blank(),
        axis.text.x = element_text(size=14),axis.text.y = element_text(size=14),
        axis.title.x = element_text(size=14),axis.title.y = element_text(size=14))+#设置网格，字体大小
  xlab("Ln(GGT)")+ylab("Hazard ratio(95%CI)")+#设置x和y轴标签
  ggtitle('GGT All-cause mortality')+#添加标题
  annotate(geom="text",x=min(data$GGT_log),y=CI_upper,label=paste('P for overall',fit_over,'\nP for nonlinearity',fit_nonl),hjust=-0.1,size=5,color="black")+#添加文字
  scale_y_continuous(sec.axis = sec_axis( ~rescale(.,c(dmin,dmax)), name = "Density, %"))#添加右侧坐标轴

#保存图片为pdf
pdf("图s4-1.pdf")
p
dev.off()

#画图S4-GGT变量-CVDmortstat
#构建生存模型
#构建带权重的生存模型
fit_svy<- svycoxph(Surv(permth_int, CVDmortstat) ~ rcs(GGT_log,4) + Age + Sex + Race + Educationlevel + 
                     PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
                     Alcoholstatus+ Failingkidneys + Hypertension, design=data_design)
#weight变量是权重
fit<- cph(Surv(permth_int, CVDmortstat) ~ rcs(GGT_log,4) + Age + Sex + Race + Educationlevel + 
            PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
            Alcoholstatus+ Failingkidneys + Hypertension, x=TRUE, y=TRUE,data=data, weights = (1/fit_svy$survey.design$prob)/mean(1/fit_svy$survey.design$prob))
#计算预测值
Pre0<-rms::Predict(fit,GGT_log=data$GGT_log,fun=exp,type="predictions",ref.zero=T,conf.int= 0.95,digits=2)
Pre0<-as.data.frame(Pre0)#预测值转换为df必须转换，不然作不了图

#计算p overall和p nonliner
fit_p = anova(fit)
fit_over = format.pval(fit_p[1,3],digit=3,eps=0.001)
fit_nonl = format.pval(fit_p[2,3],digit=3,eps=0.001)

#计算直方图的密度，按照0.1的宽度进划分区间，并计算区间的最大密度和最小密度，用于绘制坐标轴
# d = data.frame(table(cut(data$GGT_log, breaks=seq(min(data$GGT_log), max(data$GGT_log), by=0.2))))
# d$density = d$Freq / sum(d$Freq)
d<-density(data$GGT_log, n=as.integer((max(data$GGT_log) - min(data$GGT_log)) / 0.1),bw=0.1)
dmin<-as.numeric(min(d[["y"]])) / sum(d[["y"]])##density
dmax<-as.numeric(max(d[["y"]])) / sum(d[["y"]])##density
CI_upper = max(Pre0$upper)

#画图
p = ggplot(Pre0,aes(x=GGT_log))+
  geom_histogram(aes(x=GGT_log,y =rescale(after_stat(density),c(0,CI_upper))),binwidth = 0.1,fill="#FCC060",colour="white")+#画直方图
  geom_line(data=Pre0,aes(GGT_log,yhat),linetype=1,linewidth=1,alpha = 0.9,colour="red")+#画HR曲线
  geom_ribbon(data=Pre0,aes(ymin = lower, ymax = upper),linetype=2,alpha = 0.3,fill="grey",colour="red")+#画置信区间
  geom_hline(yintercept=1, linetype="dashed",linewidth=1)+#在HR曲线与y=1交点画一条y轴的红色虚线
  theme_bw()+
  scale_x_continuous(n.breaks = 6,labels = label_number(accuracy = 0.01))+#设置x轴
  xlim(min(data$GGT_log), max(data$GGT_log))+
  theme(panel.grid.major = element_blank(),panel.grid.minor = element_blank(),
        axis.text.x = element_text(size=14),axis.text.y = element_text(size=14),
        axis.title.x = element_text(size=14),axis.title.y = element_text(size=14))+#设置网格，字体大小
  xlab("Ln(GGT)")+ylab("Hazard ratio(95%CI)")+#设置x和y轴标签
  ggtitle('GGT CVD mortality')+#添加标题
  annotate(geom="text",x=min(data$GGT_log),y=CI_upper,label=paste('P for overall',fit_over,'\nP for nonlinearity',fit_nonl),hjust=-0.1,size=5,color="black")+#添加文字
  scale_y_continuous(sec.axis = sec_axis( ~rescale(.,c(dmin,dmax)), name = "Density, %"))#添加右侧坐标轴

#保存图片为pdf
pdf("图s4-2.pdf")
p
dev.off()

#画图S4-GGT变量-CANSERmortstat
#构建生存模型
#构建带权重的生存模型
fit_svy<- svycoxph(Surv(permth_int, CANSERmortstat) ~ rcs(GGT_log,4) + Age + Sex + Race + Educationlevel + 
                     PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
                     Alcoholstatus+ Failingkidneys + Hypertension, design=data_design)
#weight变量是权重
fit<- cph(Surv(permth_int, CANSERmortstat) ~ rcs(GGT_log,4) + Age + Sex + Race + Educationlevel + 
            PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
            Alcoholstatus+ Failingkidneys + Hypertension, x=TRUE, y=TRUE,data=data, weights = (1/fit_svy$survey.design$prob)/mean(1/fit_svy$survey.design$prob))
#计算预测值
Pre0<-rms::Predict(fit,GGT_log=data$GGT_log,fun=exp,type="predictions",ref.zero=T,conf.int= 0.95,digits=2)
Pre0<-as.data.frame(Pre0)#预测值转换为df必须转换，不然作不了图

#计算p overall和p nonliner
fit_p = anova(fit)
fit_over = format.pval(fit_p[1,3],digit=3,eps=0.001)
fit_nonl = format.pval(fit_p[2,3],digit=3,eps=0.001)

#计算直方图的密度，按照0.1的宽度进划分区间，并计算区间的最大密度和最小密度，用于绘制坐标轴
d<-density(data$GGT_log, n=as.integer((max(data$GGT_log) - min(data$GGT_log)) / 0.1))
dmin<-as.numeric(min(d[["y"]])) / sum(d[["y"]])##density
dmax<-as.numeric(max(d[["y"]])) / sum(d[["y"]])##density
CI_upper = max(Pre0$upper)

#画图
p = ggplot(Pre0,aes(x=GGT_log))+
  geom_histogram(aes(x=GGT_log,y =rescale(after_stat(density),c(0,CI_upper))),binwidth = 0.1,fill="#FCC060",colour="white")+#画直方图
  geom_line(data=Pre0,aes(GGT_log,yhat),linetype=1,linewidth=1,alpha = 0.9,colour="red")+#画HR曲线
  geom_ribbon(data=Pre0,aes(ymin = lower, ymax = upper),linetype=2,alpha = 0.3,fill="grey",colour="red")+#画置信区间
  geom_hline(yintercept=1, linetype="dashed",linewidth=1)+#在HR曲线与y=1交点画一条y轴的红色虚线
  theme_bw()+
  scale_x_continuous(n.breaks = 6,labels = label_number(accuracy = 0.01))+#设置x轴对数坐标
  xlim(min(data$GGT_log), max(data$GGT_log))+
  theme(panel.grid.major = element_blank(),panel.grid.minor = element_blank(),
        axis.text.x = element_text(size=14),axis.text.y = element_text(size=14),
        axis.title.x = element_text(size=14),axis.title.y = element_text(size=14))+#设置网格，字体大小
  xlab("Ln(GGT)")+ylab("Hazard ratio(95%CI)")+#设置x和y轴标签
  ggtitle('GGT CANSER mortality')+#添加标题
  annotate(geom="text",x=min(data$GGT_log),y=CI_upper,label=paste('P for overall',fit_over,'\nP for nonlinearity',fit_nonl),hjust=-0.1,size=5,color="black")+#添加文字
  scale_y_continuous(sec.axis = sec_axis( ~rescale(.,c(dmin,dmax)), name = "Density, %"))#添加右侧坐标轴

#保存图片为pdf
pdf("图s4-3.pdf")
p
dev.off()

#其余变量绘制方法同理，区别就是变量名称
#画图S4-UA变量-Allcausemortstat
#构建生存模型
#构建带权重的生存模型
fit_svy<- svycoxph(Surv(permth_int, Allcausemortstat) ~ rcs(UA_log,4) + Age + Sex + Race + Educationlevel + 
                     PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
                     Alcoholstatus+ Failingkidneys + Hypertension, design=data_design)
#weight变量是权重
fit<- cph(Surv(permth_int, Allcausemortstat) ~ rcs(UA_log,4) + Age + Sex + Race + Educationlevel + 
            PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
            Alcoholstatus+ Failingkidneys + Hypertension, x=TRUE, y=TRUE,data=data, weights = (1/fit_svy$survey.design$prob)/mean(1/fit_svy$survey.design$prob))
#计算预测值
Pre0<-rms::Predict(fit,UA_log=data$UA_log,fun=exp,type="predictions",ref.zero=T,conf.int= 0.95,digits=2)
Pre0<-as.data.frame(Pre0)#预测值转换为df必须转换，不然作不了图

#计算p overall和p nonliner
fit_p = anova(fit)
fit_over = format.pval(fit_p[1,3],digit=3,eps=0.001)
fit_nonl = format.pval(fit_p[2,3],digit=3,eps=0.001)

#计算直方图的密度，按照0.1的宽度进划分区间，并计算区间的最大密度和最小密度，用于绘制坐标轴
d<-density(data$UA_log, n=as.integer((max(data$UA_log) - min(data$UA_log)) / 0.1))
dmin<-as.numeric(min(d[["y"]])) / sum(d[["y"]])##density
dmax<-as.numeric(max(d[["y"]])) / sum(d[["y"]])##density
CI_upper = max(Pre0$upper)

#画图
p = ggplot(Pre0,aes(x=UA_log))+
  geom_histogram(aes(x=UA_log,y =rescale(after_stat(density),c(0,CI_upper))),binwidth = 0.1,fill="#FCC060",colour="white")+#画直方图
  geom_line(data=Pre0,aes(UA_log,yhat),linetype=1,linewidth=1,alpha = 0.9,colour="red")+#画HR曲线
  geom_ribbon(data=Pre0,aes(ymin = lower, ymax = upper),linetype=2,alpha = 0.3,fill="grey",colour="red")+#画置信区间
  geom_hline(yintercept=1, linetype="dashed",linewidth=1)+#在HR曲线与y=1交点画一条y轴的红色虚线
  theme_bw()+
  scale_x_continuous(n.breaks = 6,labels = label_number(accuracy = 0.01))+#设置x轴对数坐标
  xlim(min(data$UA_log), max(data$UA_log))+
  theme(panel.grid.major = element_blank(),panel.grid.minor = element_blank(),
        axis.text.x = element_text(size=14),axis.text.y = element_text(size=14),
        axis.title.x = element_text(size=14),axis.title.y = element_text(size=14))+#设置网格，字体大小
  xlab("Ln(UA)")+ylab("Hazard ratio(95%CI)")+#设置x和y轴标签
  ggtitle('UA All-cause mortality')+#添加标题
  annotate(geom="text",x=min(data$UA_log),y=CI_upper,label=paste('P for overall',fit_over,'\nP for nonlinearity',fit_nonl),hjust=-0.1,size=5,color="black")+#添加文字
  scale_y_continuous(sec.axis = sec_axis( ~rescale(.,c(dmin,dmax)), name = "Density, %"))#添加右侧坐标轴


#保存图片为pdf
pdf("图s4-4.pdf")
p
dev.off()

#画图S4-UA变量-CVDmortstat
#构建生存模型
#构建带权重的生存模型
fit_svy<- svycoxph(Surv(permth_int, CVDmortstat) ~ rcs(UA_log,4) + Age + Sex + Race + Educationlevel + 
                     PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
                     Alcoholstatus+ Failingkidneys + Hypertension, design=data_design)
#weight变量是权重
fit<- cph(Surv(permth_int, CVDmortstat) ~ rcs(UA_log,4) + Age + Sex + Race + Educationlevel + 
            PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
            Alcoholstatus+ Failingkidneys + Hypertension, x=TRUE, y=TRUE,data=data, weights = (1/fit_svy$survey.design$prob)/mean(1/fit_svy$survey.design$prob))
#计算预测值
Pre0<-rms::Predict(fit,UA_log=data$UA_log,fun=exp,type="predictions",ref.zero=T,conf.int= 0.95,digits=2)
Pre0<-as.data.frame(Pre0)#预测值转换为df必须转换，不然作不了图

#计算p overall和p nonliner
fit_p = anova(fit)
fit_over = format.pval(fit_p[1,3],digit=3,eps=0.001)
fit_nonl = format.pval(fit_p[2,3],digit=3,eps=0.001)

#计算直方图的密度，按照0.1的宽度进划分区间，并计算区间的最大密度和最小密度，用于绘制坐标轴
# d = data.frame(table(cut(data$UA_log, breaks=seq(min(data$UA_log), max(data$UA_log), by=0.2))))
# d$density = d$Freq / sum(d$Freq)
d<-density(data$UA_log, n=as.integer((max(data$UA_log) - min(data$UA_log)) / 0.1),bw=0.1)
dmin<-as.numeric(min(d[["y"]])) / sum(d[["y"]])##density
dmax<-as.numeric(max(d[["y"]])) / sum(d[["y"]])##density
CI_upper = max(Pre0$upper)

#画图
p = ggplot(Pre0,aes(x=UA_log))+
  geom_histogram(aes(x=UA_log,y =rescale(after_stat(density),c(0,CI_upper))),binwidth = 0.1,fill="#FCC060",colour="white")+#画直方图
  geom_line(data=Pre0,aes(UA_log,yhat),linetype=1,linewidth=1,alpha = 0.9,colour="red")+#画HR曲线
  geom_ribbon(data=Pre0,aes(ymin = lower, ymax = upper),linetype=2,alpha = 0.3,fill="grey",colour="red")+#画置信区间
  geom_hline(yintercept=1, linetype="dashed",linewidth=1)+#在HR曲线与y=1交点画一条y轴的红色虚线
  theme_bw()+
  scale_x_continuous(n.breaks = 6,labels = label_number(accuracy = 0.01))+#设置x轴
  xlim(min(data$UA_log), max(data$UA_log))+
  theme(panel.grid.major = element_blank(),panel.grid.minor = element_blank(),
        axis.text.x = element_text(size=14),axis.text.y = element_text(size=14),
        axis.title.x = element_text(size=14),axis.title.y = element_text(size=14))+#设置网格，字体大小
  xlab("Ln(UA)")+ylab("Hazard ratio(95%CI)")+#设置x和y轴标签
  ggtitle('UA CVD mortality')+#添加标题
  annotate(geom="text",x=min(data$UA_log),y=CI_upper,label=paste('P for overall',fit_over,'\nP for nonlinearity',fit_nonl),hjust=-0.1,size=5,color="black")+#添加文字
  scale_y_continuous(sec.axis = sec_axis( ~rescale(.,c(dmin,dmax)), name = "Density, %"))#添加右侧坐标轴

#保存图片为pdf
pdf("图s4-5.pdf")
p
dev.off()


#画图S4-UA变量-CANSERmortstat
#构建生存模型
#构建带权重的生存模型
fit_svy<- svycoxph(Surv(permth_int, CANSERmortstat) ~ rcs(UA_log,4) + Age + Sex + Race + Educationlevel + 
                     PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
                     Alcoholstatus+ Failingkidneys + Hypertension, design=data_design)
#weight变量是权重
fit<- cph(Surv(permth_int, CANSERmortstat) ~ rcs(UA_log,4) + Age + Sex + Race + Educationlevel + 
            PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
            Alcoholstatus+ Failingkidneys + Hypertension, x=TRUE, y=TRUE,data=data, weights = (1/fit_svy$survey.design$prob)/mean(1/fit_svy$survey.design$prob))
#计算预测值
Pre0<-rms::Predict(fit,UA_log=data$UA_log,fun=exp,type="predictions",ref.zero=T,conf.int= 0.95,digits=2)
Pre0<-as.data.frame(Pre0)#预测值转换为df必须转换，不然作不了图

#计算p overall和p nonliner
fit_p = anova(fit)
fit_over = format.pval(fit_p[1,3],digit=3,eps=0.001)
fit_nonl = format.pval(fit_p[2,3],digit=3,eps=0.001)

#计算直方图的密度，按照0.1的宽度进划分区间，并计算区间的最大密度和最小密度，用于绘制坐标轴
d<-density(data$UA_log, n=as.integer((max(data$UA_log) - min(data$UA_log)) / 0.1))
dmin<-as.numeric(min(d[["y"]])) / sum(d[["y"]])##density
dmax<-as.numeric(max(d[["y"]])) / sum(d[["y"]])##density
CI_upper = max(Pre0$upper)

#画图
p = ggplot(Pre0,aes(x=UA_log))+
  geom_histogram(aes(x=UA_log,y =rescale(after_stat(density),c(0,CI_upper))),binwidth = 0.1,fill="#FCC060",colour="white")+#画直方图
  geom_line(data=Pre0,aes(UA_log,yhat),linetype=1,linewidth=1,alpha = 0.9,colour="red")+#画HR曲线
  geom_ribbon(data=Pre0,aes(ymin = lower, ymax = upper),linetype=2,alpha = 0.3,fill="grey",colour="red")+#画置信区间
  geom_hline(yintercept=1, linetype="dashed",linewidth=1)+#在HR曲线与y=1交点画一条y轴的红色虚线
  theme_bw()+
  scale_x_continuous(n.breaks = 6,labels = label_number(accuracy = 0.01))+#设置x轴对数坐标
  xlim(min(data$UA_log), max(data$UA_log))+
  theme(panel.grid.major = element_blank(),panel.grid.minor = element_blank(),
        axis.text.x = element_text(size=14),axis.text.y = element_text(size=14),
        axis.title.x = element_text(size=14),axis.title.y = element_text(size=14))+#设置网格，字体大小
  xlab("Ln(UA)")+ylab("Hazard ratio(95%CI)")+#设置x和y轴标签
  ggtitle('UA CANSER mortality')+#添加标题
  annotate(geom="text",x=min(data$UA_log),y=CI_upper,label=paste('P for overall',fit_over,'\nP for nonlinearity',fit_nonl),hjust=-0.1,size=5,color="black")+#添加文字
  scale_y_continuous(sec.axis = sec_axis( ~rescale(.,c(dmin,dmax)), name = "Density, %"))#添加右侧坐标轴

#保存图片为pdf
pdf("图s4-6.pdf")
p
dev.off()


#画图S4-HDL变量-Allcausemortstat
#构建生存模型
#构建带权重的生存模型
fit_svy<- svycoxph(Surv(permth_int, Allcausemortstat) ~ rcs(HDL_log,4) + Age + Sex + Race + Educationlevel + 
                     PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
                     Alcoholstatus+ Failingkidneys + Hypertension, design=data_design)
#weight变量是权重
fit<- cph(Surv(permth_int, Allcausemortstat) ~ rcs(HDL_log,4) + Age + Sex + Race + Educationlevel + 
            PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
            Alcoholstatus+ Failingkidneys + Hypertension, x=TRUE, y=TRUE,data=data, weights = (1/fit_svy$survey.design$prob)/mean(1/fit_svy$survey.design$prob))
#计算预测值
Pre0<-rms::Predict(fit,HDL_log=data$HDL_log,fun=exp,type="predictions",ref.zero=T,conf.int= 0.95,digits=2)
Pre0<-as.data.frame(Pre0)#预测值转换为df必须转换，不然作不了图

#计算p overall和p nonliner
fit_p = anova(fit)
fit_over = format.pval(fit_p[1,3],digit=3,eps=0.001)
fit_nonl = format.pval(fit_p[2,3],digit=3,eps=0.001)

#计算直方图的密度，按照0.1的宽度进划分区间，并计算区间的最大密度和最小密度，用于绘制坐标轴
d<-density(data$HDL_log, n=as.integer((max(data$HDL_log) - min(data$HDL_log)) / 0.1))
dmin<-as.numeric(min(d[["y"]])) / sum(d[["y"]])##density
dmax<-as.numeric(max(d[["y"]])) / sum(d[["y"]])##density
CI_upper = max(Pre0$upper)

#画图
p = ggplot(Pre0,aes(x=HDL_log))+
  geom_histogram(aes(x=HDL_log,y =rescale(after_stat(density),c(0,CI_upper))),binwidth = 0.1,fill="#FCC060",colour="white")+#画直方图
  geom_line(data=Pre0,aes(HDL_log,yhat),linetype=1,linewidth=1,alpha = 0.9,colour="red")+#画HR曲线
  geom_ribbon(data=Pre0,aes(ymin = lower, ymax = upper),linetype=2,alpha = 0.3,fill="grey",colour="red")+#画置信区间
  geom_hline(yintercept=1, linetype="dashed",linewidth=1)+#在HR曲线与y=1交点画一条y轴的红色虚线
  theme_bw()+
  scale_x_continuous(n.breaks = 6,labels = label_number(accuracy = 0.01))+#设置x轴对数坐标
  xlim(min(data$HDL_log), max(data$HDL_log))+
  theme(panel.grid.major = element_blank(),panel.grid.minor = element_blank(),
        axis.text.x = element_text(size=14),axis.text.y = element_text(size=14),
        axis.title.x = element_text(size=14),axis.title.y = element_text(size=14))+#设置网格，字体大小
  xlab("Ln(HDL)")+ylab("Hazard ratio(95%CI)")+#设置x和y轴标签
  ggtitle('HDL All-cause mortality')+#添加标题
  annotate(geom="text",x=min(data$HDL_log),y=CI_upper,label=paste('P for overall',fit_over,'\nP for nonlinearity',fit_nonl),hjust=-0.1,size=5,color="black")+#添加文字
  scale_y_continuous(sec.axis = sec_axis( ~rescale(.,c(dmin,dmax)), name = "Density, %"))#添加右侧坐标轴


#保存图片为pdf
pdf("图s4-7.pdf")
p
dev.off()


#画图S4-HDL变量-CVDmortstat
#构建生存模型
#构建带权重的生存模型
fit_svy<- svycoxph(Surv(permth_int, CVDmortstat) ~ rcs(HDL_log,4) + Age + Sex + Race + Educationlevel + 
                     PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
                     Alcoholstatus+ Failingkidneys + Hypertension, design=data_design)
#weight变量是权重
fit<- cph(Surv(permth_int, CVDmortstat) ~ rcs(HDL_log,4) + Age + Sex + Race + Educationlevel + 
            PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
            Alcoholstatus+ Failingkidneys + Hypertension, x=TRUE, y=TRUE,data=data, weights = (1/fit_svy$survey.design$prob)/mean(1/fit_svy$survey.design$prob))
#计算预测值
Pre0<-rms::Predict(fit,HDL_log=data$HDL_log,fun=exp,type="predictions",ref.zero=T,conf.int= 0.95,digits=2)
Pre0<-as.data.frame(Pre0)#预测值转换为df必须转换，不然作不了图

#计算p overall和p nonliner
fit_p = anova(fit)
fit_over = format.pval(fit_p[1,3],digit=3,eps=0.001)
fit_nonl = format.pval(fit_p[2,3],digit=3,eps=0.001)

#计算直方图的密度，按照0.1的宽度进划分区间，并计算区间的最大密度和最小密度，用于绘制坐标轴
# d = data.frame(table(cut(data$HDL_log, breaks=seq(min(data$HDL_log), max(data$HDL_log), by=0.2))))
# d$density = d$Freq / sum(d$Freq)
d<-density(data$HDL_log, n=as.integer((max(data$HDL_log) - min(data$HDL_log)) / 0.1),bw=0.1)
dmin<-as.numeric(min(d[["y"]])) / sum(d[["y"]])##density
dmax<-as.numeric(max(d[["y"]])) / sum(d[["y"]])##density
CI_upper = max(Pre0$upper)

#画图
p = ggplot(Pre0,aes(x=HDL_log))+
  geom_histogram(aes(x=HDL_log,y =rescale(after_stat(density),c(0,CI_upper))),binwidth = 0.1,fill="#FCC060",colour="white")+#画直方图
  geom_line(data=Pre0,aes(HDL_log,yhat),linetype=1,linewidth=1,alpha = 0.9,colour="red")+#画HR曲线
  geom_ribbon(data=Pre0,aes(ymin = lower, ymax = upper),linetype=2,alpha = 0.3,fill="grey",colour="red")+#画置信区间
  geom_hline(yintercept=1, linetype="dashed",linewidth=1)+#在HR曲线与y=1交点画一条y轴的红色虚线
  theme_bw()+
  scale_x_continuous(n.breaks = 6,labels = label_number(accuracy = 0.01))+#设置x轴
  xlim(min(data$HDL_log), max(data$HDL_log))+
  theme(panel.grid.major = element_blank(),panel.grid.minor = element_blank(),
        axis.text.x = element_text(size=14),axis.text.y = element_text(size=14),
        axis.title.x = element_text(size=14),axis.title.y = element_text(size=14))+#设置网格，字体大小
  xlab("Ln(HDL)")+ylab("Hazard ratio(95%CI)")+#设置x和y轴标签
  ggtitle('HDL CVD mortality')+#添加标题
  annotate(geom="text",x=min(data$HDL_log),y=CI_upper,label=paste('P for overall',fit_over,'\nP for nonlinearity',fit_nonl),hjust=-0.1,size=5,color="black")+#添加文字
  scale_y_continuous(sec.axis = sec_axis( ~rescale(.,c(dmin,dmax)), name = "Density, %"))#添加右侧坐标轴

#保存图片为pdf
pdf("图s4-8.pdf")
p
dev.off()



#画图S4-HDL变量-CANSERmortstat
#构建生存模型
#构建带权重的生存模型
fit_svy<- svycoxph(Surv(permth_int, CANSERmortstat) ~ rcs(HDL_log,4) + Age + Sex + Race + Educationlevel + 
                     PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
                     Alcoholstatus+ Failingkidneys + Hypertension, design=data_design)
#weight变量是权重
fit<- cph(Surv(permth_int, CANSERmortstat) ~ rcs(HDL_log,4) + Age + Sex + Race + Educationlevel + 
            PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
            Alcoholstatus+ Failingkidneys + Hypertension, x=TRUE, y=TRUE,data=data, weights = (1/fit_svy$survey.design$prob)/mean(1/fit_svy$survey.design$prob))
#计算预测值
Pre0<-rms::Predict(fit,HDL_log=data$HDL_log,fun=exp,type="predictions",ref.zero=T,conf.int= 0.95,digits=2)
Pre0<-as.data.frame(Pre0)#预测值转换为df必须转换，不然作不了图

#计算p overall和p nonliner
fit_p = anova(fit)
fit_over = format.pval(fit_p[1,3],digit=3,eps=0.001)
fit_nonl = format.pval(fit_p[2,3],digit=3,eps=0.001)

#计算直方图的密度，按照0.1的宽度进划分区间，并计算区间的最大密度和最小密度，用于绘制坐标轴
d<-density(data$HDL_log, n=as.integer((max(data$HDL_log) - min(data$HDL_log)) / 0.1))
dmin<-as.numeric(min(d[["y"]])) / sum(d[["y"]])##density
dmax<-as.numeric(max(d[["y"]])) / sum(d[["y"]])##density
CI_upper = max(Pre0$upper)

#画图
p = ggplot(Pre0,aes(x=HDL_log))+
  geom_histogram(aes(x=HDL_log,y =rescale(after_stat(density),c(0,CI_upper))),binwidth = 0.1,fill="#FCC060",colour="white")+#画直方图
  geom_line(data=Pre0,aes(HDL_log,yhat),linetype=1,linewidth=1,alpha = 0.9,colour="red")+#画HR曲线
  geom_ribbon(data=Pre0,aes(ymin = lower, ymax = upper),linetype=2,alpha = 0.3,fill="grey",colour="red")+#画置信区间
  geom_hline(yintercept=1, linetype="dashed",linewidth=1)+#在HR曲线与y=1交点画一条y轴的红色虚线
  theme_bw()+
  scale_x_continuous(n.breaks = 6,labels = label_number(accuracy = 0.01))+#设置x轴对数坐标
  xlim(min(data$HDL_log), max(data$HDL_log))+
  theme(panel.grid.major = element_blank(),panel.grid.minor = element_blank(),
        axis.text.x = element_text(size=14),axis.text.y = element_text(size=14),
        axis.title.x = element_text(size=14),axis.title.y = element_text(size=14))+#设置网格，字体大小
  xlab("Ln(HDL)")+ylab("Hazard ratio(95%CI)")+#设置x和y轴标签
  ggtitle('HDL CANSER mortality')+#添加标题
  annotate(geom="text",x=min(data$HDL_log),y=CI_upper,label=paste('P for overall',fit_over,'\nP for nonlinearity',fit_nonl),hjust=-0.1,size=5,color="black")+#添加文字
  scale_y_continuous(sec.axis = sec_axis( ~rescale(.,c(dmin,dmax)), name = "Density, %"))#添加右侧坐标轴


#保存图片为pdf
pdf("图s4-9.pdf")
p
dev.off()

#画图S4-UHR变量-Allcausemortstat
#构建生存模型
#构建带权重的生存模型
fit_svy<- svycoxph(Surv(permth_int, Allcausemortstat) ~ rcs(UHR_log,4) + Age + Sex + Race + Educationlevel + 
                     PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
                     Alcoholstatus+ Failingkidneys + Hypertension, design=data_design)
#weight变量是权重
fit<- cph(Surv(permth_int, Allcausemortstat) ~ rcs(UHR_log,4) + Age + Sex + Race + Educationlevel + 
            PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
            Alcoholstatus+ Failingkidneys + Hypertension, x=TRUE, y=TRUE,data=data, weights = (1/fit_svy$survey.design$prob)/mean(1/fit_svy$survey.design$prob))
#计算预测值
Pre0<-rms::Predict(fit,UHR_log=data$UHR_log,fun=exp,type="predictions",ref.zero=T,conf.int= 0.95,digits=2)
Pre0<-as.data.frame(Pre0)#预测值转换为df必须转换，不然作不了图

#计算p overall和p nonliner
fit_p = anova(fit)
fit_over = format.pval(fit_p[1,3],digit=3,eps=0.001)
fit_nonl = format.pval(fit_p[2,3],digit=3,eps=0.001)

#计算直方图的密度，按照0.1的宽度进划分区间，并计算区间的最大密度和最小密度，用于绘制坐标轴
d<-density(data$UHR_log, n=as.integer((max(data$UHR_log) - min(data$UHR_log)) / 0.1))
dmin<-as.numeric(min(d[["y"]])) / sum(d[["y"]])##density
dmax<-as.numeric(max(d[["y"]])) / sum(d[["y"]])##density
CI_upper = max(Pre0$upper)

#画图
p = ggplot(Pre0,aes(x=UHR_log))+
  geom_histogram(aes(x=UHR_log,y =rescale(after_stat(density),c(0,CI_upper))),binwidth = 0.1,fill="#FCC060",colour="white")+#画直方图
  geom_line(data=Pre0,aes(UHR_log,yhat),linetype=1,linewidth=1,alpha = 0.9,colour="red")+#画HR曲线
  geom_ribbon(data=Pre0,aes(ymin = lower, ymax = upper),linetype=2,alpha = 0.3,fill="grey",colour="red")+#画置信区间
  geom_hline(yintercept=1, linetype="dashed",linewidth=1)+#在HR曲线与y=1交点画一条y轴的红色虚线
  theme_bw()+
  scale_x_continuous(n.breaks = 6,labels = label_number(accuracy = 0.01))+#设置x轴对数坐标
  xlim(min(data$UHR_log), max(data$UHR_log))+
  theme(panel.grid.major = element_blank(),panel.grid.minor = element_blank(),
        axis.text.x = element_text(size=14),axis.text.y = element_text(size=14),
        axis.title.x = element_text(size=14),axis.title.y = element_text(size=14))+#设置网格，字体大小
  xlab("Ln(UHR)")+ylab("Hazard ratio(95%CI)")+#设置x和y轴标签
  ggtitle('UHR All-cause mortality')+#添加标题
  annotate(geom="text",x=min(data$UHR_log),y=CI_upper,label=paste('P for overall',fit_over,'\nP for nonlinearity',fit_nonl),hjust=-0.1,size=5,color="black")+#添加文字
  scale_y_continuous(sec.axis = sec_axis( ~rescale(.,c(dmin,dmax)), name = "Density, %"))#添加右侧坐标轴


#保存图片为pdf
pdf("图s4-10.pdf")
p
dev.off()


#画图S4-UHR变量-CVDmortstat
#构建生存模型
#构建带权重的生存模型
fit_svy<- svycoxph(Surv(permth_int, CVDmortstat) ~ rcs(UHR_log,4) + Age + Sex + Race + Educationlevel + 
                     PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
                     Alcoholstatus+ Failingkidneys + Hypertension, design=data_design)
#weight变量是权重
fit<- cph(Surv(permth_int, CVDmortstat) ~ rcs(UHR_log,4) + Age + Sex + Race + Educationlevel + 
            PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
            Alcoholstatus+ Failingkidneys + Hypertension, x=TRUE, y=TRUE,data=data, weights = (1/fit_svy$survey.design$prob)/mean(1/fit_svy$survey.design$prob))
#计算预测值
Pre0<-rms::Predict(fit,UHR_log=data$UHR_log,fun=exp,type="predictions",ref.zero=T,conf.int= 0.95,digits=2)
Pre0<-as.data.frame(Pre0)#预测值转换为df必须转换，不然作不了图

#计算p overall和p nonliner
fit_p = anova(fit)
fit_over = format.pval(fit_p[1,3],digit=3,eps=0.001)
fit_nonl = format.pval(fit_p[2,3],digit=3,eps=0.001)

#计算直方图的密度，按照0.1的宽度进划分区间，并计算区间的最大密度和最小密度，用于绘制坐标轴
# d = data.frame(table(cut(data$UHR_log, breaks=seq(min(data$UHR_log), max(data$UHR_log), by=0.2))))
# d$density = d$Freq / sum(d$Freq)
d<-density(data$UHR_log, n=as.integer((max(data$UHR_log) - min(data$UHR_log)) / 0.1),bw=0.1)
dmin<-as.numeric(min(d[["y"]])) / sum(d[["y"]])##density
dmax<-as.numeric(max(d[["y"]])) / sum(d[["y"]])##density
CI_upper = max(Pre0$upper)

#画图
p = ggplot(Pre0,aes(x=UHR_log))+
  geom_histogram(aes(x=UHR_log,y =rescale(after_stat(density),c(0,CI_upper))),binwidth = 0.1,fill="#FCC060",colour="white")+#画直方图
  geom_line(data=Pre0,aes(UHR_log,yhat),linetype=1,linewidth=1,alpha = 0.9,colour="red")+#画HR曲线
  geom_ribbon(data=Pre0,aes(ymin = lower, ymax = upper),linetype=2,alpha = 0.3,fill="grey",colour="red")+#画置信区间
  geom_hline(yintercept=1, linetype="dashed",linewidth=1)+#在HR曲线与y=1交点画一条y轴的红色虚线
  theme_bw()+
  scale_x_continuous(n.breaks = 6,labels = label_number(accuracy = 0.01))+#设置x轴
  xlim(min(data$UHR_log), max(data$UHR_log))+
  theme(panel.grid.major = element_blank(),panel.grid.minor = element_blank(),
        axis.text.x = element_text(size=14),axis.text.y = element_text(size=14),
        axis.title.x = element_text(size=14),axis.title.y = element_text(size=14))+#设置网格，字体大小
  xlab("Ln(UHR)")+ylab("Hazard ratio(95%CI)")+#设置x和y轴标签
  ggtitle('UHR CVD mortality')+#添加标题
  annotate(geom="text",x=min(data$UHR_log),y=CI_upper,label=paste('P for overall',fit_over,'\nP for nonlinearity',fit_nonl),hjust=-0.1,size=5,color="black")+#添加文字
  scale_y_continuous(sec.axis = sec_axis( ~rescale(.,c(dmin,dmax)), name = "Density, %"))#添加右侧坐标轴

#保存图片为pdf
pdf("图s4-11.pdf")
p
dev.off()


#画图S4-UHR变量-CANSERmortstat
#构建生存模型
#构建带权重的生存模型
fit_svy<- svycoxph(Surv(permth_int, CANSERmortstat) ~ rcs(UHR_log,4) + Age + Sex + Race + Educationlevel + 
                     PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
                     Alcoholstatus+ Failingkidneys + Hypertension, design=data_design)
#weight变量是权重
fit<- cph(Surv(permth_int, CANSERmortstat) ~ rcs(UHR_log,4) + Age + Sex + Race + Educationlevel + 
            PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
            Alcoholstatus+ Failingkidneys + Hypertension, x=TRUE, y=TRUE,data=data, weights = (1/fit_svy$survey.design$prob)/mean(1/fit_svy$survey.design$prob))
#计算预测值
Pre0<-rms::Predict(fit,UHR_log=data$UHR_log,fun=exp,type="predictions",ref.zero=T,conf.int= 0.95,digits=2)
Pre0<-as.data.frame(Pre0)#预测值转换为df必须转换，不然作不了图

#计算p overall和p nonliner
fit_p = anova(fit)
fit_over = format.pval(fit_p[1,3],digit=3,eps=0.001)
fit_nonl = format.pval(fit_p[2,3],digit=3,eps=0.001)

#计算直方图的密度，按照0.1的宽度进划分区间，并计算区间的最大密度和最小密度，用于绘制坐标轴
d<-density(data$UHR_log, n=as.integer((max(data$UHR_log) - min(data$UHR_log)) / 0.1))
dmin<-as.numeric(min(d[["y"]])) / sum(d[["y"]])##density
dmax<-as.numeric(max(d[["y"]])) / sum(d[["y"]])##density
CI_upper = max(Pre0$upper)

#画图
p = ggplot(Pre0,aes(x=UHR_log))+
  geom_histogram(aes(x=UHR_log,y =rescale(after_stat(density),c(0,CI_upper))),binwidth = 0.1,fill="#FCC060",colour="white")+#画直方图
  geom_line(data=Pre0,aes(UHR_log,yhat),linetype=1,linewidth=1,alpha = 0.9,colour="red")+#画HR曲线
  geom_ribbon(data=Pre0,aes(ymin = lower, ymax = upper),linetype=2,alpha = 0.3,fill="grey",colour="red")+#画置信区间
  geom_hline(yintercept=1, linetype="dashed",linewidth=1)+#在HR曲线与y=1交点画一条y轴的红色虚线
  theme_bw()+
  scale_x_continuous(n.breaks = 6,labels = label_number(accuracy = 0.01))+#设置x轴对数坐标
  xlim(min(data$UHR_log), max(data$UHR_log))+
  theme(panel.grid.major = element_blank(),panel.grid.minor = element_blank(),
        axis.text.x = element_text(size=14),axis.text.y = element_text(size=14),
        axis.title.x = element_text(size=14),axis.title.y = element_text(size=14))+#设置网格，字体大小
  xlab("Ln(UHR)")+ylab("Hazard ratio(95%CI)")+#设置x和y轴标签
  ggtitle('UHR CANSER mortality')+#添加标题
  annotate(geom="text",x=min(data$UHR_log),y=CI_upper,label=paste('P for overall',fit_over,'\nP for nonlinearity',fit_nonl),hjust=-0.1,size=5,color="black")+#添加文字
  scale_y_continuous(sec.axis = sec_axis( ~rescale(.,c(dmin,dmax)), name = "Density, %"))#添加右侧坐标轴

#保存图片为pdf
pdf("图s4-12.pdf")
p
dev.off()


#画图S4-NLR变量-Allcausemortstat
#构建生存模型
#构建带权重的生存模型
fit_svy<- svycoxph(Surv(permth_int, Allcausemortstat) ~ rcs(NLR_log,4) + Age + Sex + Race + Educationlevel + 
                     PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
                     Alcoholstatus+ Failingkidneys + Hypertension, design=data_design)
#weight变量是权重
fit<- cph(Surv(permth_int, Allcausemortstat) ~ rcs(NLR_log,4) + Age + Sex + Race + Educationlevel + 
            PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
            Alcoholstatus+ Failingkidneys + Hypertension, x=TRUE, y=TRUE,data=data, weights = (1/fit_svy$survey.design$prob)/mean(1/fit_svy$survey.design$prob))
#计算预测值
Pre0<-rms::Predict(fit,NLR_log=data$NLR_log,fun=exp,type="predictions",ref.zero=T,conf.int= 0.95,digits=2)
Pre0<-as.data.frame(Pre0)#预测值转换为df必须转换，不然作不了图

#计算p overall和p nonliner
fit_p = anova(fit)
fit_over = format.pval(fit_p[1,3],digit=3,eps=0.001)
fit_nonl = format.pval(fit_p[2,3],digit=3,eps=0.001)

#计算直方图的密度，按照0.1的宽度进划分区间，并计算区间的最大密度和最小密度，用于绘制坐标轴
d<-density(data$NLR_log, n=as.integer((max(data$NLR_log) - min(data$NLR_log)) / 0.1))
dmin<-as.numeric(min(d[["y"]])) / sum(d[["y"]])##density
dmax<-as.numeric(max(d[["y"]])) / sum(d[["y"]])##density
CI_upper = max(Pre0$upper)

#画图
p = ggplot(Pre0,aes(x=NLR_log))+
  geom_histogram(aes(x=NLR_log,y =rescale(after_stat(density),c(0,CI_upper))),binwidth = 0.1,fill="#FCC060",colour="white")+#画直方图
  geom_line(data=Pre0,aes(NLR_log,yhat),linetype=1,linewidth=1,alpha = 0.9,colour="red")+#画HR曲线
  geom_ribbon(data=Pre0,aes(ymin = lower, ymax = upper),linetype=2,alpha = 0.3,fill="grey",colour="red")+#画置信区间
  geom_hline(yintercept=1, linetype="dashed",linewidth=1)+#在HR曲线与y=1交点画一条y轴的红色虚线
  theme_bw()+
  scale_x_continuous(n.breaks = 6,labels = label_number(accuracy = 0.01))+#设置x轴对数坐标
  xlim(min(data$NLR_log), max(data$NLR_log))+
  theme(panel.grid.major = element_blank(),panel.grid.minor = element_blank(),
        axis.text.x = element_text(size=14),axis.text.y = element_text(size=14),
        axis.title.x = element_text(size=14),axis.title.y = element_text(size=14))+#设置网格，字体大小
  xlab("Ln(NLR)")+ylab("Hazard ratio(95%CI)")+#设置x和y轴标签
  ggtitle('NLR All-cause mortality')+#添加标题
  annotate(geom="text",x=min(data$NLR_log),y=CI_upper,label=paste('P for overall',fit_over,'\nP for nonlinearity',fit_nonl),hjust=-0.1,size=5,color="black")+#添加文字
  scale_y_continuous(sec.axis = sec_axis( ~rescale(.,c(dmin,dmax)), name = "Density, %"))#添加右侧坐标轴


#保存图片为pdf
pdf("图s4-13.pdf")
p
dev.off()


#画图S4-NLR变量-CVDmortstat
#构建生存模型
#构建带权重的生存模型
fit_svy<- svycoxph(Surv(permth_int, CVDmortstat) ~ rcs(NLR_log,4) + Age + Sex + Race + Educationlevel + 
                     PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
                     Alcoholstatus+ Failingkidneys + Hypertension, design=data_design)
#weight变量是权重
fit<- cph(Surv(permth_int, CVDmortstat) ~ rcs(NLR_log,4) + Age + Sex + Race + Educationlevel + 
            PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
            Alcoholstatus+ Failingkidneys + Hypertension, x=TRUE, y=TRUE,data=data, weights = (1/fit_svy$survey.design$prob)/mean(1/fit_svy$survey.design$prob))
#计算预测值
Pre0<-rms::Predict(fit,NLR_log=data$NLR_log,fun=exp,type="predictions",ref.zero=T,conf.int= 0.95,digits=2)
Pre0<-as.data.frame(Pre0)#预测值转换为df必须转换，不然作不了图

#计算p overall和p nonliner
fit_p = anova(fit)
fit_over = format.pval(fit_p[1,3],digit=3,eps=0.001)
fit_nonl = format.pval(fit_p[2,3],digit=3,eps=0.001)

#计算直方图的密度，按照0.1的宽度进划分区间，并计算区间的最大密度和最小密度，用于绘制坐标轴
# d = data.frame(table(cut(data$NLR_log, breaks=seq(min(data$NLR_log), max(data$NLR_log), by=0.2))))
# d$density = d$Freq / sum(d$Freq)
d<-density(data$NLR_log, n=as.integer((max(data$NLR_log) - min(data$NLR_log)) / 0.1),bw=0.1)
dmin<-as.numeric(min(d[["y"]])) / sum(d[["y"]])##density
dmax<-as.numeric(max(d[["y"]])) / sum(d[["y"]])##density
CI_upper = max(Pre0$upper)

#画图
p = ggplot(Pre0,aes(x=NLR_log))+
  geom_histogram(aes(x=NLR_log,y =rescale(after_stat(density),c(0,CI_upper))),binwidth = 0.1,fill="#FCC060",colour="white")+#画直方图
  geom_line(data=Pre0,aes(NLR_log,yhat),linetype=1,linewidth=1,alpha = 0.9,colour="red")+#画HR曲线
  geom_ribbon(data=Pre0,aes(ymin = lower, ymax = upper),linetype=2,alpha = 0.3,fill="grey",colour="red")+#画置信区间
  geom_hline(yintercept=1, linetype="dashed",linewidth=1)+#在HR曲线与y=1交点画一条y轴的红色虚线
  theme_bw()+
  scale_x_continuous(n.breaks = 6,labels = label_number(accuracy = 0.01))+#设置x轴
  xlim(min(data$NLR_log), max(data$NLR_log))+
  theme(panel.grid.major = element_blank(),panel.grid.minor = element_blank(),
        axis.text.x = element_text(size=14),axis.text.y = element_text(size=14),
        axis.title.x = element_text(size=14),axis.title.y = element_text(size=14))+#设置网格，字体大小
  xlab("Ln(NLR)")+ylab("Hazard ratio(95%CI)")+#设置x和y轴标签
  ggtitle('NLR CVD mortality')+#添加标题
  annotate(geom="text",x=min(data$NLR_log),y=CI_upper,label=paste('P for overall',fit_over,'\nP for nonlinearity',fit_nonl),hjust=-0.1,size=5,color="black")+#添加文字
  scale_y_continuous(sec.axis = sec_axis( ~rescale(.,c(dmin,dmax)), name = "Density, %"))#添加右侧坐标轴

#保存图片为pdf
pdf("图s4-14.pdf")
p
dev.off()


#画图S4-NLR变量-CANSERmortstat
#构建生存模型
#构建带权重的生存模型
fit_svy<- svycoxph(Surv(permth_int, CANSERmortstat) ~ rcs(NLR_log,4) + Age + Sex + Race + Educationlevel + 
                     PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
                     Alcoholstatus+ Failingkidneys + Hypertension, design=data_design)
#weight变量是权重
fit<- cph(Surv(permth_int, CANSERmortstat) ~ rcs(NLR_log,4) + Age + Sex + Race + Educationlevel + 
            PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
            Alcoholstatus+ Failingkidneys + Hypertension, x=TRUE, y=TRUE,data=data, weights = (1/fit_svy$survey.design$prob)/mean(1/fit_svy$survey.design$prob))
#计算预测值
Pre0<-rms::Predict(fit,NLR_log=data$NLR_log,fun=exp,type="predictions",ref.zero=T,conf.int= 0.95,digits=2)
Pre0<-as.data.frame(Pre0)#预测值转换为df必须转换，不然作不了图

#计算p overall和p nonliner
fit_p = anova(fit)
fit_over = format.pval(fit_p[1,3],digit=3,eps=0.001)
fit_nonl = format.pval(fit_p[2,3],digit=3,eps=0.001)

#计算直方图的密度，按照0.1的宽度进划分区间，并计算区间的最大密度和最小密度，用于绘制坐标轴
d<-density(data$NLR_log, n=as.integer((max(data$NLR_log) - min(data$NLR_log)) / 0.1))
dmin<-as.numeric(min(d[["y"]])) / sum(d[["y"]])##density
dmax<-as.numeric(max(d[["y"]])) / sum(d[["y"]])##density
CI_upper = max(Pre0$upper)

#画图
p = ggplot(Pre0,aes(x=NLR_log))+
  geom_histogram(aes(x=NLR_log,y =rescale(after_stat(density),c(0,CI_upper))),binwidth = 0.1,fill="#FCC060",colour="white")+#画直方图
  geom_line(data=Pre0,aes(NLR_log,yhat),linetype=1,linewidth=1,alpha = 0.9,colour="red")+#画HR曲线
  geom_ribbon(data=Pre0,aes(ymin = lower, ymax = upper),linetype=2,alpha = 0.3,fill="grey",colour="red")+#画置信区间
  geom_hline(yintercept=1, linetype="dashed",linewidth=1)+#在HR曲线与y=1交点画一条y轴的红色虚线
  theme_bw()+
  scale_x_continuous(n.breaks = 6,labels = label_number(accuracy = 0.01))+#设置x轴对数坐标
  xlim(min(data$NLR_log), max(data$NLR_log))+
  theme(panel.grid.major = element_blank(),panel.grid.minor = element_blank(),
        axis.text.x = element_text(size=14),axis.text.y = element_text(size=14),
        axis.title.x = element_text(size=14),axis.title.y = element_text(size=14))+#设置网格，字体大小
  xlab("Ln(NLR)")+ylab("Hazard ratio(95%CI)")+#设置x和y轴标签
  ggtitle('NLR CANSER mortality')+#添加标题
  annotate(geom="text",x=min(data$NLR_log),y=CI_upper,label=paste('P for overall',fit_over,'\nP for nonlinearity',fit_nonl),hjust=-0.1,size=5,color="black")+#添加文字
  scale_y_continuous(sec.axis = sec_axis( ~rescale(.,c(dmin,dmax)), name = "Density, %"))#添加右侧坐标轴

#保存图片为pdf
pdf("图s4-15.pdf")
p
dev.off()


#画图S4-MLR变量-Allcausemortstat
#构建生存模型
#构建带权重的生存模型
fit_svy<- svycoxph(Surv(permth_int, Allcausemortstat) ~ rcs(MLR_log,4) + Age + Sex + Race + Educationlevel + 
                     PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
                     Alcoholstatus+ Failingkidneys + Hypertension, design=data_design)
#weight变量是权重
fit<- cph(Surv(permth_int, Allcausemortstat) ~ rcs(MLR_log,4) + Age + Sex + Race + Educationlevel + 
            PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
            Alcoholstatus+ Failingkidneys + Hypertension, x=TRUE, y=TRUE,data=data, weights = (1/fit_svy$survey.design$prob)/mean(1/fit_svy$survey.design$prob))
#计算预测值
Pre0<-rms::Predict(fit,MLR_log=data$MLR_log,fun=exp,type="predictions",ref.zero=T,conf.int= 0.95,digits=2)
Pre0<-as.data.frame(Pre0)#预测值转换为df必须转换，不然作不了图

#计算p overall和p nonliner
fit_p = anova(fit)
fit_over = format.pval(fit_p[1,3],digit=3,eps=0.001)
fit_nonl = format.pval(fit_p[2,3],digit=3,eps=0.001)

#计算直方图的密度，按照0.1的宽度进划分区间，并计算区间的最大密度和最小密度，用于绘制坐标轴
d<-density(data$MLR_log, n=as.integer((max(data$MLR_log) - min(data$MLR_log)) / 0.1))
dmin<-as.numeric(min(d[["y"]])) / sum(d[["y"]])##density
dmax<-as.numeric(max(d[["y"]])) / sum(d[["y"]])##density
CI_upper = max(Pre0$upper)

#画图
p = ggplot(Pre0,aes(x=MLR_log))+
  geom_histogram(aes(x=MLR_log,y =rescale(after_stat(density),c(0,CI_upper))),binwidth = 0.1,fill="#FCC060",colour="white")+#画直方图
  geom_line(data=Pre0,aes(MLR_log,yhat),linetype=1,linewidth=1,alpha = 0.9,colour="red")+#画HR曲线
  geom_ribbon(data=Pre0,aes(ymin = lower, ymax = upper),linetype=2,alpha = 0.3,fill="grey",colour="red")+#画置信区间
  geom_hline(yintercept=1, linetype="dashed",linewidth=1)+#在HR曲线与y=1交点画一条y轴的红色虚线
  theme_bw()+
  scale_x_continuous(n.breaks = 6,labels = label_number(accuracy = 0.01))+#设置x轴对数坐标
  xlim(min(data$MLR_log), max(data$MLR_log))+
  theme(panel.grid.major = element_blank(),panel.grid.minor = element_blank(),
        axis.text.x = element_text(size=14),axis.text.y = element_text(size=14),
        axis.title.x = element_text(size=14),axis.title.y = element_text(size=14))+#设置网格，字体大小
  xlab("Ln(MLR)")+ylab("Hazard ratio(95%CI)")+#设置x和y轴标签
  ggtitle('MLR All-cause mortality')+#添加标题
  annotate(geom="text",x=min(data$MLR_log),y=CI_upper,label=paste('P for overall',fit_over,'\nP for nonlinearity',fit_nonl),hjust=-0.1,size=5,color="black")+#添加文字
  scale_y_continuous(sec.axis = sec_axis( ~rescale(.,c(dmin,dmax)), name = "Density, %"))#添加右侧坐标轴

#保存图片为pdf
pdf("图s4-16.pdf")
p
dev.off()



#画图S4-MLR变量-CVDmortstat
#构建生存模型
#构建带权重的生存模型
fit_svy<- svycoxph(Surv(permth_int, CVDmortstat) ~ rcs(MLR_log,4) + Age + Sex + Race + Educationlevel + 
                     PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
                     Alcoholstatus+ Failingkidneys + Hypertension, design=data_design)
#weight变量是权重
fit<- cph(Surv(permth_int, CVDmortstat) ~ rcs(MLR_log,4) + Age + Sex + Race + Educationlevel + 
            PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
            Alcoholstatus+ Failingkidneys + Hypertension, x=TRUE, y=TRUE,data=data, weights = (1/fit_svy$survey.design$prob)/mean(1/fit_svy$survey.design$prob))
#计算预测值
Pre0<-rms::Predict(fit,MLR_log=data$MLR_log,fun=exp,type="predictions",ref.zero=T,conf.int= 0.95,digits=2)
Pre0<-as.data.frame(Pre0)#预测值转换为df必须转换，不然作不了图

#计算p overall和p nonliner
fit_p = anova(fit)
fit_over = format.pval(fit_p[1,3],digit=3,eps=0.001)
fit_nonl = format.pval(fit_p[2,3],digit=3,eps=0.001)

#计算直方图的密度，按照0.1的宽度进划分区间，并计算区间的最大密度和最小密度，用于绘制坐标轴
# d = data.frame(table(cut(data$MLR_log, breaks=seq(min(data$MLR_log), max(data$MLR_log), by=0.2))))
# d$density = d$Freq / sum(d$Freq)
d<-density(data$MLR_log, n=as.integer((max(data$MLR_log) - min(data$MLR_log)) / 0.1),bw=0.1)
dmin<-as.numeric(min(d[["y"]])) / sum(d[["y"]])##density
dmax<-as.numeric(max(d[["y"]])) / sum(d[["y"]])##density
CI_upper = max(Pre0$upper)

#画图
p = ggplot(Pre0,aes(x=MLR_log))+
  geom_histogram(aes(x=MLR_log,y =rescale(after_stat(density),c(0,CI_upper))),binwidth = 0.1,fill="#FCC060",colour="white")+#画直方图
  geom_line(data=Pre0,aes(MLR_log,yhat),linetype=1,linewidth=1,alpha = 0.9,colour="red")+#画HR曲线
  geom_ribbon(data=Pre0,aes(ymin = lower, ymax = upper),linetype=2,alpha = 0.3,fill="grey",colour="red")+#画置信区间
  geom_hline(yintercept=1, linetype="dashed",linewidth=1)+#在HR曲线与y=1交点画一条y轴的红色虚线
  theme_bw()+
  scale_x_continuous(n.breaks = 6,labels = label_number(accuracy = 0.01))+#设置x轴
  xlim(min(data$MLR_log), max(data$MLR_log))+
  theme(panel.grid.major = element_blank(),panel.grid.minor = element_blank(),
        axis.text.x = element_text(size=14),axis.text.y = element_text(size=14),
        axis.title.x = element_text(size=14),axis.title.y = element_text(size=14))+#设置网格，字体大小
  xlab("Ln(design = data_design)")+ylab("Hazard ratio(95%CI)")+#设置x和y轴标签
  ggtitle('MLR CVD mortality')+#添加标题
  annotate(geom="text",x=min(data$MLR_log),y=CI_upper,label=paste('P for overall',fit_over,'\nP for nonlinearity',fit_nonl),hjust=-0.1,size=5,color="black")+#添加文字
  scale_y_continuous(sec.axis = sec_axis( ~rescale(.,c(dmin,dmax)), name = "Density, %"))#添加右侧坐标轴

#保存图片为pdf
pdf("图s4-17.pdf")
p
dev.off()


#画图S4-MLR变量-CANSERmortstat
#构建生存模型
#构建带权重的生存模型
fit_svy<- svycoxph(Surv(permth_int, CANSERmortstat) ~ rcs(MLR_log,4) + Age + Sex + Race + Educationlevel + 
                     PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
                     Alcoholstatus+ Failingkidneys + Hypertension, design=data_design)
#weight变量是权重
fit<- cph(Surv(permth_int, CANSERmortstat) ~ rcs(MLR_log,4) + Age + Sex + Race + Educationlevel + 
            PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
            Alcoholstatus+ Failingkidneys + Hypertension, x=TRUE, y=TRUE,data=data, weights = (1/fit_svy$survey.design$prob)/mean(1/fit_svy$survey.design$prob))
#计算预测值
Pre0<-rms::Predict(fit,MLR_log=data$MLR_log,fun=exp,type="predictions",ref.zero=T,conf.int= 0.95,digits=2)
Pre0<-as.data.frame(Pre0)#预测值转换为df必须转换，不然作不了图

#计算p overall和p nonliner
fit_p = anova(fit)
fit_over = format.pval(fit_p[1,3],digit=3,eps=0.001)
fit_nonl = format.pval(fit_p[2,3],digit=3,eps=0.001)

#计算直方图的密度，按照0.1的宽度进划分区间，并计算区间的最大密度和最小密度，用于绘制坐标轴
d<-density(data$MLR_log, n=as.integer((max(data$MLR_log) - min(data$MLR_log)) / 0.1))
dmin<-as.numeric(min(d[["y"]])) / sum(d[["y"]])##density
dmax<-as.numeric(max(d[["y"]])) / sum(d[["y"]])##density
CI_upper = max(Pre0$upper)

#画图
p = ggplot(Pre0,aes(x=MLR_log))+
  geom_histogram(aes(x=MLR_log,y =rescale(after_stat(density),c(0,CI_upper))),binwidth = 0.1,fill="#FCC060",colour="white")+#画直方图
  geom_line(data=Pre0,aes(MLR_log,yhat),linetype=1,linewidth=1,alpha = 0.9,colour="red")+#画HR曲线
  geom_ribbon(data=Pre0,aes(ymin = lower, ymax = upper),linetype=2,alpha = 0.3,fill="grey",colour="red")+#画置信区间
  geom_hline(yintercept=1, linetype="dashed",linewidth=1)+#在HR曲线与y=1交点画一条y轴的红色虚线
  theme_bw()+
  scale_x_continuous(n.breaks = 6,labels = label_number(accuracy = 0.01))+#设置x轴对数坐标
  xlim(min(data$MLR_log), max(data$MLR_log))+
  theme(panel.grid.major = element_blank(),panel.grid.minor = element_blank(),
        axis.text.x = element_text(size=14),axis.text.y = element_text(size=14),
        axis.title.x = element_text(size=14),axis.title.y = element_text(size=14))+#设置网格，字体大小
  xlab("Ln(MLR)")+ylab("Hazard ratio(95%CI)")+#设置x和y轴标签
  ggtitle('MLR CANSER mortality')+#添加标题
  annotate(geom="text",x=min(data$MLR_log),y=CI_upper,label=paste('P for overall',fit_over,'\nP for nonlinearity',fit_nonl),hjust=-0.1,size=5,color="black")+#添加文字
  scale_y_continuous(sec.axis = sec_axis( ~rescale(.,c(dmin,dmax)), name = "Density, %"))#添加右侧坐标轴


#保存图片为pdf
pdf("图s4-18.pdf")
p
dev.off()

#画图S4-NMLR变量-Allcausemortstat
#构建生存模型
#构建带权重的生存模型
fit_svy<- svycoxph(Surv(permth_int, Allcausemortstat) ~ rcs(NMLR_log,4) + Age + Sex + Race + Educationlevel + 
                     PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
                     Alcoholstatus+ Failingkidneys + Hypertension, design=data_design)
#weight变量是权重
fit<- cph(Surv(permth_int, Allcausemortstat) ~ rcs(NMLR_log,4) + Age + Sex + Race + Educationlevel + 
            PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
            Alcoholstatus+ Failingkidneys + Hypertension, x=TRUE, y=TRUE,data=data, weights = (1/fit_svy$survey.design$prob)/mean(1/fit_svy$survey.design$prob))
#计算预测值
Pre0<-rms::Predict(fit,NMLR_log=data$NMLR_log,fun=exp,type="predictions",ref.zero=T,conf.int= 0.95,digits=2)
Pre0<-as.data.frame(Pre0)#预测值转换为df必须转换，不然作不了图

#计算p overall和p nonliner
fit_p = anova(fit)
fit_over = format.pval(fit_p[1,3],digit=3,eps=0.001)
fit_nonl = format.pval(fit_p[2,3],digit=3,eps=0.001)

#计算直方图的密度，按照0.1的宽度进划分区间，并计算区间的最大密度和最小密度，用于绘制坐标轴
d<-density(data$NMLR_log, n=as.integer((max(data$NMLR_log) - min(data$NMLR_log)) / 0.1))
dmin<-as.numeric(min(d[["y"]])) / sum(d[["y"]])##density
dmax<-as.numeric(max(d[["y"]])) / sum(d[["y"]])##density
CI_upper = max(Pre0$upper)

#画图
p = ggplot(Pre0,aes(x=NMLR_log))+
  geom_histogram(aes(x=NMLR_log,y =rescale(after_stat(density),c(0,CI_upper))),binwidth = 0.1,fill="#FCC060",colour="white")+#画直方图
  geom_line(data=Pre0,aes(NMLR_log,yhat),linetype=1,linewidth=1,alpha = 0.9,colour="red")+#画HR曲线
  geom_ribbon(data=Pre0,aes(ymin = lower, ymax = upper),linetype=2,alpha = 0.3,fill="grey",colour="red")+#画置信区间
  geom_hline(yintercept=1, linetype="dashed",linewidth=1)+#在HR曲线与y=1交点画一条y轴的红色虚线
  theme_bw()+
  scale_x_continuous(n.breaks = 6,labels = label_number(accuracy = 0.01))+#设置x轴对数坐标
  xlim(min(data$NMLR_log), max(data$NMLR_log))+
  theme(panel.grid.major = element_blank(),panel.grid.minor = element_blank(),
        axis.text.x = element_text(size=14),axis.text.y = element_text(size=14),
        axis.title.x = element_text(size=14),axis.title.y = element_text(size=14))+#设置网格，字体大小
  xlab("Ln(NMLR)")+ylab("Hazard ratio(95%CI)")+#设置x和y轴标签
  ggtitle('NMLR All-cause mortality')+#添加标题
  annotate(geom="text",x=min(data$NMLR_log),y=CI_upper,label=paste('P for overall',fit_over,'\nP for nonlinearity',fit_nonl),hjust=-0.1,size=5,color="black")+#添加文字
  scale_y_continuous(sec.axis = sec_axis( ~rescale(.,c(dmin,dmax)), name = "Density, %"))#添加右侧坐标轴

#保存图片为pdf
pdf("图s4-19.pdf")
p
dev.off()


#画图S4-NMLR变量-CVDmortstat
#构建生存模型
#构建带权重的生存模型
fit_svy<- svycoxph(Surv(permth_int, CVDmortstat) ~ rcs(NMLR_log,4) + Age + Sex + Race + Educationlevel + 
                     PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
                     Alcoholstatus+ Failingkidneys + Hypertension, design=data_design)
#weight变量是权重
fit<- cph(Surv(permth_int, CVDmortstat) ~ rcs(NMLR_log,4) + Age + Sex + Race + Educationlevel + 
            PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
            Alcoholstatus+ Failingkidneys + Hypertension, x=TRUE, y=TRUE,data=data, weights = (1/fit_svy$survey.design$prob)/mean(1/fit_svy$survey.design$prob))
#计算预测值
Pre0<-rms::Predict(fit,NMLR_log=data$NMLR_log,fun=exp,type="predictions",ref.zero=T,conf.int= 0.95,digits=2)
Pre0<-as.data.frame(Pre0)#预测值转换为df必须转换，不然作不了图

#计算p overall和p nonliner
fit_p = anova(fit)
fit_over = format.pval(fit_p[1,3],digit=3,eps=0.001)
fit_nonl = format.pval(fit_p[2,3],digit=3,eps=0.001)

#计算直方图的密度，按照0.1的宽度进划分区间，并计算区间的最大密度和最小密度，用于绘制坐标轴
# d = data.frame(table(cut(data$NMLR_log, breaks=seq(min(data$NMLR_log), max(data$NMLR_log), by=0.2))))
# d$density = d$Freq / sum(d$Freq)
d<-density(data$NMLR_log, n=as.integer((max(data$NMLR_log) - min(data$NMLR_log)) / 0.1),bw=0.1)
dmin<-as.numeric(min(d[["y"]])) / sum(d[["y"]])##density
dmax<-as.numeric(max(d[["y"]])) / sum(d[["y"]])##density
CI_upper = max(Pre0$upper)

#画图
p = ggplot(Pre0,aes(x=NMLR_log))+
  geom_histogram(aes(x=NMLR_log,y =rescale(after_stat(density),c(0,CI_upper))),binwidth = 0.1,fill="#FCC060",colour="white")+#画直方图
  geom_line(data=Pre0,aes(NMLR_log,yhat),linetype=1,linewidth=1,alpha = 0.9,colour="red")+#画HR曲线
  geom_ribbon(data=Pre0,aes(ymin = lower, ymax = upper),linetype=2,alpha = 0.3,fill="grey",colour="red")+#画置信区间
  geom_hline(yintercept=1, linetype="dashed",linewidth=1)+#在HR曲线与y=1交点画一条y轴的红色虚线
  theme_bw()+
  scale_x_continuous(n.breaks = 6,labels = label_number(accuracy = 0.01))+#设置x轴
  xlim(min(data$NMLR_log), max(data$NMLR_log))+
  theme(panel.grid.major = element_blank(),panel.grid.minor = element_blank(),
        axis.text.x = element_text(size=14),axis.text.y = element_text(size=14),
        axis.title.x = element_text(size=14),axis.title.y = element_text(size=14))+#设置网格，字体大小
  xlab("Ln(NMLR)")+ylab("Hazard ratio(95%CI)")+#设置x和y轴标签
  ggtitle('NMLR CVD mortality')+#添加标题
  annotate(geom="text",x=min(data$NMLR_log),y=CI_upper,label=paste('P for overall',fit_over,'\nP for nonlinearity',fit_nonl),hjust=-0.1,size=5,color="black")+#添加文字
  scale_y_continuous(sec.axis = sec_axis( ~rescale(.,c(dmin,dmax)), name = "Density, %"))#添加右侧坐标轴

#保存图片为pdf
pdf("图s4-20.pdf")
p
dev.off()


#画图S4-NMLR变量-CANSERmortstat
#构建生存模型
#构建带权重的生存模型
fit_svy<- svycoxph(Surv(permth_int, CANSERmortstat) ~ rcs(NMLR_log,4) + Age + Sex + Race + Educationlevel + 
                     PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
                     Alcoholstatus+ Failingkidneys + Hypertension, design=data_design)
#weight变量是权重
fit<- cph(Surv(permth_int, CANSERmortstat) ~ rcs(NMLR_log,4) + Age + Sex + Race + Educationlevel + 
            PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
            Alcoholstatus+ Failingkidneys + Hypertension, x=TRUE, y=TRUE,data=data, weights = (1/fit_svy$survey.design$prob)/mean(1/fit_svy$survey.design$prob))
#计算预测值
Pre0<-rms::Predict(fit,NMLR_log=data$NMLR_log,fun=exp,type="predictions",ref.zero=T,conf.int= 0.95,digits=2)
Pre0<-as.data.frame(Pre0)#预测值转换为df必须转换，不然作不了图

#计算p overall和p nonliner
fit_p = anova(fit)
fit_over = format.pval(fit_p[1,3],digit=3,eps=0.001)
fit_nonl = format.pval(fit_p[2,3],digit=3,eps=0.001)

#计算直方图的密度，按照0.1的宽度进划分区间，并计算区间的最大密度和最小密度，用于绘制坐标轴
d<-density(data$NMLR_log, n=as.integer((max(data$NMLR_log) - min(data$NMLR_log)) / 0.1))
dmin<-as.numeric(min(d[["y"]])) / sum(d[["y"]])##density
dmax<-as.numeric(max(d[["y"]])) / sum(d[["y"]])##density
CI_upper = max(Pre0$upper)

#画图
p = ggplot(Pre0,aes(x=NMLR_log))+
  geom_histogram(aes(x=NMLR_log,y =rescale(after_stat(density),c(0,CI_upper))),binwidth = 0.1,fill="#FCC060",colour="white")+#画直方图
  geom_line(data=Pre0,aes(NMLR_log,yhat),linetype=1,linewidth=1,alpha = 0.9,colour="red")+#画HR曲线
  geom_ribbon(data=Pre0,aes(ymin = lower, ymax = upper),linetype=2,alpha = 0.3,fill="grey",colour="red")+#画置信区间
  geom_hline(yintercept=1, linetype="dashed",linewidth=1)+#在HR曲线与y=1交点画一条y轴的红色虚线
  theme_bw()+
  scale_x_continuous(n.breaks = 6,labels = label_number(accuracy = 0.01))+#设置x轴对数坐标
  xlim(min(data$NMLR_log), max(data$NMLR_log))+
  theme(panel.grid.major = element_blank(),panel.grid.minor = element_blank(),
        axis.text.x = element_text(size=14),axis.text.y = element_text(size=14),
        axis.title.x = element_text(size=14),axis.title.y = element_text(size=14))+#设置网格，字体大小
  xlab("Ln(NMLR)")+ylab("Hazard ratio(95%CI)")+#设置x和y轴标签
  ggtitle('NMLR CANSER mortality')+#添加标题
  annotate(geom="text",x=min(data$NMLR_log),y=CI_upper,label=paste('P for overall',fit_over,'\nP for nonlinearity',fit_nonl),hjust=-0.1,size=5,color="black")+#添加文字
  scale_y_continuous(sec.axis = sec_axis( ~rescale(.,c(dmin,dmax)), name = "Density, %"))#添加右侧坐标轴


#保存图片为pdf
pdf("图s4-21.pdf")
p
dev.off()

#画图S4-SIRI变量-Allcausemortstat
#构建生存模型
#构建带权重的生存模型
fit_svy<- svycoxph(Surv(permth_int, Allcausemortstat) ~ rcs(SIRI_log,4) + Age + Sex + Race + Educationlevel + 
                     PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
                     Alcoholstatus+ Failingkidneys + Hypertension, design=data_design)
#weight变量是权重
fit<- cph(Surv(permth_int, Allcausemortstat) ~ rcs(SIRI_log,4) + Age + Sex + Race + Educationlevel + 
            PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
            Alcoholstatus+ Failingkidneys + Hypertension, x=TRUE, y=TRUE,data=data, weights = (1/fit_svy$survey.design$prob)/mean(1/fit_svy$survey.design$prob))
#计算预测值
Pre0<-rms::Predict(fit,SIRI_log=data$SIRI_log,fun=exp,type="predictions",ref.zero=T,conf.int= 0.95,digits=2)
Pre0<-as.data.frame(Pre0)#预测值转换为df必须转换，不然作不了图

#计算p overall和p nonliner
fit_p = anova(fit)
fit_over = format.pval(fit_p[1,3],digit=3,eps=0.001)
fit_nonl = format.pval(fit_p[2,3],digit=3,eps=0.001)

#计算直方图的密度，按照0.1的宽度进划分区间，并计算区间的最大密度和最小密度，用于绘制坐标轴
d<-density(data$SIRI_log, n=as.integer((max(data$SIRI_log) - min(data$SIRI_log)) / 0.1))
dmin<-as.numeric(min(d[["y"]])) / sum(d[["y"]])##density
dmax<-as.numeric(max(d[["y"]])) / sum(d[["y"]])##density
CI_upper = max(Pre0$upper)

#画图
p = ggplot(Pre0,aes(x=SIRI_log))+
  geom_histogram(aes(x=SIRI_log,y =rescale(after_stat(density),c(0,CI_upper))),binwidth = 0.1,fill="#FCC060",colour="white")+#画直方图
  geom_line(data=Pre0,aes(SIRI_log,yhat),linetype=1,linewidth=1,alpha = 0.9,colour="red")+#画HR曲线
  geom_ribbon(data=Pre0,aes(ymin = lower, ymax = upper),linetype=2,alpha = 0.3,fill="grey",colour="red")+#画置信区间
  geom_hline(yintercept=1, linetype="dashed",linewidth=1)+#在HR曲线与y=1交点画一条y轴的红色虚线
  theme_bw()+
  scale_x_continuous(n.breaks = 6,labels = label_number(accuracy = 0.01))+#设置x轴对数坐标
  xlim(min(data$SIRI_log), max(data$SIRI_log))+
  theme(panel.grid.major = element_blank(),panel.grid.minor = element_blank(),
        axis.text.x = element_text(size=14),axis.text.y = element_text(size=14),
        axis.title.x = element_text(size=14),axis.title.y = element_text(size=14))+#设置网格，字体大小
  xlab("Ln(SIRI)")+ylab("Hazard ratio(95%CI)")+#设置x和y轴标签
  ggtitle('SIRI All-cause mortality')+#添加标题
  annotate(geom="text",x=min(data$SIRI_log),y=CI_upper,label=paste('P for overall',fit_over,'\nP for nonlinearity',fit_nonl),hjust=-0.1,size=5,color="black")+#添加文字
  scale_y_continuous(sec.axis = sec_axis( ~rescale(.,c(dmin,dmax)), name = "Density, %"))#添加右侧坐标轴


#保存图片为pdf
pdf("图s4-22.pdf")
p
dev.off()


#画图S4-SIRI变量-CVDmortstat
#构建生存模型
#构建带权重的生存模型
fit_svy<- svycoxph(Surv(permth_int, CVDmortstat) ~ rcs(SIRI_log,4) + Age + Sex + Race + Educationlevel + 
                     PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
                     Alcoholstatus+ Failingkidneys + Hypertension, design=data_design)
#weight变量是权重
fit<- cph(Surv(permth_int, CVDmortstat) ~ rcs(SIRI_log,4) + Age + Sex + Race + Educationlevel + 
            PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
            Alcoholstatus+ Failingkidneys + Hypertension, x=TRUE, y=TRUE,data=data, weights = (1/fit_svy$survey.design$prob)/mean(1/fit_svy$survey.design$prob))
#计算预测值
Pre0<-rms::Predict(fit,SIRI_log=data$SIRI_log,fun=exp,type="predictions",ref.zero=T,conf.int= 0.95,digits=2)
Pre0<-as.data.frame(Pre0)#预测值转换为df必须转换，不然作不了图

#计算p overall和p nonliner
fit_p = anova(fit)
fit_over = format.pval(fit_p[1,3],digit=3,eps=0.001)
fit_nonl = format.pval(fit_p[2,3],digit=3,eps=0.001)

#计算直方图的密度，按照0.1的宽度进划分区间，并计算区间的最大密度和最小密度，用于绘制坐标轴
# d = data.frame(table(cut(data$SIRI_log, breaks=seq(min(data$SIRI_log), max(data$SIRI_log), by=0.2))))
# d$density = d$Freq / sum(d$Freq)
d<-density(data$SIRI_log, n=as.integer((max(data$SIRI_log) - min(data$SIRI_log)) / 0.1),bw=0.1)
dmin<-as.numeric(min(d[["y"]])) / sum(d[["y"]])##density
dmax<-as.numeric(max(d[["y"]])) / sum(d[["y"]])##density
CI_upper = max(Pre0$upper)

#画图
p = ggplot(Pre0,aes(x=SIRI_log))+
  geom_histogram(aes(x=SIRI_log,y =rescale(after_stat(density),c(0,CI_upper))),binwidth = 0.1,fill="#FCC060",colour="white")+#画直方图
  geom_line(data=Pre0,aes(SIRI_log,yhat),linetype=1,linewidth=1,alpha = 0.9,colour="red")+#画HR曲线
  geom_ribbon(data=Pre0,aes(ymin = lower, ymax = upper),linetype=2,alpha = 0.3,fill="grey",colour="red")+#画置信区间
  geom_hline(yintercept=1, linetype="dashed",linewidth=1)+#在HR曲线与y=1交点画一条y轴的红色虚线
  theme_bw()+
  scale_x_continuous(n.breaks = 6,labels = label_number(accuracy = 0.01))+#设置x轴
  xlim(min(data$SIRI_log), max(data$SIRI_log))+
  theme(panel.grid.major = element_blank(),panel.grid.minor = element_blank(),
        axis.text.x = element_text(size=14),axis.text.y = element_text(size=14),
        axis.title.x = element_text(size=14),axis.title.y = element_text(size=14))+#设置网格，字体大小
  xlab("Ln(SIRI)")+ylab("Hazard ratio(95%CI)")+#设置x和y轴标签
  ggtitle('SIRI CVD mortality')+#添加标题
  annotate(geom="text",x=min(data$SIRI_log),y=CI_upper,label=paste('P for overall',fit_over,'\nP for nonlinearity',fit_nonl),hjust=-0.1,size=5,color="black")+#添加文字
  scale_y_continuous(sec.axis = sec_axis( ~rescale(.,c(dmin,dmax)), name = "Density, %"))#添加右侧坐标轴


#保存图片为pdf
pdf("图s4-23.pdf")
p
dev.off()

#画图S4-SIRI变量-CANSERmortstat
#构建生存模型
#构建带权重的生存模型
fit_svy<- svycoxph(Surv(permth_int, CANSERmortstat) ~ rcs(SIRI_log,4) + Age + Sex + Race + Educationlevel + 
                     PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
                     Alcoholstatus+ Failingkidneys + Hypertension, design=data_design)
#weight变量是权重
fit<- cph(Surv(permth_int, CANSERmortstat) ~ rcs(SIRI_log,4) + Age + Sex + Race + Educationlevel + 
            PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
            Alcoholstatus+ Failingkidneys + Hypertension, x=TRUE, y=TRUE,data=data, weights = (1/fit_svy$survey.design$prob)/mean(1/fit_svy$survey.design$prob))
#计算预测值
Pre0<-rms::Predict(fit,SIRI_log=data$SIRI_log,fun=exp,type="predictions",ref.zero=T,conf.int= 0.95,digits=2)
Pre0<-as.data.frame(Pre0)#预测值转换为df必须转换，不然作不了图

#计算p overall和p nonliner
fit_p = anova(fit)
fit_over = format.pval(fit_p[1,3],digit=3,eps=0.001)
fit_nonl = format.pval(fit_p[2,3],digit=3,eps=0.001)

#计算直方图的密度，按照0.1的宽度进划分区间，并计算区间的最大密度和最小密度，用于绘制坐标轴
d<-density(data$SIRI_log, n=as.integer((max(data$SIRI_log) - min(data$SIRI_log)) / 0.1))
dmin<-as.numeric(min(d[["y"]])) / sum(d[["y"]])##density
dmax<-as.numeric(max(d[["y"]])) / sum(d[["y"]])##density
CI_upper = max(Pre0$upper)

#画图
p = ggplot(Pre0,aes(x=SIRI_log))+
  geom_histogram(aes(x=SIRI_log,y =rescale(after_stat(density),c(0,CI_upper))),binwidth = 0.1,fill="#FCC060",colour="white")+#画直方图
  geom_line(data=Pre0,aes(SIRI_log,yhat),linetype=1,linewidth=1,alpha = 0.9,colour="red")+#画HR曲线
  geom_ribbon(data=Pre0,aes(ymin = lower, ymax = upper),linetype=2,alpha = 0.3,fill="grey",colour="red")+#画置信区间
  geom_hline(yintercept=1, linetype="dashed",linewidth=1)+#在HR曲线与y=1交点画一条y轴的红色虚线
  theme_bw()+
  scale_x_continuous(n.breaks = 6,labels = label_number(accuracy = 0.01))+#设置x轴对数坐标
  xlim(min(data$SIRI_log), max(data$SIRI_log))+
  theme(panel.grid.major = element_blank(),panel.grid.minor = element_blank(),
        axis.text.x = element_text(size=14),axis.text.y = element_text(size=14),
        axis.title.x = element_text(size=14),axis.title.y = element_text(size=14))+#设置网格，字体大小
  xlab("Ln(SIRI)")+ylab("Hazard ratio(95%CI)")+#设置x和y轴标签
  ggtitle('SIRI CANSER mortality')+#添加标题
  annotate(geom="text",x=min(data$SIRI_log),y=CI_upper,label=paste('P for overall',fit_over,'\nP for nonlinearity',fit_nonl),hjust=-0.1,size=5,color="black")+#添加文字
  scale_y_continuous(sec.axis = sec_axis( ~rescale(.,c(dmin,dmax)), name = "Density, %"))#添加右侧坐标轴

#保存图片为pdf
pdf("图s4-24.pdf")
p
dev.off()


#画图S4-SII变量-Allcausemortstat
#构建生存模型
#构建带权重的生存模型
fit_svy<- svycoxph(Surv(permth_int, Allcausemortstat) ~ rcs(SII_log,4) + Age + Sex + Race + Educationlevel + 
                     PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
                     Alcoholstatus+ Failingkidneys + Hypertension, design=data_design)
#weight变量是权重
fit<- cph(Surv(permth_int, Allcausemortstat) ~ rcs(SII_log,4) + Age + Sex + Race + Educationlevel + 
            PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
            Alcoholstatus+ Failingkidneys + Hypertension, x=TRUE, y=TRUE,data=data, weights = (1/fit_svy$survey.design$prob)/mean(1/fit_svy$survey.design$prob))
#计算预测值
Pre0<-rms::Predict(fit,SII_log=data$SII_log,fun=exp,type="predictions",ref.zero=T,conf.int= 0.95,digits=2)
Pre0<-as.data.frame(Pre0)#预测值转换为df必须转换，不然作不了图

#计算p overall和p nonliner
fit_p = anova(fit)
fit_over = format.pval(fit_p[1,3],digit=3,eps=0.001)
fit_nonl = format.pval(fit_p[2,3],digit=3,eps=0.001)

#计算直方图的密度，按照0.1的宽度进划分区间，并计算区间的最大密度和最小密度，用于绘制坐标轴
d<-density(data$SII_log, n=as.integer((max(data$SII_log) - min(data$SII_log)) / 0.1))
dmin<-as.numeric(min(d[["y"]])) / sum(d[["y"]])##density
dmax<-as.numeric(max(d[["y"]])) / sum(d[["y"]])##density
CI_upper = max(Pre0$upper)

#画图
p = ggplot(Pre0,aes(x=SII_log))+
  geom_histogram(aes(x=SII_log,y =rescale(after_stat(density),c(0,CI_upper))),binwidth = 0.1,fill="#FCC060",colour="white")+#画直方图
  geom_line(data=Pre0,aes(SII_log,yhat),linetype=1,linewidth=1,alpha = 0.9,colour="red")+#画HR曲线
  geom_ribbon(data=Pre0,aes(ymin = lower, ymax = upper),linetype=2,alpha = 0.3,fill="grey",colour="red")+#画置信区间
  geom_hline(yintercept=1, linetype="dashed",linewidth=1)+#在HR曲线与y=1交点画一条y轴的红色虚线
  theme_bw()+
  scale_x_continuous(n.breaks = 6,labels = label_number(accuracy = 0.01))+#设置x轴对数坐标
  xlim(min(data$SII_log), max(data$SII_log))+
  theme(panel.grid.major = element_blank(),panel.grid.minor = element_blank(),
        axis.text.x = element_text(size=14),axis.text.y = element_text(size=14),
        axis.title.x = element_text(size=14),axis.title.y = element_text(size=14))+#设置网格，字体大小
  xlab("Ln(SII)")+ylab("Hazard ratio(95%CI)")+#设置x和y轴标签
  ggtitle('SII All-cause mortality')+#添加标题
  annotate(geom="text",x=min(data$SII_log),y=CI_upper,label=paste('P for overall',fit_over,'\nP for nonlinearity',fit_nonl),hjust=-0.1,size=5,color="black")+#添加文字
  scale_y_continuous(sec.axis = sec_axis( ~rescale(.,c(dmin,dmax)), name = "Density, %"))#添加右侧坐标轴


#保存图片为pdf
pdf("图s4-25.pdf")
p
dev.off()


#画图S4-SII变量-CVDmortstat
#构建生存模型
#构建带权重的生存模型
fit_svy<- svycoxph(Surv(permth_int, CVDmortstat) ~ rcs(SII_log,4) + Age + Sex + Race + Educationlevel + 
                     PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
                     Alcoholstatus+ Failingkidneys + Hypertension, design=data_design)
#weight变量是权重
fit<- cph(Surv(permth_int, CVDmortstat) ~ rcs(SII_log,4) + Age + Sex + Race + Educationlevel + 
            PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
            Alcoholstatus+ Failingkidneys + Hypertension, x=TRUE, y=TRUE,data=data, weights = (1/fit_svy$survey.design$prob)/mean(1/fit_svy$survey.design$prob))
#计算预测值
Pre0<-rms::Predict(fit,SII_log=data$SII_log,fun=exp,type="predictions",ref.zero=T,conf.int= 0.95,digits=2)
Pre0<-as.data.frame(Pre0)#预测值转换为df必须转换，不然作不了图

#计算p overall和p nonliner
fit_p = anova(fit)
fit_over = format.pval(fit_p[1,3],digit=3,eps=0.001)
fit_nonl = format.pval(fit_p[2,3],digit=3,eps=0.001)

#计算直方图的密度，按照0.1的宽度进划分区间，并计算区间的最大密度和最小密度，用于绘制坐标轴
# d = data.frame(table(cut(data$SII_log, breaks=seq(min(data$SII_log), max(data$SII_log), by=0.2))))
# d$density = d$Freq / sum(d$Freq)
d<-density(data$SII_log, n=as.integer((max(data$SII_log) - min(data$SII_log)) / 0.1),bw=0.1)
dmin<-as.numeric(min(d[["y"]])) / sum(d[["y"]])##density
dmax<-as.numeric(max(d[["y"]])) / sum(d[["y"]])##density
CI_upper = max(Pre0$upper)

#画图
p = ggplot(Pre0,aes(x=SII_log))+
  geom_histogram(aes(x=SII_log,y =rescale(after_stat(density),c(0,CI_upper))),binwidth = 0.1,fill="#FCC060",colour="white")+#画直方图
  geom_line(data=Pre0,aes(SII_log,yhat),linetype=1,linewidth=1,alpha = 0.9,colour="red")+#画HR曲线
  geom_ribbon(data=Pre0,aes(ymin = lower, ymax = upper),linetype=2,alpha = 0.3,fill="grey",colour="red")+#画置信区间
  geom_hline(yintercept=1, linetype="dashed",linewidth=1)+#在HR曲线与y=1交点画一条y轴的红色虚线
  theme_bw()+
  scale_x_continuous(n.breaks = 6,labels = label_number(accuracy = 0.01))+#设置x轴
  xlim(min(data$SII_log), max(data$SII_log))+
  theme(panel.grid.major = element_blank(),panel.grid.minor = element_blank(),
        axis.text.x = element_text(size=14),axis.text.y = element_text(size=14),
        axis.title.x = element_text(size=14),axis.title.y = element_text(size=14))+#设置网格，字体大小
  xlab("Ln(SII)")+ylab("Hazard ratio(95%CI)")+#设置x和y轴标签
  ggtitle('SII CVD mortality')+#添加标题
  annotate(geom="text",x=min(data$SII_log),y=CI_upper,label=paste('P for overall',fit_over,'\nP for nonlinearity',fit_nonl),hjust=-0.1,size=5,color="black")+#添加文字
  scale_y_continuous(sec.axis = sec_axis( ~rescale(.,c(dmin,dmax)), name = "Density, %"))#添加右侧坐标轴

#保存图片为pdf
pdf("图s4-26.pdf")
p
dev.off()


#画图S4-SII变量-CANSERmortstat
#构建生存模型
#构建带权重的生存模型
fit_svy<- svycoxph(Surv(permth_int, CANSERmortstat) ~ rcs(SII_log,4) + Age + Sex + Race + Educationlevel + 
                     PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
                     Alcoholstatus+ Failingkidneys + Hypertension, design=data_design)
#weight变量是权重
fit<- cph(Surv(permth_int, CANSERmortstat) ~ rcs(SII_log,4) + Age + Sex + Race + Educationlevel + 
            PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus + 
            Alcoholstatus+ Failingkidneys + Hypertension, x=TRUE, y=TRUE,data=data, weights = (1/fit_svy$survey.design$prob)/mean(1/fit_svy$survey.design$prob))
#计算预测值
Pre0<-rms::Predict(fit,SII_log=data$SII_log,fun=exp,type="predictions",ref.zero=T,conf.int= 0.95,digits=2)
Pre0<-as.data.frame(Pre0)#预测值转换为df必须转换，不然作不了图

#计算p overall和p nonliner
fit_p = anova(fit)
fit_over = format.pval(fit_p[1,3],digit=3,eps=0.001)
fit_nonl = format.pval(fit_p[2,3],digit=3,eps=0.001)

#计算直方图的密度，按照0.1的宽度进划分区间，并计算区间的最大密度和最小密度，用于绘制坐标轴
d<-density(data$SII_log, n=as.integer((max(data$SII_log) - min(data$SII_log)) / 0.1))
dmin<-as.numeric(min(d[["y"]])) / sum(d[["y"]])##density
dmax<-as.numeric(max(d[["y"]])) / sum(d[["y"]])##density
CI_upper = max(Pre0$upper)

#画图
p = ggplot(Pre0,aes(x=SII_log))+
  geom_histogram(aes(x=SII_log,y =rescale(after_stat(density),c(0,CI_upper))),binwidth = 0.1,fill="#FCC060",colour="white")+#画直方图
  geom_line(data=Pre0,aes(SII_log,yhat),linetype=1,linewidth=1,alpha = 0.9,colour="red")+#画HR曲线
  geom_ribbon(data=Pre0,aes(ymin = lower, ymax = upper),linetype=2,alpha = 0.3,fill="grey",colour="red")+#画置信区间
  geom_hline(yintercept=1, linetype="dashed",linewidth=1)+#在HR曲线与y=1交点画一条y轴的红色虚线
  theme_bw()+
  scale_x_continuous(n.breaks = 6,labels = label_number(accuracy = 0.01))+#设置x轴对数坐标
  xlim(min(data$SII_log), max(data$SII_log))+
  theme(panel.grid.major = element_blank(),panel.grid.minor = element_blank(),
        axis.text.x = element_text(size=14),axis.text.y = element_text(size=14),
        axis.title.x = element_text(size=14),axis.title.y = element_text(size=14))+#设置网格，字体大小
  xlab("Ln(SII)")+ylab("Hazard ratio(95%CI)")+#设置x和y轴标签
  ggtitle('SII CANSER mortality')+#添加标题
  annotate(geom="text",x=min(data$SII_log),y=CI_upper,label=paste('P for overall',fit_over,'\nP for nonlinearity',fit_nonl),hjust=-0.1,size=5,color="black")+#添加文字
  scale_y_continuous(sec.axis = sec_axis( ~rescale(.,c(dmin,dmax)), name = "Density, %"))#添加右侧坐标轴


#保存图片为pdf
pdf("图s4-27.pdf")
p
dev.off()





