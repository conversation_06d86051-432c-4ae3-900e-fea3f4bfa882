library(dplyr)
library(plyr)
library(foreign)
library(mice)
library(tableone)#画基线表
library(mlbench)
library(caret)
library(Boruta)
library(questionr)#计算OR和置信区间
library(plotRCS)#限制性立方样条绘图
library(pROC)
library(dcurves)
library(rms)
library(riskRegression)
library(rmda)
library(jstable)
library(forestplot)#画森林图
library(forestploter)
library(nricens)



#设置不用科学计数法显示数字，设置为0则为默认
options(scipen= 999)
options(digits = 3)


#读取人口学数据
library(dplyr)
library(haven)

load("data_raw.RData")
data = data_raw
#根据表s1进行筛选，一共有101316个样本，去掉20岁以下的个体(-47208个)
data = data %>% filter(Age > 20)
#去掉怀孕的个体(-1469)
data = data %>% filter(RIDEXPRG  != 1 | is.na(RIDEXPRG))
#去掉患癌的个体(-5132)
data = data %>% filter(MCQ220  != 1 | is.na(MCQ220))
#去掉没有糖尿病或糖尿病前期的个体的个体(-26603)
data = data %>% filter(DIQ  == 1 | DIQ  == 2)
#去掉血镉数据缺失的个体(-2624)
data = data %>% filter(!is.na(Cd))
#去掉氧化应激和炎症生物标志物数据缺失的个体(-1675)
data = data %>% filter(!is.na(GGT) & !is.na(UA) & !is.na(NLR) & !is.na(HDL) & !is.na(UHR) & !is.na(NLR) & !is.na(MLR) & !is.na(NMLR) & !is.na(SII) & !is.na(SIRI))
#去掉随访信息缺失的个体（-31）,最终剩余16116个（论文中为17687个）
data = data %>% filter(!is.na("All-cause-mortstat"))
save(data, file="data.RData")

#加权基线表
library(tableone)#画基线表
library(survey)
load("data.RData")

#把NA值全部替换为99999
data[is.na(data)] <- 99999

data$Age = cut(data$Age, breaks = c(-Inf, 60, Inf), labels = c("<60",">=60"), right=FALSE)
data$Sex = factor(data$Sex, levels=c(1,2), labels=c("Male", "Female"))
data$Race = factor(data$Race, levels=c(1,2,3,4,5), labels=c("Non-Hispanic White","Non-Hispanic Black","Mexican American", "Other Hispanic","Other Race - Including Multi-Racial"))
data$Educationlevel = factor(data$Educationlevel, levels=c(1,2,3,99999), labels=c("Below high school", "High school", "Above High school","Missing"))
data$PIR = cut(data$PIR, breaks = c(-Inf, 1.3,3.5,99998, Inf), labels = c("=<1.3",">1.3,=<3.5",">4.0","Missing"), right=FALSE)
data$BMI = cut(data$BMI, breaks = c(-Inf, 25,30,99998, Inf), labels = c("=<25",">25,=<30",">30","Missing"), right=FALSE)
data$Maritalstatus = factor(data$Maritalstatus, levels=c(1,2,3,99999), labels=c("Married / Living with partner", "Widowed / Divorced / Separated","Never married","Missing"))
data$Physicalactivity = cut(data$Physicalactivity, breaks = c(-Inf, 0,600,2000,99998, Inf), labels = c("Inactive","Moderate","Vigorous","Both moderate and vigorous ","Missing"), right=FALSE)
data$Smokingstatus = factor(data$Smokingstatus, levels=c(1,2,3,99999), labels=c("Never", "Former","Now","Missing"))
data$Alcoholstatus = factor(data$Alcoholstatus, levels=c(1,2,3,99999), labels=c("None", "Moderate","Excessive","Missing"))
data$Failingkidneys = factor(data$Failingkidneys, levels=c(1,2,99999), labels=c("Yes", "No","Missing"))
data$Hypertension = factor(data$Hypertension, levels=c(1,0,99999), labels=c("Yes", "No","Missing"))
#Cd加权四分位数分组
library(Hmisc)
result <- wtd.quantile(data$Cd, weights = data$WTMEC, probs = c(0,0.25, 0.5, 0.75,1))
data$Cd_4 = cut(data$Cd, breaks = c(quantile(result)[1:4], Inf), labels = c("Quantile1","Quantile2","Quantile3","Quantile4"), right=FALSE)



data_design <- svydesign(ids=~SDMVPSU,   #集群，抽样单元PSU
                    strata=~SDMVSTRA,   #分层
                    weights=~WTMEC,
                    nest=TRUE,
                    survey.lonely.psu="adjust",  #抽样单元为1时不报错
                    data=data)

#逐个变量正态性检验
#new_df <- data %>% slice(rep(1:n(), times = data$WTMEC))
#ks.test(scale(new_df$RIDAGEYR), "pnorm")#p<0.05，非正态分布

#其实好多变量都不是正态分布，所以这里直接取论文中的结果，即只有WWI变量是非正态分布变量
allVars <- c("Age","Sex",  "Race","Educationlevel","PIR","BMI","WWI","Maritalstatus","Physicalactivity",
             "Smokingstatus","Alcoholstatus","Failingkidneys","Hypertension")
fvars <- c("Age","Sex",  "Race","Educationlevel","PIR","BMI","Maritalstatus","Physicalactivity",
           "Smokingstatus","Alcoholstatus","Failingkidneys","Hypertension")#分类变量默认卡方检验
Svytab2 <- svyCreateTableOne(vars=allVars, strata="Cd_4", data=data_design, factorVars=fvars,addOverall = TRUE)

nonvar <- c("WWI")#指定非正态分布变量,默认用Kruskal-Wallis秩和检验

tab4Mat = print(Svytab2, #指定表格
                showAllLevels = TRUE, #显示所有类别
                factorVars = fvars,#指定分类变量
                nonnormal = nonvar,#指定非正态分布连续变量变量
                quote = FALSE, #不显示引号
                noSpaces = TRUE, #删除用于对齐的空格
                printToggle = FALSE)#不展示输出结果
# 保存为 CSV 格式文件
write.csv(tab4Mat, file = "myTable-before-mice.csv")

#这里做出来的基线表是加权后的样本数和加权百分比来展示的
#但是论文里是不加权样本数和加权百分比展示，个人感觉这是脱裤子放屁，个人建议想和论文里一样的话
#做一个加权的基线表，再做一个不加权的基线表，之后在excel表格中手动替换百分比即可
#虽然代码也可以实现，但是改代码的时间可以手动调整10个表格了，所以建议手动

#下面做一个不加权的基线表
tab2 <- CreateTableOne(vars=allVars, strata="Cd_4", data=data, factorVars=fvars,addOverall = TRUE)

tab4Mat_noweight = print(tab2, #指定表格
                showAllLevels = TRUE, #显示所有类别
                factorVars = fvars,#指定分类变量
                nonnormal = nonvar,#指定非正态分布连续变量变量
                quote = FALSE, #不显示引号
                noSpaces = TRUE, #删除用于对齐的空格
                printToggle = FALSE)#不展示输出结果
# 保存为 CSV 格式文件
write.csv(tab4Mat_noweight, file = "myTable-before-mice-noweight.csv")




#查看每一列的缺失值
apply(data, 2, function(x) sum(is.na(x)))

#画协变量缺失比例柱状图，图S3
library("ggplot2")
library(lmtest)
library (scales)

load("data.RData")
#筛选需要画图的数据
data_NA = data %>% 
  select("Smokingstatus","Educationlevel","Failingkidneys","Maritalstatus","BMI","Hypertension",
         "WWI","Alcoholstatus","PIR","Physicalactivity")

df = data.frame(sapply(data_NA, function (x) sum(is.na (x))))#计算缺失值的数量
colnames(df) = "Missingvalue"#修改列名
df$per = percent(df$Missingvalue / nrow(data), accuracy = 0.01)#计算缺失值的比例
df$name = rownames(df)
df$color = ifelse((df$Missingvalue / nrow(data) < 0.05), "#1B9E77", "#E6AB02")#设置柱状图颜色

ggp <- ggplot(df, aes(reorder(name, -Missingvalue), Missingvalue)) +   
  geom_bar(stat = "identity",fill=df$color) +
  geom_label(aes(label=per),size=8 ,fill=df$color)

p = ggp +  coord_flip()

#保存图片为pdf
pdf("图s3.pdf")
p
dev.off()


#多重插补，用表S2中的方法进行插补
library(mice)
#筛选需要插补的数据
load("data.RData")
data$Educationlevel = factor(data$Educationlevel, levels=c(1,2,3,99999), labels=c("Below high school", "High school", "Above High school","Missing"))
data$Maritalstatus = factor(data$Maritalstatus, levels=c(1,2,3,99999), labels=c("Married / Living with partner", "Widowed / Divorced / Separated","Never married","Missing"))
data$Physicalactivity = cut(data$Physicalactivity, breaks = c(-Inf, 0,600,2000,99998, Inf), labels = c("Inactive","Moderate","Vigorous","Both moderate and vigorous ","Missing"), right=FALSE)
data$Smokingstatus = factor(data$Smokingstatus, levels=c(1,2,3,99999), labels=c("Never", "Former","Now","Missing"))
data$Alcoholstatus = factor(data$Alcoholstatus, levels=c(1,2,3,99999), labels=c("None", "Moderate","Excessive","Missing"))
data$Failingkidneys = factor(data$Failingkidneys, levels=c(1,2), labels=c("Yes", "No"))
data$Hypertension = factor(data$Hypertension, levels=c(1,0), labels=c("Yes", "No"))

data_NA = data %>% 
  select("Smokingstatus","Educationlevel","Failingkidneys","Maritalstatus","BMI","Hypertension",
         "WWI","Alcoholstatus","PIR","Physicalactivity")
#开始插补,数值变量默认用pmm方法，两因子变量默认用logreg法，多因子变量默认用polyreg方法
imp_data <- mice(data_NA, #数据集
                 m=5, # 5次插补，论文中是50次，运行有点慢，就设置成了5
                 printFlag = FALSE, #不显示历史记录
                 seed = 123456#s设置随机数种子
)

#查看各个变量的插补方法，与表S2中方法一致
imp_data$method
#保存插补后的完整数据，action 指定 m个完整数据集中的一个来展示，
data_NA = complete(imp_data, action = 2)
#更新data变量中的数据
data$Smokingstatus = data_NA$Smokingstatus
data$Educationlevel = data_NA$Educationlevel
data$Failingkidneys = data_NA$Failingkidneys
data$Maritalstatus = data_NA$Maritalstatus
data$BMI = data_NA$BMI
data$Hypertension = data_NA$Hypertension
data$WWI = data_NA$WWI
data$Alcoholstatus = data_NA$Alcoholstatus
data$PIR = data_NA$PIR
data$Physicalactivity = data_NA$Physicalactivity

save(data, file="data.RData")

#绘制变量插补后的基线表，表S3，和之前的基线表制作基本一致
load("data.RData")

data$Age = cut(data$Age, breaks = c(-Inf, 60, Inf), labels = c("<60",">=60"), right=FALSE)
data$Sex = factor(data$Sex, levels=c(1,2), labels=c("Male", "Female"))
data$Race = factor(data$Race, levels=c(1,2,3,4,5), labels=c("Non-Hispanic White","Non-Hispanic Black","Mexican American", "Other Hispanic","Other Race - Including Multi-Racial"))
#data$Educationlevel = factor(data$Educationlevel, levels=c(1,2,3), labels=c("Below high school", "High school", "Above High school"))
data$PIR = cut(data$PIR, breaks = c(-Inf, 1.3,3.5, Inf), labels = c("=<1.3",">1.3,=<3.5",">4.0"), right=FALSE)
data$BMI = cut(data$BMI, breaks = c(-Inf, 25,30, Inf), labels = c("=<25",">25,=<30",">30"), right=FALSE)
#data$Maritalstatus = factor(data$Maritalstatus, levels=c(1,2,3), labels=c("Married / Living with partner", "Widowed / Divorced / Separated","Never married"))
#data$Physicalactivity = cut(data$Physicalactivity, breaks = c(-Inf, 0,600,2000, Inf), labels = c("Inactive","Moderate","Vigorous","Both moderate and vigorous "), right=FALSE)
#data$Smokingstatus = factor(data$Smokingstatus, levels=c(1,2,3), labels=c("Never", "Former","Now"))
#data$Alcoholstatus = factor(data$Alcoholstatus, levels=c(1,2,3), labels=c("None", "Moderate","Excessive"))
#data$Failingkidneys = factor(data$Failingkidneys, levels=c(1,2), labels=c("Yes", "No"))
#data$Hypertension = factor(data$Hypertension, levels=c(1,0), labels=c("Yes", "No"))
#Cd加权四分位数分组
library(Hmisc)
result <- wtd.quantile(data$Cd, weights = data$WTMEC, probs = c(0,0.25, 0.5, 0.75,1))
data$Cd_4 = cut(data$Cd, breaks = c(quantile(result)[1:4], Inf), labels = c("Quantile1","Quantile2","Quantile3","Quantile4"), right=FALSE)

data_design2 <- svydesign(ids=~SDMVPSU,   #集群，抽样单元PSU
                         strata=~SDMVSTRA,   #分层
                         weights=~WTMEC,
                         nest=TRUE,
                         survey.lonely.psu="adjust",  #抽样单元为1时不报错
                         data=data)

Svytab3 <- svyCreateTableOne(vars=allVars, strata="Cd_4", data=data_design2, factorVars=fvars,addOverall = TRUE)

nonvar <- c("WWI")#指定非正态分布变量,默认用Kruskal-Wallis秩和检验

tab4Mat2 = print(Svytab3, #指定表格
                showAllLevels = TRUE, #显示所有类别
                factorVars = fvars,#指定分类变量
                nonnormal = nonvar,#指定非正态分布连续变量变量
                quote = FALSE, #不显示引号
                noSpaces = TRUE, #删除用于对齐的空格
                printToggle = FALSE)#不展示输出结果

# 保存为 CSV 格式文件
write.csv(tab4Mat2, file = "myTable-after—mice.csv")


#同样是再做一个不加劝的基线表，然后手动调整
#下面做一个不加权的基线表
tab3 <- CreateTableOne(vars=allVars, strata="Cd_4", data=data, factorVars=fvars,addOverall = TRUE)

tab4Mat2_noweight = print(tab3, #指定表格
                         showAllLevels = TRUE, #显示所有类别
                         factorVars = fvars,#指定分类变量
                         nonnormal = nonvar,#指定非正态分布连续变量变量
                         quote = FALSE, #不显示引号
                         noSpaces = TRUE, #删除用于对齐的空格
                         printToggle = FALSE)#不展示输出结果
# 保存为 CSV 格式文件
write.csv(tab4Mat2_noweight, file = "myTable-after-mice-noweight.csv")




