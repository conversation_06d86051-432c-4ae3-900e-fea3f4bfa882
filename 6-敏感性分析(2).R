#表s13
library(survey)
library(rlang)
library(survival)  # 用于Cox回归
library(boot)      # 用于Bootstrap检验
library(boot.pval)



#加载数据
load("data.RData")
data  = droplevels(data)

#分析完之后根据结果在PPT中绘图,因为是boot算法，有随机性，所以每次运行结果都会不太一样，但是不会差别太大

data_design <- svydesign(ids=~SDMVPSU,   #集群，抽样单元PSU
                         strata=~SDMVSTRA,   #分层
                         weights=~WTMEC,
                         nest=TRUE,
                         survey.lonely.psu="adjust",  #抽样单元为1时不报错
                         data=data)
dd<-datadist(data)
options(datadist='dd')

cor = "+ Age + Sex + Race + Educationlevel +
                 PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus +
                 Alcoholstatus+ Failingkidneys + Hypertension"
options(survey.lonely.psu = "adjust")

#定义计算中介效应的函数
Median_alalysiae = function(data,indices){
  
  d <- data[indices, ]  # 重抽样数据
  d_design= svydesign(ids=~SDMVPSU,   #集群，抽样单元PSU
                      strata=~SDMVSTRA,   #分层
                      weights=~WTMEC,
                      nest=TRUE,
                      survey.lonely.psu="adjust",  #抽样单元为1时不报错
                      data=d)
  
  cor = "+ Age + Sex + Race + Educationlevel +
                 PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus +
                 Alcoholstatus+ Failingkidneys + Hypertension"
  f1 = as.formula(paste0("Surv(permth_int, ", status, ")", "~", treat, cor))
  f2 = as.formula(paste0(mediator, "~", treat, cor))
  f3 = as.formula(paste0("Surv(permth_int, ", status, ")", "~", treat, "+", mediator, cor))
  
  fit_svy<- svycoxph(f1, design=d_design)
  #weight变量是权重,1总效应模型（X→结局）,Total Effect
  model_total<- coxph(f1, x=TRUE, y=TRUE,data=d, weights = (1/fit_svy$survey.design$prob)/mean(1/fit_svy$survey.design$prob))
  #提取HR和置信区间
  #total_result = ShowRegTable(model_total, exp = FALSE,digits = 3,pDigits = 3,printToggle=FALSE,quote = FALSE, ciFun = confint)
  beta_total <- coef(model_total)[treat]  # 总效应系数
  
  # 2：X→M的关系
  # 用线性回归
  model_XM <- lm(f2, data = d)
  #提取HR和置信区间
  #XM_result = ShowRegTable(model_XM, exp = FALSE,digits = 3,pDigits = 3,printToggle=FALSE,quote = FALSE, ciFun = confint)
  a <- coef(model_XM)[treat]  # X→M的效应
  
  # 3：（X和M共同预测结局）ADE（Average Direct Effects）
  model_direct <- coxph(f3, data = d,weights = (1/fit_svy$survey.design$prob)/mean(1/fit_svy$survey.design$prob))
  #提取HR和置信区间
  #direct_result = ShowRegTable(model_direct, exp = FALSE,digits = 3,pDigits = 3,printToggle=FALSE,quote = FALSE, ciFun = confint)
  beta_direct <- coef(model_direct)[treat]  # 直接效应系数
  b <- coef(model_direct)[mediator]  # M→结局的效应
  
  # 4. 计算效应量 ACME
  indirect_effect <- a * b  # 中介效应 (乘积法)
  proportion_mediated <- indirect_effect / beta_total  # 中介效应占比,PE
  return(c(beta_total, beta_direct, indirect_effect, proportion_mediated))
}


#得到最终的ACME，ADE，PE等和置信区间
Get_result = function(R = 10, data_design=data_design){
  boot_results <- boot(data = data, statistic = Median_alalysiae, R = R)#进行Bootstrap，1000次计算速度太慢了,次数越多，计算的P值越准确
  # 计算置信区间 (95%)
  ci_total <- boot.ci(boot_results, type = "norm", index = 1)  # 总效应CI,TE
  ci_direct <- boot.ci(boot_results, type = "norm", index = 2)  # 直接效应CI,ADE
  ci_indirect <- boot.ci(boot_results, type = "norm", index = 3)  # 中介效应CI,ACME
  ci_pm <- boot.ci(boot_results, type = "norm", index = 4)  # 中介效应占比CI,PE
  print(paste0("分析", treat, "对", status, "的中介效应，中介变量为", mediator))
  
  # 计算观测统计量
  obs_stat  = Median_alalysiae(data,seq(from=1, to=nrow(data)))
  
  # 计算Bootstrap P值（双侧检验）
  #计算ADE值
  cor = "+ Age + Sex + Race + Educationlevel +
                 PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus +
                 Alcoholstatus+ Failingkidneys + Hypertension"
  f3 = as.formula(paste0("Surv(permth_int, ", status, ")", "~", treat, "+", mediator, cor))
  fit_svy<- svycoxph(f3, design=data_design)
  model_direct <- coxph(f3, data = data,weights = (1/fit_svy$survey.design$prob)/mean(1/fit_svy$survey.design$prob))
  #提取HR和置信区间
  p_direct = data.frame(ShowRegTable(model_direct, exp = FALSE,digits = 3,pDigits = 3,printToggle=FALSE,quote = FALSE, ciFun = confint))["Cd", "p"]
  
  #p_total = 1-boot.pval(boot_results,theta_null = obs_stat[1], alternative = "two.sided", index=1,pval_precision=0.00001)
  #p_direct = 1-boot.pval(boot_results,theta_null = obs_stat[2], alternative = "two.sided", index=2,pval_precision=0.00001)
  p_indirect = 1-boot.pval(boot_results,theta_null = obs_stat[3], alternative = "two.sided", index=3,pval_precision=0.001)
  p_pm = 1-boot.pval(boot_results,theta_null = obs_stat[4], alternative = "two.sided", index=4,pval_precision=0.001)
  
  y = c(paste0(round(boot_results$t0[3],digit=3), "(",round(ci_indirect$normal[2],digit=3), ",", round(ci_indirect$normal[3],digit=3), ")"),p_indirect,
        paste0(round(boot_results$t0[2],digit=3), "(",round(ci_direct$normal[2],digit=3), ",", round(ci_direct$normal[3],digit=3), ")"), p_direct,
        paste0(round(boot_results$t0[4],digit=3), "(",round(ci_pm$normal[2],digit=3), ",", round(ci_pm$normal[3],digit=3), ")"), p_pm)
  
  return(y)
}

#生成表s13
Tables13 = data.frame(
  x1 = c("NA"),
  x2 = c("ACME"),
  x3 = c("ACME"),
  x4 = c("ADE"),
  x5 = c("ADE"),  
  x6 = c("PE"),
  x7 = c("PE")
)

#将数据写入tables13
Tables13[nrow(Tables13) + 1,] <- c("NA","Estimate (95% CI)","P value","Estimate (95% CI)","P value","Estimate (95% CI)","P value")

#结局为全因死亡率
Tables13[nrow(Tables13) + 1,] <- c("All-cause mortality","NA","NA","NA","NA","NA","NA")


#Neutrophils
treat="Cd";mediator="Neutrophils"; status="Allcausemortstat";l1 = Get_result(data_design = data_design)#分析Neutrophils变量的中介效应
Tables13[nrow(Tables13) + 1,] <- c(mediator,l1)#将数据写入Tables13
#Lymphocyte
treat="Cd";mediator="Lymphocyte"; status="Allcausemortstat";l1 = Get_result(data_design = data_design)#分析Lymphocyte变量的中介效应
Tables13[nrow(Tables13) + 1,] <- c(mediator,l1)#将数据写入Tables13
#Monocyte
treat="Cd";mediator="Monocyte"; status="Allcausemortstat";l1 = Get_result(data_design = data_design)#分析Monocyte变量的中介效应
Tables13[nrow(Tables13) + 1,] <- c(mediator,l1)#将数据写入Tables13
#Platelet
treat="Cd";mediator="Platelet"; status="Allcausemortstat";l1 = Get_result(data_design = data_design)#分析Platelet变量的中介效应
Tables13[nrow(Tables13) + 1,] <- c(mediator,l1)#将数据写入Tables13





#结局为CVD死亡率
Tables13[nrow(Tables13) + 1,] <- c("CVD mortality","NA","NA","NA","NA","NA","NA")


#Neutrophils
treat="Cd";mediator="Neutrophils"; status="CVDmortstat";l1 = Get_result(data_design = data_design)#分析Neutrophils变量的中介效应
Tables13[nrow(Tables13) + 1,] <- c(mediator,l1)#将数据写入Tables13
#Lymphocyte
treat="Cd";mediator="Lymphocyte"; status="CVDmortstat";l1 = Get_result(data_design = data_design)#分析Lymphocyte变量的中介效应
Tables13[nrow(Tables13) + 1,] <- c(mediator,l1)#将数据写入Tables13
#Monocyte
treat="Cd";mediator="Monocyte"; status="CVDmortstat";l1 = Get_result(data_design = data_design)#分析Monocyte变量的中介效应
Tables13[nrow(Tables13) + 1,] <- c(mediator,l1)#将数据写入Tables13
#Platelet
treat="Cd";mediator="Platelet"; status="CVDmortstat";l1 = Get_result(data_design = data_design)#分析Platelet变量的中介效应
Tables13[nrow(Tables13) + 1,] <- c(mediator,l1)#将数据写入Tables13




#结局为CANSER死亡率
Tables13[nrow(Tables13) + 1,] <- c("CANSER mortality","NA","NA","NA","NA","NA","NA")


#Neutrophils
treat="Cd";mediator="Neutrophils"; status="CANSERmortstat";l1 = Get_result(data_design = data_design)#分析Neutrophils变量的中介效应
Tables13[nrow(Tables13) + 1,] <- c(mediator,l1)#将数据写入Tables13
#Lymphocyte
treat="Cd";mediator="Lymphocyte"; status="CANSERmortstat";l1 = Get_result(data_design = data_design)#分析Lymphocyte变量的中介效应
Tables13[nrow(Tables13) + 1,] <- c(mediator,l1)#将数据写入Tables13
#Monocyte
treat="Cd";mediator="Monocyte"; status="CANSERmortstat";l1 = Get_result(data_design = data_design)#分析Monocyte变量的中介效应
Tables13[nrow(Tables13) + 1,] <- c(mediator,l1)#将数据写入Tables13
#Platelet
treat="Cd";mediator="Platelet"; status="CANSERmortstat";l1 = Get_result(data_design = data_design)#分析Platelet变量的中介效应
Tables13[nrow(Tables13) + 1,] <- c(mediator,l1)#将数据写入Tables13


# 保存为 CSV 格式文件
write.csv(Tables13, file = "Tables13.csv")








