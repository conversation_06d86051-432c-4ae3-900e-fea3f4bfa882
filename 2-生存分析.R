library(survival)
library(survminer)
library(survey)
library(scales)

load("data.RData")
result <- wtd.quantile(data$Cd, weights = data$WTMEC, probs = c(0,0.25, 0.5, 0.75,1))
data$Cd_4 = cut(data$Cd, breaks = c(quantile(result)[1:4], Inf), labels = c("Quantile1","Quantile2","Quantile3","Quantile4"), right=FALSE)
save(data, file="data.RData")


data_design <- svydesign(ids=~SDMVPSU,   #集群，抽样单元PSU
                         strata=~SDMVSTRA,   #分层
                         weights=~WTMEC,
                         nest=TRUE,
                         survey.lonely.psu="adjust",  #抽样单元为1时不报错
                         data=data)

dd<-datadist(data)
options(datadist='dd')
#构建带权重的生存模型，全因死亡率
fit_svy<- svycoxph(Surv(permth_int, Allcausemortstat) ~ Cd_4, design=data_design)
# 使用 survfit 函数拟合生存曲线,全因死亡结局的K-M曲线，图1A
fit1 <- survfit(Surv(permth_int, Allcausemortstat) ~ Cd_4, data = data,weights = (1/fit_svy$survey.design$prob)/mean(1/fit_svy$survey.design$prob))


surv_plot1 = ggsurvplot(
  fit1,
  data = data,
  size = 1,                 # 更改线条粗细
  ylim=c(0.4,1.0),
  # 配色方案，支持ggsci配色，自定义颜色，brewer palettes中的配色，等
  palette = "lancet",
  conf.int = FALSE,          # 可信区间
  pval = TRUE,              # log-rank P值，也可以提供一个数值
  pval.method = TRUE,       # 计算P值的方法
  log.rank.weights = "1",
  risk.table = TRUE,        # 增加risk table
  risk.table.col = "strata",# risk table根据分组使用不同颜色
  legend.labs = c("Q1", "Q2","Q3","Q4"),    # 图例标签
  risk.table.height = 0.25, # risk table高度
  ggtheme = theme_classic2()      # 主题，支持ggplot2及其扩展包的主题
)
#保存图片为pdf
pdf("RCS-Cd-All.pdf")
surv_plot1
dev.off()

#CVD死亡结局的K-M曲线，图1B
#构建带权重的生存模型，全因死亡率
fit_svy<- svycoxph(Surv(permth_int, CVDmortstat) ~ Cd_4, design=data_design)
# 使用 survfit 函数拟合生存曲线,全因死亡结局的K-M曲线，图1A
fit2 <- survfit(Surv(permth_int, CVDmortstat) ~ Cd_4, data = data,weights = (1/fit_svy$survey.design$prob)/mean(1/fit_svy$survey.design$prob))

surv_plot2 = ggsurvplot(
  fit2,
  data = data,
  size = 1,                 # 更改线条粗细
  ylim=c(0.75,1.0),
  # 配色方案，支持ggsci配色，自定义颜色，brewer palettes中的配色，等
  palette = "lancet",
  conf.int = FALSE,          # 可信区间
  pval = TRUE,              # log-rank P值，也可以提供一个数值
  pval.method = TRUE,       # 计算P值的方法
  log.rank.weights = "1",
  risk.table = TRUE,        # 增加risk table
  risk.table.col = "strata",# risk table根据分组使用不同颜色
  legend.labs = c("Q1", "Q2","Q3","Q4"),    # 图例标签
  risk.table.height = 0.25, # risk table高度
  ggtheme = theme_classic2()      # 主题，支持ggplot2及其扩展包的主题
)

surv_plot2
#保存图片为pdf
pdf("RCS-Cd-CVD.pdf")
surv_plot2
dev.off()

#cANSER死亡结局的K-M曲线，图1B
#构建带权重的生存模型，全因死亡率
fit_svy<- svycoxph(Surv(permth_int, CANSERmortstat) ~ Cd_4, design=data_design)
# 使用 survfit 函数拟合生存曲线,全因死亡结局的K-M曲线，图1A
fit3 <- survfit(Surv(permth_int, CANSERmortstat) ~ Cd_4, data = data,weights = (1/fit_svy$survey.design$prob)/mean(1/fit_svy$survey.design$prob))

surv_plot3 = ggsurvplot(
  fit3,
  data = data,
  size = 1,                 # 更改线条粗细
  ylim=c(0.8,1.0),
  # 配色方案，支持ggsci配色，自定义颜色，brewer palettes中的配色，等
  palette = "lancet",
  conf.int = FALSE,          # 可信区间
  pval = TRUE,              # log-rank P值，也可以提供一个数值
  pval.method = TRUE,       # 计算P值的方法
  log.rank.weights = "1",
  risk.table = TRUE,        # 增加risk table
  risk.table.col = "strata",# risk table根据分组使用不同颜色
  legend.labs = c("Q1", "Q2","Q3","Q4"),    # 图例标签
  risk.table.height = 0.25, # risk table高度
  ggtheme = theme_classic2()      # 主题，支持ggplot2及其扩展包的主题
)

surv_plot3
#保存图片为pdf
pdf("RCS-Cd-CANSER.pdf")
surv_plot3
dev.off()

#cox回归分析

#生成表2
Table2 = data.frame(
  x1 = c("NA"),
  x2 = c("Blood Cadmium Levels, HR (95% CI)"),
  x3 = c("NA"),
  x4 = c("NA"),
  x5 = c("NA"),
  x6 = c("P for trend"),
  x7 = c("Per ln-unit increase")
)
Table2[nrow(Table2) + 1,] <- c("NA","Blood Cadmium Levels, HR (95% CI)", "NA", "NA", "NA", "P for trend", "Per ln-unit increase")
Table2[nrow(Table2) + 1,] <- c("NA","Quantile 1","Quantile 2","Quantile 3","Quantile 4", "NA", "NA")
Q1 = data.frame(summary(data$Cd_4))["Quantile1",]
Q2 = data.frame(summary(data$Cd_4))["Quantile2",]
Q3 = data.frame(summary(data$Cd_4))["Quantile3",]
Q4 = data.frame(summary(data$Cd_4))["Quantile4",]


library(rms)
library(survey)
data  = droplevels(data)

#将Cd进行对数转换,为了不出现赋值，加1后再转换
data$Cd_log = rescale(log(data$Cd + 1),to=c(0,2))
data$Sex = factor(data$Sex, levels=c(1,2), labels=c("Male", "Female"))
data$Race = factor(data$Race, levels=c(1,2,3,4,5), labels=c("Non-Hispanic White","Non-Hispanic Black","Mexican American", "Other Hispanic","Other Race - Including Multi-Racial"))
data$Cd_level = as.numeric(data$Cd_4)

data_design <- svydesign(ids=~SDMVPSU,   #集群，抽样单元PSU
                         strata=~SDMVSTRA,   #分层
                         weights=~WTMEC,
                         nest=TRUE,
                         survey.lonely.psu="adjust",  #抽样单元为1时不报错
                         data=data)

cor_all = "+ Age + Sex + Race + Educationlevel +
                 PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus +
                 Alcoholstatus+ Failingkidneys + Hypertension"
cor_part = "+ Age + Sex + Race"
#计算HR，CI，P trend的函数
Cox_analysise = function(treat, treat_level, treat_con, status, data_design, cor){
  #treat分类变量，treat_level按1-n的连续变量，treat_con按实际值的连续变量
  #cor调整变量
  f1 = as.formula(paste0("Surv(permth_int, ", status, ")", "~", treat, cor))#做分类变量
  fit1 = svycoxph(f1, design = data_design)
  CI = round(data.frame(summary(fit1)$conf.int), digit=3)#提取HR和置信区间
  
  f2 = as.formula(paste0("Surv(permth_int, ", status, ")", "~", treat_level, cor))#计算p trend
  fit2 = svycoxph(f2, design = data_design)
  P = format.pval(data.frame(summary(fit2)$coefficients)[treat_level, "Pr...z.."],digit=3,eps=0.001)
  
  f3 = as.formula(paste0("Surv(permth_int, ", status, ")", "~", treat_con, cor))#做连续变量
  fit3 = svycoxph(f3, design = data_design)
  CI3 = round(data.frame(summary(fit3)$conf.int), digit=3)#提取HR和置信区间
  
  y = c("1.00 (Ref.) ",
        paste0(CI["Cd_4Quantile2", "exp.coef."], "(", CI["Cd_4Quantile2", "lower..95"],",", CI["Cd_4Quantile2", "upper..95"],")"),
        paste0(CI["Cd_4Quantile3", "exp.coef."], "(", CI["Cd_4Quantile3", "lower..95"],",", CI["Cd_4Quantile3", "upper..95"],")"),
        paste0(CI["Cd_4Quantile4", "exp.coef."], "(", CI["Cd_4Quantile4", "lower..95"],",", CI["Cd_4Quantile4", "upper..95"],")"),
        P,
        paste0(CI3[treat_con, "exp.coef."], "(", CI3[treat_con, "lower..95"],",",CI3[treat_con, "upper..95"],")")
        )
  
  return(y)
}


#全因死亡率
All_Q1 = sum(data$Cd_4 == "Quantile1" & data$Allcausemortstat == 1)
All_Q2 = sum(data$Cd_4 == "Quantile2" & data$Allcausemortstat == 1)
All_Q3 = sum(data$Cd_4 == "Quantile3" & data$Allcausemortstat == 1)
All_Q4 = sum(data$Cd_4 == "Quantile4" & data$Allcausemortstat == 1)
Table2[nrow(Table2) + 1,] <- c("All-cause mortality, No",paste0(All_Q1,"/",Q1),paste0(All_Q2,"/",Q2),paste0(All_Q3,"/",Q3),paste0(All_Q4,"/",Q4), "NA", "NA")
line = Cox_analysise("Cd_4", "Cd_level", "Cd", "Allcausemortstat",data_design,cor_part)#全因死亡率，部分调整
Table2[nrow(Table2) + 1,] <-c("Partially adjusted", line)#将数据写入表2中
line = Cox_analysise("Cd_4", "Cd_level", "Cd", "Allcausemortstat",data_design,cor_all)#全因死亡率，全部调整
Table2[nrow(Table2) + 1,] <-c("Fully adjusted", line)#将数据写入表2中

#CVD死亡率
CVD_Q1 = sum(data$Cd_4 == "Quantile1" & data$CVDmortstat == 1)
CVD_Q2 = sum(data$Cd_4 == "Quantile2" & data$CVDmortstat == 1)
CVD_Q3 = sum(data$Cd_4 == "Quantile3" & data$CVDmortstat == 1)
CVD_Q4 = sum(data$Cd_4 == "Quantile4" & data$CVDmortstat == 1)
Table2[nrow(Table2) + 1,] <- c("CVD mortality, No",paste0(CVD_Q1,"/",Q1),paste0(CVD_Q2,"/",Q2),paste0(CVD_Q3,"/",Q3),paste0(CVD_Q4,"/",Q4), "NA", "NA")
line = Cox_analysise("Cd_4", "Cd_level", "Cd", "CVDmortstat",data_design,cor_part)#CVD死亡率，部分调整
Table2[nrow(Table2) + 1,] <-c("Partially adjusted", line)#将数据写入表2中
line = Cox_analysise("Cd_4", "Cd_level", "Cd", "CVDmortstat",data_design,cor_all)#CVD死亡率，全部调整
Table2[nrow(Table2) + 1,] <-c("Fully adjusted", line)#将数据写入表2中

#CANSER死亡率
CANSER_Q1 = sum(data$Cd_4 == "Quantile1" & data$CANSERmortstat == 1)
CANSER_Q2 = sum(data$Cd_4 == "Quantile2" & data$CANSERmortstat == 1)
CANSER_Q3 = sum(data$Cd_4 == "Quantile3" & data$CANSERmortstat == 1)
CANSER_Q4 = sum(data$Cd_4 == "Quantile4" & data$CANSERmortstat == 1)
Table2[nrow(Table2) + 1,] <- c("CANSER mortality, No",paste0(CANSER_Q1,"/",Q1),paste0(CANSER_Q2,"/",Q2),paste0(CANSER_Q3,"/",Q3),paste0(CANSER_Q4,"/",Q4), "NA", "NA")
line = Cox_analysise("Cd_4", "Cd_level", "Cd", "CANSERmortstat",data_design,cor_part)#CANSER死亡率，部分调整
Table2[nrow(Table2) + 1,] <-c("Partially adjusted", line)#将数据写入表2中
line = Cox_analysise("Cd_4", "Cd_level", "Cd", "CANSERmortstat",data_design,cor_all)#CANSER死亡率，全部调整
Table2[nrow(Table2) + 1,] <-c("Fully adjusted", line)#将数据写入表2中

write.csv(Table2, file = "Table2.csv")



#部分调整模型,Cd做分类变量
All_part_cox <- svycoxph(Surv(permth_int, Allcausemortstat) ~ Cd_4 + Age + Sex + Race, design = data_design)
CVD_part_cox <- svycoxph(Surv(permth_int, CVDmortstat) ~ Cd_4 + Age + Sex + Race, design = data_design)
CANSER_part_cox <- svycoxph(Surv(permth_int, CANSERmortstat) ~ Cd_4 + Age + Sex + Race, design = data_design)

#部分调整模型,Cd做连续变量
All_part_cox_con <- svycoxph(Surv(permth_int, Allcausemortstat) ~ Cd + Age + Sex + Race, design = data_design)
CVD_part_cox_con <- svycoxph(Surv(permth_int, CVDmortstat) ~ Cd + Age + Sex + Race, design = data_design)
CANSER_part_cox_con <- svycoxph(Surv(permth_int, CANSERmortstat) ~ Cd + Age + Sex + Race, design = data_design)

#全调整模型,Cd做分类变量
All_all_cox <- svycoxph(Surv(permth_int, Allcausemortstat) ~ Cd_4 + Age + Sex + Race + Educationlevel +
                       PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus +
                       Alcoholstatus+ Failingkidneys + Hypertension, design = data_design)
CVD_all_cox <- svycoxph(Surv(permth_int, CVDmortstat) ~ Cd_4 + Age + Sex + Race + Educationlevel +
                       PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus +
                       Alcoholstatus+ Failingkidneys + Hypertension, design = data_design)
CANSER_all_cox <- svycoxph(Surv(permth_int, CANSERmortstat) ~ Cd_4 + Age + Sex + Race + Educationlevel +
                       PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus +
                       Alcoholstatus+ Failingkidneys + Hypertension, design = data_design)

#全调整模型,Cd做连续变量
All_all_cox_con <- svycoxph(Surv(permth_int, Allcausemortstat) ~ Cd + Age + Sex + Race + Educationlevel +
                       PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus +
                       Alcoholstatus+ Failingkidneys + Hypertension, design = data_design)
CVD_all_cox_con <- svycoxph(Surv(permth_int, CVDmortstat) ~ Cd + Age + Sex + Race + Educationlevel +
                       PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus +
                       Alcoholstatus+ Failingkidneys + Hypertension, design = data_design)
CANSER_all_cox_con <- svycoxph(Surv(permth_int, CANSERmortstat) ~ Cd + Age + Sex + Race + Educationlevel +
                          PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus +
                          Alcoholstatus+ Failingkidneys + Hypertension, design = data_design)


#Schoenfeld 残差法检验cox模型
#论文中没有说Schoenfeld 残差法检验的是哪个模型，也没有说Cd是连续变量还是分类变量
#也没有说P值是总的检验值，还是某个变量的检验值
#只有下面这几个模型才能得到论文中类似的结果
All_cox <- svycoxph(Surv(permth_int, Allcausemortstat) ~ Cd, design = data_design)
CVD_cox <- svycoxph(Surv(permth_int, CVDmortstat) ~ Cd, design = data_design)
CANSER_cox <- svycoxph(Surv(permth_int, CANSERmortstat) ~ Cd, design = data_design)

cox.zph(All_cox)
cox.zph(CVD_cox)
cox.zph(CANSER_cox)

#输出所有模型的C-index，均大于0.7，与论文结果一致
All_part_cox$concordance[6]
CVD_part_cox$concordance[6]
CANSER_part_cox$concordance[6]

All_part_cox_con$concordance[6]
CVD_part_cox_con$concordance[6]
CANSER_part_cox_con$concordance[6]

All_all_cox$concordance[6]
CVD_all_cox$concordance[6]
CANSER_all_cox$concordance[6]

All_all_cox_con$concordance[6]
CVD_all_cox_con$concordance[6]
CANSER_all_cox_con$concordance[6]

#计算方差膨胀因子,表s4
library(car)
#因为NMLR=NLR+MLR，这三个变量根本不能放到一个线性回归模型中去计算VIF，只能分开计算
#论文中也没有描述怎么分开计算，分开方式不同结果也不一样，表S4看个乐得了
model = lm(CVDmortstat ~ Cd_4 + GGT + UA + HDL +  NLR + MLR  + SII,  data = data)
vif(model)

model2 = lm(CVDmortstat ~ Cd_4 + UHR + NMLR + SIRI,  data = data)
vif(model2)


