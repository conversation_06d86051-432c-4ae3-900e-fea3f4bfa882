#中介效应分析
library(survey)
library(rlang)
library(survival)  # 用于Cox回归
library(boot)      # 用于Bootstrap检验
library(boot.pval)


#加载数据
load("data.RData")
data  = droplevels(data)

#分析完之后根据结果在PPT中绘图,因为是boot算法，有随机性，所以每次运行结果都会不太一样，但是不会差别太大
#图3根据图s5的数据在ppt或者visio中画图即可，这里就不画了

data_design <- svydesign(ids=~SDMVPSU,   #集群，抽样单元PSU
                         strata=~SDMVSTRA,   #分层
                         weights=~WTMEC,
                         nest=TRUE,
                         survey.lonely.psu="adjust",  #抽样单元为1时不报错
                         data=data)
dd<-datadist(data)
options(datadist='dd')

cor = "+ Age + Sex + Race + Educationlevel +
                 PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus +
                 Alcoholstatus+ Failingkidneys + Hypertension"
options(survey.lonely.psu = "adjust")

#定义计算中介效应的函数
Median_alalysiae = function(data,indices){
  
  d <- data[indices, ]  # 重抽样数据
  d_design= svydesign(ids=~SDMVPSU,   #集群，抽样单元PSU
                      strata=~SDMVSTRA,   #分层
                      weights=~WTMEC,
                      nest=TRUE,
                      survey.lonely.psu="adjust",  #抽样单元为1时不报错
                      data=d)
  
  cor = "+ Age + Sex + Race + Educationlevel +
                 PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus +
                 Alcoholstatus+ Failingkidneys + Hypertension"
  f1 = as.formula(paste0("Surv(permth_int, ", status, ")", "~", treat, cor))
  f2 = as.formula(paste0(mediator, "~", treat, cor))
  f3 = as.formula(paste0("Surv(permth_int, ", status, ")", "~", treat, "+", mediator, cor))
  
  fit_svy<- svycoxph(f1, design=d_design)
  #weight变量是权重,1总效应模型（X→结局）,Total Effect
  model_total<- coxph(f1, x=TRUE, y=TRUE,data=d, weights = (1/fit_svy$survey.design$prob)/mean(1/fit_svy$survey.design$prob))
  #提取HR和置信区间
  #total_result = ShowRegTable(model_total, exp = FALSE,digits = 3,pDigits = 3,printToggle=FALSE,quote = FALSE, ciFun = confint)
  beta_total <- coef(model_total)[treat]  # 总效应系数
  
  # 2：X→M的关系
  # 用线性回归
  model_XM <- lm(f2, data = d)
  #提取HR和置信区间
  #XM_result = ShowRegTable(model_XM, exp = FALSE,digits = 3,pDigits = 3,printToggle=FALSE,quote = FALSE, ciFun = confint)
  a <- coef(model_XM)[treat]  # X→M的效应
  
  # 3：（X和M共同预测结局）ADE（Average Direct Effects）
  model_direct <- coxph(f3, data = d,weights = (1/fit_svy$survey.design$prob)/mean(1/fit_svy$survey.design$prob))
  #提取HR和置信区间
  #direct_result = ShowRegTable(model_direct, exp = FALSE,digits = 3,pDigits = 3,printToggle=FALSE,quote = FALSE, ciFun = confint)
  beta_direct <- coef(model_direct)[treat]  # 直接效应系数
  b <- coef(model_direct)[mediator]  # M→结局的效应
  
  # 4. 计算效应量 ACME
  indirect_effect <- a * b  # 中介效应 (乘积法)
  proportion_mediated <- indirect_effect / beta_total  # 中介效应占比,PE
  return(c(beta_total, beta_direct, indirect_effect, proportion_mediated))
}


#得到最终的ACME，ADE，PE等和置信区间
Get_result = function(R = 10, data_design=data_design){
  boot_results <- boot(data = data, statistic = Median_alalysiae, R = R)#进行Bootstrap，1000次计算速度太慢了,次数越多，计算的P值越准确
  # 计算置信区间 (95%)
  ci_total <- boot.ci(boot_results, type = "norm", index = 1)  # 总效应CI,TE
  ci_direct <- boot.ci(boot_results, type = "norm", index = 2)  # 直接效应CI,ADE
  ci_indirect <- boot.ci(boot_results, type = "norm", index = 3)  # 中介效应CI,ACME
  ci_pm <- boot.ci(boot_results, type = "norm", index = 4)  # 中介效应占比CI,PE
  print(paste0("分析", treat, "对", status, "的中介效应，中介变量为", mediator))
  
  # 计算观测统计量
  obs_stat  = Median_alalysiae(data,seq(from=1, to=nrow(data)))
  
  # 计算Bootstrap P值（双侧检验）
  #计算ADE值
  cor = "+ Age + Sex + Race + Educationlevel +
                 PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus +
                 Alcoholstatus+ Failingkidneys + Hypertension"
  f3 = as.formula(paste0("Surv(permth_int, ", status, ")", "~", treat, "+", mediator, cor))
  fit_svy<- svycoxph(f3, design=data_design)
  model_direct <- coxph(f3, data = data,weights = (1/fit_svy$survey.design$prob)/mean(1/fit_svy$survey.design$prob))
  #提取HR和置信区间
  p_direct = data.frame(ShowRegTable(model_direct, exp = FALSE,digits = 3,pDigits = 3,printToggle=FALSE,quote = FALSE, ciFun = confint))["Cd", "p"]

  #p_total = 1-boot.pval(boot_results,theta_null = obs_stat[1], alternative = "two.sided", index=1,pval_precision=0.00001)
  #p_direct = 1-boot.pval(boot_results,theta_null = obs_stat[2], alternative = "two.sided", index=2,pval_precision=0.00001)
  p_indirect = 1-boot.pval(boot_results,theta_null = obs_stat[3], alternative = "two.sided", index=3,pval_precision=0.001)
  p_pm = 1-boot.pval(boot_results,theta_null = obs_stat[4], alternative = "two.sided", index=4,pval_precision=0.001)
  
  y = c(paste0(round(boot_results$t0[3],digit=3), "(",round(ci_indirect$normal[2],digit=3), ",", round(ci_indirect$normal[3],digit=3), ")"),p_indirect,
        paste0(round(boot_results$t0[2],digit=3), "(",round(ci_direct$normal[2],digit=3), ",", round(ci_direct$normal[3],digit=3), ")"), p_direct,
        paste0(round(boot_results$t0[4],digit=3), "(",round(ci_pm$normal[2],digit=3), ",", round(ci_pm$normal[3],digit=3), ")"), p_pm)
  
  return(y)
}

#生成表s5
Tables5 = data.frame(
  x1 = c("NA"),
  x2 = c("ACME"),
  x3 = c("ACME"),
  x4 = c("ADE"),
  x5 = c("ADE"),  
  x6 = c("PE"),
  x7 = c("PE")
)

#将数据写入tableS5
Tables5[nrow(Tables5) + 1,] <- c("NA","Estimate (95% CI)","P value","Estimate (95% CI)","P value","Estimate (95% CI)","P value")

#结局为全因死亡率
Tables5[nrow(Tables5) + 1,] <- c("All-cause mortality","NA","NA","NA","NA","NA","NA")

#GGT
treat="Cd";mediator="GGT"; status="Allcausemortstat";l1 = Get_result(data_design = data_design)#分析GGT变量的中介效应
Tables5[nrow(Tables5) + 1,] <- c(mediator,l1)#将数据写入tables5
#UA
treat="Cd";mediator="UA"; status="Allcausemortstat";l1 = Get_result(data_design = data_design)#分析UA变量的中介效应
Tables5[nrow(Tables5) + 1,] <- c(mediator,l1)#将数据写入tables5
#HDL
treat="Cd";mediator="HDL"; status="Allcausemortstat";l1 = Get_result(data_design = data_design)#分析HDL变量的中介效应
Tables5[nrow(Tables5) + 1,] <- c(mediator,l1)#将数据写入tables5
#UHR
treat="Cd";mediator="UHR"; status="Allcausemortstat";l1 = Get_result(data_design = data_design)#分析UHR变量的中介效应
Tables5[nrow(Tables5) + 1,] <- c(mediator,l1)#将数据写入tables5
#NLR
treat="Cd";mediator="NLR"; status="Allcausemortstat";l1 = Get_result(data_design = data_design)#分析NLR变量的中介效应
Tables5[nrow(Tables5) + 1,] <- c(mediator,l1)#将数据写入tables5
#MLR
treat="Cd";mediator="MLR"; status="Allcausemortstat";l1 = Get_result(data_design = data_design)#分析MLR变量的中介效应
Tables5[nrow(Tables5) + 1,] <- c(mediator,l1)#将数据写入tables5
#NMLR
treat="Cd";mediator="NMLR"; status="Allcausemortstat";l1 = Get_result(data_design = data_design)#分析NMLR变量的中介效应
Tables5[nrow(Tables5) + 1,] <- c(mediator,l1)#将数据写入tables5
#SIRI
treat="Cd";mediator="SIRI"; status="Allcausemortstat";l1 = Get_result(data_design = data_design)#分析SIRI变量的中介效应
Tables5[nrow(Tables5) + 1,] <- c(mediator,l1)#将数据写入tables5
#SII
treat="Cd";mediator="SII"; status="Allcausemortstat";l1 = Get_result(data_design = data_design)#分析SII变量的中介效应
Tables5[nrow(Tables5) + 1,] <- c(mediator,l1)#将数据写入tables5

#结局为CVD死亡率
Tables5[nrow(Tables5) + 1,] <- c("CVD mortality","NA","NA","NA","NA","NA","NA")

#GGT
treat="Cd";mediator="GGT"; status="CVDmortstat";l1 = Get_result(data_design = data_design)#分析GGT变量的中介效应
Tables5[nrow(Tables5) + 1,] <- c(mediator,l1)#将数据写入tables5
#UA
treat="Cd";mediator="UA"; status="CVDmortstat";l1 = Get_result(data_design = data_design)#分析UA变量的中介效应
Tables5[nrow(Tables5) + 1,] <- c(mediator,l1)#将数据写入tables5
#HDL
treat="Cd";mediator="HDL"; status="CVDmortstat";l1 = Get_result(data_design = data_design)#分析HDL变量的中介效应
Tables5[nrow(Tables5) + 1,] <- c(mediator,l1)#将数据写入tables5
#UHR
treat="Cd";mediator="UHR"; status="CVDmortstat";l1 = Get_result(data_design = data_design)#分析UHR变量的中介效应
Tables5[nrow(Tables5) + 1,] <- c(mediator,l1)#将数据写入tables5
#NLR
treat="Cd";mediator="NLR"; status="CVDmortstat";l1 = Get_result(data_design = data_design)#分析NLR变量的中介效应
Tables5[nrow(Tables5) + 1,] <- c(mediator,l1)#将数据写入tables5
#MLR
treat="Cd";mediator="MLR"; status="CVDmortstat";l1 = Get_result(data_design = data_design)#分析MLR变量的中介效应
Tables5[nrow(Tables5) + 1,] <- c(mediator,l1)#将数据写入tables5
#NMLR
treat="Cd";mediator="NMLR"; status="CVDmortstat";l1 = Get_result(data_design = data_design)#分析NMLR变量的中介效应
Tables5[nrow(Tables5) + 1,] <- c(mediator,l1)#将数据写入tables5
#SIRI
treat="Cd";mediator="SIRI"; status="CVDmortstat";l1 = Get_result(data_design = data_design)#分析SIRI变量的中介效应
Tables5[nrow(Tables5) + 1,] <- c(mediator,l1)#将数据写入tables5
#SII
treat="Cd";mediator="SII"; status="CVDmortstat";l1 = Get_result(data_design = data_design)#分析SII变量的中介效应
Tables5[nrow(Tables5) + 1,] <- c(mediator,l1)#将数据写入tables5


#结局为CANSER死亡率
Tables5[nrow(Tables5) + 1,] <- c("CANSER mortality","NA","NA","NA","NA","NA","NA")

#GGT
treat="Cd";mediator="GGT"; status="CANSERmortstat";l1 = Get_result(data_design = data_design)#分析GGT变量的中介效应
Tables5[nrow(Tables5) + 1,] <- c(mediator,l1)#将数据写入tables5
#UA
treat="Cd";mediator="UA"; status="CANSERmortstat";l1 = Get_result(data_design = data_design)#分析UA变量的中介效应
Tables5[nrow(Tables5) + 1,] <- c(mediator,l1)#将数据写入tables5
#HDL
treat="Cd";mediator="HDL"; status="CANSERmortstat";l1 = Get_result(data_design = data_design)#分析HDL变量的中介效应
Tables5[nrow(Tables5) + 1,] <- c(mediator,l1)#将数据写入tables5
#UHR
treat="Cd";mediator="UHR"; status="CANSERmortstat";l1 = Get_result(data_design = data_design)#分析UHR变量的中介效应
Tables5[nrow(Tables5) + 1,] <- c(mediator,l1)#将数据写入tables5
#NLR
treat="Cd";mediator="NLR"; status="CANSERmortstat";l1 = Get_result(data_design = data_design)#分析NLR变量的中介效应
Tables5[nrow(Tables5) + 1,] <- c(mediator,l1)#将数据写入tables5
#MLR
treat="Cd";mediator="MLR"; status="CANSERmortstat";l1 = Get_result(data_design = data_design)#分析MLR变量的中介效应
Tables5[nrow(Tables5) + 1,] <- c(mediator,l1)#将数据写入tables5
#NMLR
treat="Cd";mediator="NMLR"; status="CANSERmortstat";l1 = Get_result(data_design = data_design)#分析NMLR变量的中介效应
Tables5[nrow(Tables5) + 1,] <- c(mediator,l1)#将数据写入tables5
#SIRI
treat="Cd";mediator="SIRI"; status="CANSERmortstat";l1 = Get_result(data_design = data_design)#分析SIRI变量的中介效应
Tables5[nrow(Tables5) + 1,] <- c(mediator,l1)#将数据写入tables5
#SII
treat="Cd";mediator="SII"; status="CANSERmortstat";l1 = Get_result(data_design = data_design)#分析SII变量的中介效应
Tables5[nrow(Tables5) + 1,] <- c(mediator,l1)#将数据写入tables5


# 保存为 CSV 格式文件
write.csv(Tables5, file = "Tables5.csv")





Interaction_analysise = function(treat, mediator, status, data_design){
  cor = "+ Age + Sex + Race + Educationlevel +
                 PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus +
                 Alcoholstatus+ Failingkidneys + Hypertension"
  f1 = as.formula(paste0("Surv(permth_int, ", status, ")", "~", treat, "+", mediator, "+",treat, "*", mediator, cor))
  #构建模型
  fit1 <- svycoxph(f1, design = data_design)
  #提取P值
  P = data.frame(summary(fit1)$coefficients)[,c("exp.coef.", "Pr...z..")]
  #提取HR和置信区间
  CI = round(data.frame(summary(fit1)$conf.int), digit=3)
  
  y = c(mediator, paste0(CI[treat,"exp.coef."], "(", CI[treat,"lower..95"], ",", CI[treat,"upper..95"], ")"), 
        format.pval(P[treat, "Pr...z.."],digit=3,eps=0.001), 
        paste0(CI[mediator,"exp.coef."], "(", CI[mediator,"lower..95"], ",", CI[mediator,"upper..95"], ")"), 
        format.pval(P[mediator, "Pr...z.."],digit=3,eps=0.001),
        paste0(CI[paste0(treat, ":", mediator),"exp.coef."], "(", CI[paste0(treat, ":", mediator),"lower..95"], ",", CI[paste0(treat, ":", mediator),"upper..95"], ")"), 
        format.pval(P[paste0(treat, ":", mediator), "Pr...z.."],digit=3,eps=0.001))
  return(y)
}

#生成表s6
Tables6 = data.frame(
  x1 = c("NA"),
  x2 = c("Single Cd effect"),
  x3 = c("Single Cd effect"),
  x4 = c("Single index effect"),
  x5 = c("Single index effect"),  
  x6 = c("Interaction effect"),
  x7 = c("Interaction effect")
)
#将数据写入tableS6
Tables6[nrow(Tables6) + 1,] <- c("NA","HR (95% CI)","P value","HR (95% CI)","P value","HR (95% CI)","P value")
#结局为全因死亡率
Tables6[nrow(Tables6) + 1,] <- c("All-cause mortality","NA","NA","NA","NA","NA","NA")

l2 = Interaction_analysise("Cd", "GGT", "Allcausemortstat", data_design)#分析Cd与GGT的交互作用
Tables6[nrow(Tables6) + 1,] <- l2
l2 = Interaction_analysise("Cd", "UA", "Allcausemortstat", data_design)#分析Cd与UA的交互作用
Tables6[nrow(Tables6) + 1,] <- l2
l2 = Interaction_analysise("Cd", "HDL", "Allcausemortstat", data_design)#分析Cd与HDL的交互作用
Tables6[nrow(Tables6) + 1,] <- l2
l2 = Interaction_analysise("Cd", "UHR", "Allcausemortstat", data_design)#分析Cd与UHR的交互作用
Tables6[nrow(Tables6) + 1,] <- l2
l2 = Interaction_analysise("Cd", "NLR", "Allcausemortstat", data_design)#分析Cd与NLR的交互作用
Tables6[nrow(Tables6) + 1,] <- l2
l2 = Interaction_analysise("Cd", "MLR", "Allcausemortstat", data_design)#分析Cd与MLR的交互作用
Tables6[nrow(Tables6) + 1,] <- l2
l2 = Interaction_analysise("Cd", "NMLR", "Allcausemortstat", data_design)#分析Cd与NMLR的交互作用
Tables6[nrow(Tables6) + 1,] <- l2
l2 = Interaction_analysise("Cd", "SIRI", "Allcausemortstat", data_design)#分析Cd与SIRI的交互作用
Tables6[nrow(Tables6) + 1,] <- l2
l2 = Interaction_analysise("Cd", "SII", "Allcausemortstat", data_design)#分析Cd与SII的交互作用
Tables6[nrow(Tables6) + 1,] <- l2


#结局为CVD死亡率
Tables6[nrow(Tables6) + 1,] <- c("CVD mortality","NA","NA","NA","NA","NA","NA")

l2 = Interaction_analysise("Cd", "GGT", "CVDmortstat", data_design)#分析Cd与GGT的交互作用
Tables6[nrow(Tables6) + 1,] <- l2
l2 = Interaction_analysise("Cd", "UA", "CVDmortstat", data_design)#分析Cd与UA的交互作用
Tables6[nrow(Tables6) + 1,] <- l2
l2 = Interaction_analysise("Cd", "HDL", "CVDmortstat", data_design)#分析Cd与HDL的交互作用
Tables6[nrow(Tables6) + 1,] <- l2
l2 = Interaction_analysise("Cd", "UHR", "CVDmortstat", data_design)#分析Cd与UHR的交互作用
Tables6[nrow(Tables6) + 1,] <- l2
l2 = Interaction_analysise("Cd", "NLR", "CVDmortstat", data_design)#分析Cd与NLR的交互作用
Tables6[nrow(Tables6) + 1,] <- l2
l2 = Interaction_analysise("Cd", "MLR", "CVDmortstat", data_design)#分析Cd与MLR的交互作用
Tables6[nrow(Tables6) + 1,] <- l2
l2 = Interaction_analysise("Cd", "NMLR", "CVDmortstat", data_design)#分析Cd与NMLR的交互作用
Tables6[nrow(Tables6) + 1,] <- l2
l2 = Interaction_analysise("Cd", "SIRI", "CVDmortstat", data_design)#分析Cd与SIRI的交互作用
Tables6[nrow(Tables6) + 1,] <- l2
l2 = Interaction_analysise("Cd", "SII", "CVDmortstat", data_design)#分析Cd与SII的交互作用
Tables6[nrow(Tables6) + 1,] <- l2


#结局为CANSER死亡率
Tables6[nrow(Tables6) + 1,] <- c("CANSER mortality","NA","NA","NA","NA","NA","NA")

l2 = Interaction_analysise("Cd", "GGT", "CANSERmortstat", data_design)#分析Cd与GGT的交互作用
Tables6[nrow(Tables6) + 1,] <- l2
l2 = Interaction_analysise("Cd", "UA", "CANSERmortstat", data_design)#分析Cd与UA的交互作用
Tables6[nrow(Tables6) + 1,] <- l2
l2 = Interaction_analysise("Cd", "HDL", "CANSERmortstat", data_design)#分析Cd与HDL的交互作用
Tables6[nrow(Tables6) + 1,] <- l2
l2 = Interaction_analysise("Cd", "UHR", "CANSERmortstat", data_design)#分析Cd与UHR的交互作用
Tables6[nrow(Tables6) + 1,] <- l2
l2 = Interaction_analysise("Cd", "NLR", "CANSERmortstat", data_design)#分析Cd与NLR的交互作用
Tables6[nrow(Tables6) + 1,] <- l2
l2 = Interaction_analysise("Cd", "MLR", "CANSERmortstat", data_design)#分析Cd与MLR的交互作用
Tables6[nrow(Tables6) + 1,] <- l2
l2 = Interaction_analysise("Cd", "NMLR", "CANSERmortstat", data_design)#分析Cd与NMLR的交互作用
Tables6[nrow(Tables6) + 1,] <- l2
l2 = Interaction_analysise("Cd", "SIRI", "CANSERmortstat", data_design)#分析Cd与SIRI的交互作用
Tables6[nrow(Tables6) + 1,] <- l2
l2 = Interaction_analysise("Cd", "SII", "CANSERmortstat", data_design)#分析Cd与SII的交互作用
Tables6[nrow(Tables6) + 1,] <- l2



# 保存为 CSV 格式文件
write.csv(Tables6, file = "Tables6.csv")







#计算表S7中根据糖尿病分层之后的中介分析数据
#加载数据
load("data.RData")
data  = droplevels(data)

#分析完之后根据结果在PPT中绘图,因为是boot算法，有随机性，所以每次运行结果都会不太一样，但是不会差别太大
#图3根据图s5的数据在ppt或者visio中画图即可，这里就不画了

data_design <- svydesign(ids=~SDMVPSU,   #集群，抽样单元PSU
                         strata=~SDMVSTRA,   #分层
                         weights=~WTMEC,
                         nest=TRUE,
                         survey.lonely.psu="adjust",  #抽样单元为1时不报错
                         data=data)
data_design2 = subset(data_design, DIQ == 1)#糖尿病前期的样本
data_design3 = subset(data_design, DIQ == 2)#糖尿病的样本

#生成表s7
Tables7 = data.frame(
  x1 = c("NA"),
  x2 = c("ACME"),
  x3 = c("ACME"),
  x4 = c("ADE"),
  x5 = c("ADE"),  
  x6 = c("PE"),
  x7 = c("PE")
)

#将数据写入tableS7
Tables7[nrow(Tables7) + 1,] <- c("NA","Estimate (95% CI)","P value","Estimate (95% CI)","P value","Estimate (95% CI)","P value")


#重写计算中介效应的函数
Median_alalysiae_DM = function(data,indices){
  
  d <- data[indices, ]  # 重抽样数据
  d_design= svydesign(ids=~SDMVPSU,   #集群，抽样单元PSU
                      strata=~SDMVSTRA,   #分层
                      weights=~WTMEC,
                      nest=TRUE,
                      survey.lonely.psu="adjust",  #抽样单元为1时不报错
                      data=d)
  if (variable_sub == "preDM"){
    d_design = subset(d_design, DIQ == 1)#糖尿病前期的样本
    d = subset(d, DIQ == 1)
  }else if(variable_sub == "DM"){
    d_design = subset(d_design, DIQ == 2)#糖尿病的样本
    d = subset(d, DIQ == 2)
  }
  
  
  
  cor = "+ Age + Sex + Race + Educationlevel +
                 PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus +
                 Alcoholstatus+ Failingkidneys + Hypertension"
  f1 = as.formula(paste0("Surv(permth_int, ", status, ")", "~", treat, cor))
  f2 = as.formula(paste0(mediator, "~", treat, cor))
  f3 = as.formula(paste0("Surv(permth_int, ", status, ")", "~", treat, "+", mediator, cor))
  
  fit_svy<- svycoxph(f1, design=d_design)
  #weight变量是权重,1总效应模型（X→结局）,Total Effect
  model_total<- coxph(f1, x=TRUE, y=TRUE,data=d, weights = (1/fit_svy$survey.design$prob)/mean(1/fit_svy$survey.design$prob))
  #提取HR和置信区间
  #total_result = ShowRegTable(model_total, exp = FALSE,digits = 3,pDigits = 3,printToggle=FALSE,quote = FALSE, ciFun = confint)
  beta_total <- coef(model_total)[treat]  # 总效应系数
  
  # 2：X→M的关系
  # 用线性回归
  model_XM <- lm(f2, data = d)
  #提取HR和置信区间
  #XM_result = ShowRegTable(model_XM, exp = FALSE,digits = 3,pDigits = 3,printToggle=FALSE,quote = FALSE, ciFun = confint)
  a <- coef(model_XM)[treat]  # X→M的效应
  
  # 3：（X和M共同预测结局）ADE（Average Direct Effects）
  model_direct <- coxph(f3, data = d,weights = (1/fit_svy$survey.design$prob)/mean(1/fit_svy$survey.design$prob))
  #提取HR和置信区间
  #direct_result = ShowRegTable(model_direct, exp = FALSE,digits = 3,pDigits = 3,printToggle=FALSE,quote = FALSE, ciFun = confint)
  beta_direct <- coef(model_direct)[treat]  # 直接效应系数
  b <- coef(model_direct)[mediator]  # M→结局的效应
  
  # 4. 计算效应量 ACME
  indirect_effect <- a * b  # 中介效应 (乘积法)
  proportion_mediated <- indirect_effect / beta_total  # 中介效应占比,PE
  return(c(beta_total, beta_direct, indirect_effect, proportion_mediated))
}


#重写得到最终的ACME，ADE，PE等和置信区间的函数
Get_result_DM = function(R = 10, data_design=data_design){
  boot_results <- boot(data = data, statistic = Median_alalysiae_DM, R = R)#进行Bootstrap，1000次计算速度太慢了,次数越多，计算的P值越准确
  # 计算置信区间 (95%)
  ci_total <- boot.ci(boot_results, type = "norm", index = 1)  # 总效应CI,TE
  ci_direct <- boot.ci(boot_results, type = "norm", index = 2)  # 直接效应CI,ADE
  ci_indirect <- boot.ci(boot_results, type = "norm", index = 3)  # 中介效应CI,ACME
  ci_pm <- boot.ci(boot_results, type = "norm", index = 4)  # 中介效应占比CI,PE
  print(paste0("分析", treat, "对", status, "的中介效应，中介变量为", mediator))
  
  # 计算观测统计量
  obs_stat  = Median_alalysiae_DM(data,seq(from=1, to=nrow(data)))
  
  # 计算Bootstrap P值（双侧检验）
  #计算ADE值
  cor = "+ Age + Sex + Race + Educationlevel +
                 PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus +
                 Alcoholstatus+ Failingkidneys + Hypertension"
  f3 = as.formula(paste0("Surv(permth_int, ", status, ")", "~", treat, "+", mediator, cor))
  fit_svy<- svycoxph(f3, design=data_design)
  
  if (variable_sub == "preDM"){
    d_ = subset(data, DIQ == 1)
  }else if(variable_sub == "DM"){
    d_ = subset(data, DIQ == 2)
  }
  
  model_direct <- coxph(f3, data = d_,weights = (1/fit_svy$survey.design$prob)/mean(1/fit_svy$survey.design$prob))
  #提取HR和置信区间
  p_direct = data.frame(ShowRegTable(model_direct, exp = FALSE,digits = 3,pDigits = 3,printToggle=FALSE,quote = FALSE, ciFun = confint))["Cd", "p"]
  
  #p_total = 1-boot.pval(boot_results,theta_null = obs_stat[1], alternative = "two.sided", index=1,pval_precision=0.00001)
  #p_direct = 1-boot.pval(boot_results,theta_null = obs_stat[2], alternative = "two.sided", index=2,pval_precision=0.00001)
  p_indirect = 1-boot.pval(boot_results,theta_null = obs_stat[3], alternative = "two.sided", index=3,pval_precision=0.001)
  p_pm = 1-boot.pval(boot_results,theta_null = obs_stat[4], alternative = "two.sided", index=4,pval_precision=0.001)
  
  y = c(paste0(round(boot_results$t0[3],digit=3), "(",round(ci_indirect$normal[2],digit=3), ",", round(ci_indirect$normal[3],digit=3), ")"),p_indirect,
        paste0(round(boot_results$t0[2],digit=3), "(",round(ci_direct$normal[2],digit=3), ",", round(ci_direct$normal[3],digit=3), ")"), p_direct,
        paste0(round(boot_results$t0[4],digit=3), "(",round(ci_pm$normal[2],digit=3), ",", round(ci_pm$normal[3],digit=3), ")"), p_pm)
  
  return(y)
}

#GGT
Tables7[nrow(Tables7) + 1,] <- c("GGT","NA","NA","NA","NA","NA","NA")
treat="Cd";mediator="GGT"; status="Allcausemortstat";variable_sub="preDM";l1 = Get_result_DM(data_design = data_design2)#分析GGT变量在糖尿病前期样本的中介效应
Tables7[nrow(Tables7) + 1,] <- c(variable_sub,l1)#将数据写入Tables7
treat="Cd";mediator="GGT"; status="Allcausemortstat";variable_sub="DM";l1 = Get_result_DM(data_design = data_design3)#分析GGT变量在糖尿病前期样本的中介效应
Tables7[nrow(Tables7) + 1,] <- c(variable_sub,l1)#将数据写入Tables7
#UA
Tables7[nrow(Tables7) + 1,] <- c("UA","NA","NA","NA","NA","NA","NA")
treat="Cd";mediator="UA"; status="Allcausemortstat";variable_sub="preDM";l1 = Get_result_DM(data_design = data_design2)#分析UA变量在糖尿病前期样本的中介效应
Tables7[nrow(Tables7) + 1,] <- c(variable_sub,l1)#将数据写入Tables7
treat="Cd";mediator="UA"; status="Allcausemortstat";variable_sub="DM";l1 = Get_result_DM(data_design = data_design3)#分析UA变量在糖尿病前期样本的中介效应
Tables7[nrow(Tables7) + 1,] <- c(variable_sub,l1)#将数据写入Tables7
#HDL
Tables7[nrow(Tables7) + 1,] <- c("HDL","NA","NA","NA","NA","NA","NA")
treat="Cd";mediator="HDL"; status="Allcausemortstat";variable_sub="preDM";l1 = Get_result_DM(data_design = data_design2)#分析HDL变量在糖尿病前期样本的中介效应
Tables7[nrow(Tables7) + 1,] <- c(variable_sub,l1)#将数据写入Tables7
treat="Cd";mediator="HDL"; status="Allcausemortstat";variable_sub="DM";l1 = Get_result_DM(data_design = data_design3)#分析HDL变量在糖尿病前期样本的中介效应
Tables7[nrow(Tables7) + 1,] <- c(variable_sub,l1)#将数据写入Tables7
#UHR
Tables7[nrow(Tables7) + 1,] <- c("UHR","NA","NA","NA","NA","NA","NA")
treat="Cd";mediator="UHR"; status="Allcausemortstat";variable_sub="preDM";l1 = Get_result_DM(data_design = data_design2)#分析UHR变量在糖尿病前期样本的中介效应
Tables7[nrow(Tables7) + 1,] <- c(variable_sub,l1)#将数据写入Tables7
treat="Cd";mediator="UHR"; status="Allcausemortstat";variable_sub="DM";l1 = Get_result_DM(data_design = data_design3)#分析UHR变量在糖尿病前期样本的中介效应
Tables7[nrow(Tables7) + 1,] <- c(variable_sub,l1)#将数据写入Tables7
#NLR
Tables7[nrow(Tables7) + 1,] <- c("NLR","NA","NA","NA","NA","NA","NA")
treat="Cd";mediator="NLR"; status="Allcausemortstat";variable_sub="preDM";l1 = Get_result_DM(data_design = data_design2)#分析NLR变量在糖尿病前期样本的中介效应
Tables7[nrow(Tables7) + 1,] <- c(variable_sub,l1)#将数据写入Tables7
treat="Cd";mediator="NLR"; status="Allcausemortstat";variable_sub="DM";l1 = Get_result_DM(data_design = data_design3)#分析NLR变量在糖尿病前期样本的中介效应
Tables7[nrow(Tables7) + 1,] <- c(variable_sub,l1)#将数据写入Tables7
#MLR
Tables7[nrow(Tables7) + 1,] <- c("MLR","NA","NA","NA","NA","NA","NA")
treat="Cd";mediator="MLR"; status="Allcausemortstat";variable_sub="preDM";l1 = Get_result_DM(data_design = data_design2)#分析MLR变量在糖尿病前期样本的中介效应
Tables7[nrow(Tables7) + 1,] <- c(variable_sub,l1)#将数据写入Tables7
treat="Cd";mediator="MLR"; status="Allcausemortstat";variable_sub="DM";l1 = Get_result_DM(data_design = data_design3)#分析MLR变量在糖尿病前期样本的中介效应
Tables7[nrow(Tables7) + 1,] <- c(variable_sub,l1)#将数据写入Tables7
#NMLR
Tables7[nrow(Tables7) + 1,] <- c("NMLR","NA","NA","NA","NA","NA","NA")
treat="Cd";mediator="NMLR"; status="Allcausemortstat";variable_sub="preDM";l1 = Get_result_DM(data_design = data_design2)#分析NMLR变量在糖尿病前期样本的中介效应
Tables7[nrow(Tables7) + 1,] <- c(variable_sub,l1)#将数据写入Tables7
treat="Cd";mediator="NMLR"; status="Allcausemortstat";variable_sub="DM";l1 = Get_result_DM(data_design = data_design3)#分析NMLR变量在糖尿病前期样本的中介效应
Tables7[nrow(Tables7) + 1,] <- c(variable_sub,l1)#将数据写入Tables7
#SIRI
Tables7[nrow(Tables7) + 1,] <- c("SIRI","NA","NA","NA","NA","NA","NA")
treat="Cd";mediator="SIRI"; status="Allcausemortstat";variable_sub="preDM";l1 = Get_result_DM(data_design = data_design2)#分析SIRI变量在糖尿病前期样本的中介效应
Tables7[nrow(Tables7) + 1,] <- c(variable_sub,l1)#将数据写入Tables7
treat="Cd";mediator="SIRI"; status="Allcausemortstat";variable_sub="DM";l1 = Get_result_DM(data_design = data_design3)#分析SIRI变量在糖尿病前期样本的中介效应
Tables7[nrow(Tables7) + 1,] <- c(variable_sub,l1)#将数据写入Tables7
#SII
Tables7[nrow(Tables7) + 1,] <- c("SII","NA","NA","NA","NA","NA","NA")
treat="Cd";mediator="SII"; status="Allcausemortstat";variable_sub="preDM";l1 = Get_result_DM(data_design = data_design2)#分析SII变量在糖尿病前期样本的中介效应
Tables7[nrow(Tables7) + 1,] <- c(variable_sub,l1)#将数据写入Tables7
treat="Cd";mediator="SII"; status="Allcausemortstat";variable_sub="DM";l1 = Get_result_DM(data_design = data_design3)#分析SII变量在糖尿病前期样本的中介效应
Tables7[nrow(Tables7) + 1,] <- c(variable_sub,l1)#将数据写入Tables7

# 保存为 CSV 格式文件
write.csv(Tables7, file = "Tables7.csv")



#生成表Tables8
Tables8 = data.frame(
  x1 = c("NA"),
  x2 = c("ACME"),
  x3 = c("ACME"),
  x4 = c("ADE"),
  x5 = c("ADE"),  
  x6 = c("PE"),
  x7 = c("PE")
)

#将数据写入TableS8
Tables8[nrow(Tables8) + 1,] <- c("NA","Estimate (95% CI)","P value","Estimate (95% CI)","P value","Estimate (95% CI)","P value")


#GGT
Tables8[nrow(Tables8) + 1,] <- c("GGT","NA","NA","NA","NA","NA","NA")
treat="Cd";mediator="GGT"; status="CVDmortstat";variable_sub="preDM";l1 = Get_result_DM(data_design = data_design2)#分析GGT变量在糖尿病前期样本的中介效应
Tables8[nrow(Tables8) + 1,] <- c(variable_sub,l1)#将数据写入Tables8
treat="Cd";mediator="GGT"; status="CVDmortstat";variable_sub="DM";l1 = Get_result_DM(data_design = data_design3)#分析GGT变量在糖尿病前期样本的中介效应
Tables8[nrow(Tables8) + 1,] <- c(variable_sub,l1)#将数据写入Tables8
#UA
Tables8[nrow(Tables8) + 1,] <- c("UA","NA","NA","NA","NA","NA","NA")
treat="Cd";mediator="UA"; status="CVDmortstat";variable_sub="preDM";l1 = Get_result_DM(data_design = data_design2)#分析UA变量在糖尿病前期样本的中介效应
Tables8[nrow(Tables8) + 1,] <- c(variable_sub,l1)#将数据写入Tables8
treat="Cd";mediator="UA"; status="CVDmortstat";variable_sub="DM";l1 = Get_result_DM(data_design = data_design3)#分析UA变量在糖尿病前期样本的中介效应
Tables8[nrow(Tables8) + 1,] <- c(variable_sub,l1)#将数据写入Tables8
#HDL
Tables8[nrow(Tables8) + 1,] <- c("HDL","NA","NA","NA","NA","NA","NA")
treat="Cd";mediator="HDL"; status="CVDmortstat";variable_sub="preDM";l1 = Get_result_DM(data_design = data_design2)#分析HDL变量在糖尿病前期样本的中介效应
Tables8[nrow(Tables8) + 1,] <- c(variable_sub,l1)#将数据写入Tables8
treat="Cd";mediator="HDL"; status="CVDmortstat";variable_sub="DM";l1 = Get_result_DM(data_design = data_design3)#分析HDL变量在糖尿病前期样本的中介效应
Tables8[nrow(Tables8) + 1,] <- c(variable_sub,l1)#将数据写入Tables8
#UHR
Tables8[nrow(Tables8) + 1,] <- c("UHR","NA","NA","NA","NA","NA","NA")
treat="Cd";mediator="UHR"; status="CVDmortstat";variable_sub="preDM";l1 = Get_result_DM(data_design = data_design2)#分析UHR变量在糖尿病前期样本的中介效应
Tables8[nrow(Tables8) + 1,] <- c(variable_sub,l1)#将数据写入Tables8
treat="Cd";mediator="UHR"; status="CVDmortstat";variable_sub="DM";l1 = Get_result_DM(data_design = data_design3)#分析UHR变量在糖尿病前期样本的中介效应
Tables8[nrow(Tables8) + 1,] <- c(variable_sub,l1)#将数据写入Tables8
#NLR
Tables8[nrow(Tables8) + 1,] <- c("NLR","NA","NA","NA","NA","NA","NA")
treat="Cd";mediator="NLR"; status="CVDmortstat";variable_sub="preDM";l1 = Get_result_DM(data_design = data_design2)#分析NLR变量在糖尿病前期样本的中介效应
Tables8[nrow(Tables8) + 1,] <- c(variable_sub,l1)#将数据写入Tables8
treat="Cd";mediator="NLR"; status="CVDmortstat";variable_sub="DM";l1 = Get_result_DM(data_design = data_design3)#分析NLR变量在糖尿病前期样本的中介效应
Tables8[nrow(Tables8) + 1,] <- c(variable_sub,l1)#将数据写入Tables8
#MLR
Tables8[nrow(Tables8) + 1,] <- c("MLR","NA","NA","NA","NA","NA","NA")
treat="Cd";mediator="MLR"; status="CVDmortstat";variable_sub="preDM";l1 = Get_result_DM(data_design = data_design2)#分析MLR变量在糖尿病前期样本的中介效应
Tables8[nrow(Tables8) + 1,] <- c(variable_sub,l1)#将数据写入Tables8
treat="Cd";mediator="MLR"; status="CVDmortstat";variable_sub="DM";l1 = Get_result_DM(data_design = data_design3)#分析MLR变量在糖尿病前期样本的中介效应
Tables8[nrow(Tables8) + 1,] <- c(variable_sub,l1)#将数据写入Tables8
#NMLR
Tables8[nrow(Tables8) + 1,] <- c("NMLR","NA","NA","NA","NA","NA","NA")
treat="Cd";mediator="NMLR"; status="CVDmortstat";variable_sub="preDM";l1 = Get_result_DM(data_design = data_design2)#分析NMLR变量在糖尿病前期样本的中介效应
Tables8[nrow(Tables8) + 1,] <- c(variable_sub,l1)#将数据写入Tables8
treat="Cd";mediator="NMLR"; status="CVDmortstat";variable_sub="DM";l1 = Get_result_DM(data_design = data_design3)#分析NMLR变量在糖尿病前期样本的中介效应
Tables8[nrow(Tables8) + 1,] <- c(variable_sub,l1)#将数据写入Tables8
#SIRI
Tables8[nrow(Tables8) + 1,] <- c("SIRI","NA","NA","NA","NA","NA","NA")
treat="Cd";mediator="SIRI"; status="CVDmortstat";variable_sub="preDM";l1 = Get_result_DM(data_design = data_design2)#分析SIRI变量在糖尿病前期样本的中介效应
Tables8[nrow(Tables8) + 1,] <- c(variable_sub,l1)#将数据写入Tables8
treat="Cd";mediator="SIRI"; status="CVDmortstat";variable_sub="DM";l1 = Get_result_DM(data_design = data_design3)#分析SIRI变量在糖尿病前期样本的中介效应
Tables8[nrow(Tables8) + 1,] <- c(variable_sub,l1)#将数据写入Tables8
#SII
Tables8[nrow(Tables8) + 1,] <- c("SII","NA","NA","NA","NA","NA","NA")
treat="Cd";mediator="SII"; status="CVDmortstat";variable_sub="preDM";l1 = Get_result_DM(data_design = data_design2)#分析SII变量在糖尿病前期样本的中介效应
Tables8[nrow(Tables8) + 1,] <- c(variable_sub,l1)#将数据写入Tables8
treat="Cd";mediator="SII"; status="CVDmortstat";variable_sub="DM";l1 = Get_result_DM(data_design = data_design3)#分析SII变量在糖尿病前期样本的中介效应
Tables8[nrow(Tables8) + 1,] <- c(variable_sub,l1)#将数据写入Tables8


# 保存为 CSV 格式文件
write.csv(Tables8, file = "Tables8.csv")



#生成表Tables9
Tables9 = data.frame(
  x1 = c("NA"),
  x2 = c("ACME"),
  x3 = c("ACME"),
  x4 = c("ADE"),
  x5 = c("ADE"),  
  x6 = c("PE"),
  x7 = c("PE")
)

#将数据写入Tables9
Tables9[nrow(Tables9) + 1,] <- c("NA","Estimate (95% CI)","P value","Estimate (95% CI)","P value","Estimate (95% CI)","P value")


#GGT
Tables9[nrow(Tables9) + 1,] <- c("GGT","NA","NA","NA","NA","NA","NA")
treat="Cd";mediator="GGT"; status="CANSERmortstat";variable_sub="preDM";l1 = Get_result_DM(data_design = data_design2)#分析GGT变量在糖尿病前期样本的中介效应
Tables9[nrow(Tables9) + 1,] <- c(variable_sub,l1)#将数据写入Tables9
treat="Cd";mediator="GGT"; status="CANSERmortstat";variable_sub="DM";l1 = Get_result_DM(data_design = data_design3)#分析GGT变量在糖尿病前期样本的中介效应
Tables9[nrow(Tables9) + 1,] <- c(variable_sub,l1)#将数据写入Tables9
#UA
Tables9[nrow(Tables9) + 1,] <- c("UA","NA","NA","NA","NA","NA","NA")
treat="Cd";mediator="UA"; status="CANSERmortstat";variable_sub="preDM";l1 = Get_result_DM(data_design = data_design2)#分析UA变量在糖尿病前期样本的中介效应
Tables9[nrow(Tables9) + 1,] <- c(variable_sub,l1)#将数据写入Tables9
treat="Cd";mediator="UA"; status="CANSERmortstat";variable_sub="DM";l1 = Get_result_DM(data_design = data_design3)#分析UA变量在糖尿病前期样本的中介效应
Tables9[nrow(Tables9) + 1,] <- c(variable_sub,l1)#将数据写入Tables9
#HDL
Tables9[nrow(Tables9) + 1,] <- c("HDL","NA","NA","NA","NA","NA","NA")
treat="Cd";mediator="HDL"; status="CANSERmortstat";variable_sub="preDM";l1 = Get_result_DM(data_design = data_design2)#分析HDL变量在糖尿病前期样本的中介效应
Tables9[nrow(Tables9) + 1,] <- c(variable_sub,l1)#将数据写入Tables9
treat="Cd";mediator="HDL"; status="CANSERmortstat";variable_sub="DM";l1 = Get_result_DM(data_design = data_design3)#分析HDL变量在糖尿病前期样本的中介效应
Tables9[nrow(Tables9) + 1,] <- c(variable_sub,l1)#将数据写入Tables9
#UHR
Tables9[nrow(Tables9) + 1,] <- c("UHR","NA","NA","NA","NA","NA","NA")
treat="Cd";mediator="UHR"; status="CANSERmortstat";variable_sub="preDM";l1 = Get_result_DM(data_design = data_design2)#分析UHR变量在糖尿病前期样本的中介效应
Tables9[nrow(Tables9) + 1,] <- c(variable_sub,l1)#将数据写入Tables9
treat="Cd";mediator="UHR"; status="CANSERmortstat";variable_sub="DM";l1 = Get_result_DM(data_design = data_design3)#分析UHR变量在糖尿病前期样本的中介效应
Tables9[nrow(Tables9) + 1,] <- c(variable_sub,l1)#将数据写入Tables9
#NLR
Tables9[nrow(Tables9) + 1,] <- c("NLR","NA","NA","NA","NA","NA","NA")
treat="Cd";mediator="NLR"; status="CANSERmortstat";variable_sub="preDM";l1 = Get_result_DM(data_design = data_design2)#分析NLR变量在糖尿病前期样本的中介效应
Tables9[nrow(Tables9) + 1,] <- c(variable_sub,l1)#将数据写入Tables9
treat="Cd";mediator="NLR"; status="CANSERmortstat";variable_sub="DM";l1 = Get_result_DM(data_design = data_design3)#分析NLR变量在糖尿病前期样本的中介效应
Tables9[nrow(Tables9) + 1,] <- c(variable_sub,l1)#将数据写入Tables9
#MLR
Tables9[nrow(Tables9) + 1,] <- c("MLR","NA","NA","NA","NA","NA","NA")
treat="Cd";mediator="MLR"; status="CANSERmortstat";variable_sub="preDM";l1 = Get_result_DM(data_design = data_design2)#分析MLR变量在糖尿病前期样本的中介效应
Tables9[nrow(Tables9) + 1,] <- c(variable_sub,l1)#将数据写入Tables9
treat="Cd";mediator="MLR"; status="CANSERmortstat";variable_sub="DM";l1 = Get_result_DM(data_design = data_design3)#分析MLR变量在糖尿病前期样本的中介效应
Tables9[nrow(Tables9) + 1,] <- c(variable_sub,l1)#将数据写入Tables9
#NMLR
Tables9[nrow(Tables9) + 1,] <- c("NMLR","NA","NA","NA","NA","NA","NA")
treat="Cd";mediator="NMLR"; status="CANSERmortstat";variable_sub="preDM";l1 = Get_result_DM(data_design = data_design2)#分析NMLR变量在糖尿病前期样本的中介效应
Tables9[nrow(Tables9) + 1,] <- c(variable_sub,l1)#将数据写入Tables9
treat="Cd";mediator="NMLR"; status="CANSERmortstat";variable_sub="DM";l1 = Get_result_DM(data_design = data_design3)#分析NMLR变量在糖尿病前期样本的中介效应
Tables9[nrow(Tables9) + 1,] <- c(variable_sub,l1)#将数据写入Tables9
#SIRI
Tables9[nrow(Tables9) + 1,] <- c("SIRI","NA","NA","NA","NA","NA","NA")
treat="Cd";mediator="SIRI"; status="CANSERmortstat";variable_sub="preDM";l1 = Get_result_DM(data_design = data_design2)#分析SIRI变量在糖尿病前期样本的中介效应
Tables9[nrow(Tables9) + 1,] <- c(variable_sub,l1)#将数据写入Tables9
treat="Cd";mediator="SIRI"; status="CANSERmortstat";variable_sub="DM";l1 = Get_result_DM(data_design = data_design3)#分析SIRI变量在糖尿病前期样本的中介效应
Tables9[nrow(Tables9) + 1,] <- c(variable_sub,l1)#将数据写入Tables9
#SII
Tables9[nrow(Tables9) + 1,] <- c("SII","NA","NA","NA","NA","NA","NA")
treat="Cd";mediator="SII"; status="CANSERmortstat";variable_sub="preDM";l1 = Get_result_DM(data_design = data_design2)#分析SII变量在糖尿病前期样本的中介效应
Tables9[nrow(Tables9) + 1,] <- c(variable_sub,l1)#将数据写入Tables9
treat="Cd";mediator="SII"; status="CANSERmortstat";variable_sub="DM";l1 = Get_result_DM(data_design = data_design3)#分析SII变量在糖尿病前期样本的中介效应
Tables9[nrow(Tables9) + 1,] <- c(variable_sub,l1)#将数据写入Tables9



# 保存为 CSV 格式文件
write.csv(Tables9, file = "Tables9.csv")








#计算表Tables10中根据性别分层之后的中介分析数据
#加载数据
load("data.RData")
data  = droplevels(data)

#分析完之后根据结果在PPT中绘图,因为是boot算法，有随机性，所以每次运行结果都会不太一样，但是不会差别太大
#图3根据图s5的数据在ppt或者visio中画图即可，这里就不画了

data_design <- svydesign(ids=~SDMVPSU,   #集群，抽样单元PSU
                         strata=~SDMVSTRA,   #分层
                         weights=~WTMEC,
                         nest=TRUE,
                         survey.lonely.psu="adjust",  #抽样单元为1时不报错
                         data=data)
data_design2 = subset(data_design, Sex == 1)#男样本
data_design3 = subset(data_design, Sex == 2)#女样本

#生成表Tables10
Tables10 = data.frame(
  x1 = c("NA"),
  x2 = c("ACME"),
  x3 = c("ACME"),
  x4 = c("ADE"),
  x5 = c("ADE"),  
  x6 = c("PE"),
  x7 = c("PE")
)

#将数据写入Tables10
Tables10[nrow(Tables10) + 1,] <- c("NA","Estimate (95% CI)","P value","Estimate (95% CI)","P value","Estimate (95% CI)","P value")


#重写计算中介效应的函数
Median_alalysiae_SEX = function(data,indices){
  
  d <- data[indices, ]  # 重抽样数据
  d_design= svydesign(ids=~SDMVPSU,   #集群，抽样单元PSU
                      strata=~SDMVSTRA,   #分层
                      weights=~WTMEC,
                      nest=TRUE,
                      survey.lonely.psu="adjust",  #抽样单元为1时不报错
                      data=d)
  if (variable_sub == "man"){
    d_design = subset(d_design, Sex == 1)#男的样本
    d = subset(d, Sex == 1)
  }else if(variable_sub == "woman"){
    d_design = subset(d_design, Sex == 2)#女的样本
    d = subset(d, Sex == 2)
  }
  
  
  
  cor = "+ Age + Race + Educationlevel +
                 PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus +
                 Alcoholstatus+ Failingkidneys + Hypertension"
  f1 = as.formula(paste0("Surv(permth_int, ", status, ")", "~", treat, cor))
  f2 = as.formula(paste0(mediator, "~", treat, cor))
  f3 = as.formula(paste0("Surv(permth_int, ", status, ")", "~", treat, "+", mediator, cor))
  
  fit_svy<- svycoxph(f1, design=d_design)
  #weight变量是权重,1总效应模型（X→结局）,Total Effect
  model_total<- coxph(f1, x=TRUE, y=TRUE,data=d, weights = (1/fit_svy$survey.design$prob)/mean(1/fit_svy$survey.design$prob))
  #提取HR和置信区间
  #total_result = ShowRegTable(model_total, exp = FALSE,digits = 3,pDigits = 3,printToggle=FALSE,quote = FALSE, ciFun = confint)
  beta_total <- coef(model_total)[treat]  # 总效应系数
  
  # 2：X→M的关系
  # 用线性回归
  model_XM <- lm(f2, data = d)
  #提取HR和置信区间
  #XM_result = ShowRegTable(model_XM, exp = FALSE,digits = 3,pDigits = 3,printToggle=FALSE,quote = FALSE, ciFun = confint)
  a <- coef(model_XM)[treat]  # X→M的效应
  
  # 3：（X和M共同预测结局）ADE（Average Direct Effects）
  model_direct <- coxph(f3, data = d,weights = (1/fit_svy$survey.design$prob)/mean(1/fit_svy$survey.design$prob))
  #提取HR和置信区间
  #direct_result = ShowRegTable(model_direct, exp = FALSE,digits = 3,pDigits = 3,printToggle=FALSE,quote = FALSE, ciFun = confint)
  beta_direct <- coef(model_direct)[treat]  # 直接效应系数
  b <- coef(model_direct)[mediator]  # M→结局的效应
  
  # 4. 计算效应量 ACME
  indirect_effect <- a * b  # 中介效应 (乘积法)
  proportion_mediated <- indirect_effect / beta_total  # 中介效应占比,PE
  return(c(beta_total, beta_direct, indirect_effect, proportion_mediated))
}




#重写得到最终的ACME，ADE，PE等和置信区间的函数
Get_result_SEX = function(R = 10, data_design=data_design){
  boot_results <- boot(data = data, statistic = Median_alalysiae_SEX, R = R)#进行Bootstrap，1000次计算速度太慢了,次数越多，计算的P值越准确
  # 计算置信区间 (95%)
  ci_total <- boot.ci(boot_results, type = "norm", index = 1)  # 总效应CI,TE
  ci_direct <- boot.ci(boot_results, type = "norm", index = 2)  # 直接效应CI,ADE
  ci_indirect <- boot.ci(boot_results, type = "norm", index = 3)  # 中介效应CI,ACME
  ci_pm <- boot.ci(boot_results, type = "norm", index = 4)  # 中介效应占比CI,PE
  print(paste0("分析", treat, "对", status, "的中介效应，中介变量为", mediator))
  
  # 计算观测统计量
  obs_stat  = Median_alalysiae_SEX(data,seq(from=1, to=nrow(data)))
  
  # 计算Bootstrap P值（双侧检验）
  #计算ADE值
  cor = "+ Age +  Race + Educationlevel +
                 PIR + BMI + WWI + Maritalstatus + Physicalactivity + Smokingstatus +
                 Alcoholstatus+ Failingkidneys + Hypertension"
  f3 = as.formula(paste0("Surv(permth_int, ", status, ")", "~", treat, "+", mediator, cor))
  fit_svy<- svycoxph(f3, design=data_design)
  
  if (variable_sub == "man"){
    d_ = subset(data, Sex == 1)
  }else if(variable_sub == "woman"){
    d_ = subset(data, Sex == 2)
  }
  
  model_direct <- coxph(f3, data = d_,weights = (1/fit_svy$survey.design$prob)/mean(1/fit_svy$survey.design$prob))
  #提取HR和置信区间
  p_direct = data.frame(ShowRegTable(model_direct, exp = FALSE,digits = 3,pDigits = 3,printToggle=FALSE,quote = FALSE, ciFun = confint))["Cd", "p"]
  
  #p_total = 1-boot.pval(boot_results,theta_null = obs_stat[1], alternative = "two.sided", index=1,pval_precision=0.00001)
  #p_direct = 1-boot.pval(boot_results,theta_null = obs_stat[2], alternative = "two.sided", index=2,pval_precision=0.00001)
  p_indirect = 1-boot.pval(boot_results,theta_null = obs_stat[3], alternative = "two.sided", index=3,pval_precision=0.001)
  p_pm = 1-boot.pval(boot_results,theta_null = obs_stat[4], alternative = "two.sided", index=4,pval_precision=0.001)
  
  y = c(paste0(round(boot_results$t0[3],digit=3), "(",round(ci_indirect$normal[2],digit=3), ",", round(ci_indirect$normal[3],digit=3), ")"),p_indirect,
        paste0(round(boot_results$t0[2],digit=3), "(",round(ci_direct$normal[2],digit=3), ",", round(ci_direct$normal[3],digit=3), ")"), p_direct,
        paste0(round(boot_results$t0[4],digit=3), "(",round(ci_pm$normal[2],digit=3), ",", round(ci_pm$normal[3],digit=3), ")"), p_pm)
  
  return(y)
}






#GGT
Tables10[nrow(Tables10) + 1,] <- c("GGT","NA","NA","NA","NA","NA","NA")
treat="Cd";mediator="GGT"; status="Allcausemortstat";variable_sub="man";l1 = Get_result_SEX(data_design = data_design2)#分析GGT变量在男样本的中介效应
Tables10[nrow(Tables10) + 1,] <- c(variable_sub,l1)#将数据写入Tables10
treat="Cd";mediator="GGT"; status="Allcausemortstat";variable_sub="woman";l1 = Get_result_SEX(data_design = data_design3)#分析GGT变量在女样本的中介效应
Tables10[nrow(Tables10) + 1,] <- c(variable_sub,l1)#将数据写入Tables10
#UA
Tables10[nrow(Tables10) + 1,] <- c("UA","NA","NA","NA","NA","NA","NA")
treat="Cd";mediator="UA"; status="Allcausemortstat";variable_sub="man";l1 = Get_result_SEX(data_design = data_design2)#分析UA变量在男样本的中介效应
Tables10[nrow(Tables10) + 1,] <- c(variable_sub,l1)#将数据写入Tables10
treat="Cd";mediator="UA"; status="Allcausemortstat";variable_sub="woman";l1 = Get_result_SEX(data_design = data_design3)#分析UA变量在女样本的中介效应
Tables10[nrow(Tables10) + 1,] <- c(variable_sub,l1)#将数据写入Tables10
#HDL
Tables10[nrow(Tables10) + 1,] <- c("HDL","NA","NA","NA","NA","NA","NA")
treat="Cd";mediator="HDL"; status="Allcausemortstat";variable_sub="man";l1 = Get_result_SEX(data_design = data_design2)#分析HDL变量在男样本的中介效应
Tables10[nrow(Tables10) + 1,] <- c(variable_sub,l1)#将数据写入Tables10
treat="Cd";mediator="HDL"; status="Allcausemortstat";variable_sub="woman";l1 = Get_result_SEX(data_design = data_design3)#分析HDL变量女样本的中介效应
Tables10[nrow(Tables10) + 1,] <- c(variable_sub,l1)#将数据写入Tables10
#UHR
Tables10[nrow(Tables10) + 1,] <- c("UHR","NA","NA","NA","NA","NA","NA")
treat="Cd";mediator="UHR"; status="Allcausemortstat";variable_sub="man";l1 = Get_result_SEX(data_design = data_design2)#分析UHR变量在男样本的中介效应
Tables10[nrow(Tables10) + 1,] <- c(variable_sub,l1)#将数据写入Tables10
treat="Cd";mediator="UHR"; status="Allcausemortstat";variable_sub="woman";l1 = Get_result_SEX(data_design = data_design3)#分析UHR变量女样本的中介效应
Tables10[nrow(Tables10) + 1,] <- c(variable_sub,l1)#将数据写入Tables10
#NLR
Tables10[nrow(Tables10) + 1,] <- c("NLR","NA","NA","NA","NA","NA","NA")
treat="Cd";mediator="NLR"; status="Allcausemortstat";variable_sub="man";l1 = Get_result_SEX(data_design = data_design2)#分析NLR变量在男样本的中介效应
Tables10[nrow(Tables10) + 1,] <- c(variable_sub,l1)#将数据写入Tables10
treat="Cd";mediator="NLR"; status="Allcausemortstat";variable_sub="woman";l1 = Get_result_SEX(data_design = data_design3)#分析NLR变量在女样本的中介效应
Tables10[nrow(Tables10) + 1,] <- c(variable_sub,l1)#将数据写入Tables10
#MLR
Tables10[nrow(Tables10) + 1,] <- c("MLR","NA","NA","NA","NA","NA","NA")
treat="Cd";mediator="MLR"; status="Allcausemortstat";variable_sub="man";l1 = Get_result_SEX(data_design = data_design2)#分析MLR变量在男样本的中介效应
Tables10[nrow(Tables10) + 1,] <- c(variable_sub,l1)#将数据写入Tables10
treat="Cd";mediator="MLR"; status="Allcausemortstat";variable_sub="woman";l1 = Get_result_SEX(data_design = data_design3)#分析MLR变量在女样本的中介效应
Tables10[nrow(Tables10) + 1,] <- c(variable_sub,l1)#将数据写入Tables10
#NMLR
Tables10[nrow(Tables10) + 1,] <- c("NMLR","NA","NA","NA","NA","NA","NA")
treat="Cd";mediator="NMLR"; status="Allcausemortstat";variable_sub="man";l1 = Get_result_SEX(data_design = data_design2)#分析NMLR变量在男样本的中介效应
Tables10[nrow(Tables10) + 1,] <- c(variable_sub,l1)#将数据写入Tables10
treat="Cd";mediator="NMLR"; status="Allcausemortstat";variable_sub="woman";l1 = Get_result_SEX(data_design = data_design3)#分析NMLR变量在女样本的中介效应
Tables10[nrow(Tables10) + 1,] <- c(variable_sub,l1)#将数据写入Tables10
#SIRI
Tables10[nrow(Tables10) + 1,] <- c("SIRI","NA","NA","NA","NA","NA","NA")
treat="Cd";mediator="SIRI"; status="Allcausemortstat";variable_sub="man";l1 = Get_result_SEX(data_design = data_design2)#分析SIRI变量在男样本的中介效应
Tables10[nrow(Tables10) + 1,] <- c(variable_sub,l1)#将数据写入Tables10
treat="Cd";mediator="SIRI"; status="Allcausemortstat";variable_sub="woman";l1 = Get_result_SEX(data_design = data_design3)#分析SIRI变量在女样本的中介效应
Tables10[nrow(Tables10) + 1,] <- c(variable_sub,l1)#将数据写入Tables10
#SII
Tables10[nrow(Tables10) + 1,] <- c("SII","NA","NA","NA","NA","NA","NA")
treat="Cd";mediator="SII"; status="Allcausemortstat";variable_sub="man";l1 = Get_result_SEX(data_design = data_design2)#分析SII变量在男样本的中介效应
Tables10[nrow(Tables10) + 1,] <- c(variable_sub,l1)#将数据写入Tables10
treat="Cd";mediator="SII"; status="Allcausemortstat";variable_sub="woman";l1 = Get_result_SEX(data_design = data_design3)#分析SII变量在女样本的中介效应
Tables10[nrow(Tables10) + 1,] <- c(variable_sub,l1)#将数据写入Tables10

# 保存为 CSV 格式文件
write.csv(Tables10, file = "Tables10.csv")









#生成表Tables11
Tables11 = data.frame(
  x1 = c("NA"),
  x2 = c("ACME"),
  x3 = c("ACME"),
  x4 = c("ADE"),
  x5 = c("ADE"),  
  x6 = c("PE"),
  x7 = c("PE")
)

#将数据写入Tables11
Tables11[nrow(Tables11) + 1,] <- c("NA","Estimate (95% CI)","P value","Estimate (95% CI)","P value","Estimate (95% CI)","P value")

#GGT
Tables11[nrow(Tables11) + 1,] <- c("GGT","NA","NA","NA","NA","NA","NA")
treat="Cd";mediator="GGT"; status="CVDmortstat";variable_sub="man";l1 = Get_result_SEX(data_design = data_design2)#分析GGT变量在男样本的中介效应
Tables11[nrow(Tables11) + 1,] <- c(variable_sub,l1)#将数据写入Tables11
treat="Cd";mediator="GGT"; status="CVDmortstat";variable_sub="woman";l1 = Get_result_SEX(data_design = data_design3)#分析GGT变量在女样本的中介效应
Tables11[nrow(Tables11) + 1,] <- c(variable_sub,l1)#将数据写入Tables11
#UA
Tables11[nrow(Tables11) + 1,] <- c("UA","NA","NA","NA","NA","NA","NA")
treat="Cd";mediator="UA"; status="CVDmortstat";variable_sub="man";l1 = Get_result_SEX(data_design = data_design2)#分析UA变量在男样本的中介效应
Tables11[nrow(Tables11) + 1,] <- c(variable_sub,l1)#将数据写入Tables11
treat="Cd";mediator="UA"; status="CVDmortstat";variable_sub="woman";l1 = Get_result_SEX(data_design = data_design3)#分析UA变量在女样本的中介效应
Tables11[nrow(Tables11) + 1,] <- c(variable_sub,l1)#将数据写入Tables11
#HDL
Tables11[nrow(Tables11) + 1,] <- c("HDL","NA","NA","NA","NA","NA","NA")
treat="Cd";mediator="HDL"; status="CVDmortstat";variable_sub="man";l1 = Get_result_SEX(data_design = data_design2)#分析HDL变量在男样本的中介效应
Tables11[nrow(Tables11) + 1,] <- c(variable_sub,l1)#将数据写入Tables11
treat="Cd";mediator="HDL"; status="CVDmortstat";variable_sub="woman";l1 = Get_result_SEX(data_design = data_design3)#分析HDL变量女样本的中介效应
Tables11[nrow(Tables11) + 1,] <- c(variable_sub,l1)#将数据写入Tables11
#UHR
Tables11[nrow(Tables11) + 1,] <- c("UHR","NA","NA","NA","NA","NA","NA")
treat="Cd";mediator="UHR"; status="CVDmortstat";variable_sub="man";l1 = Get_result_SEX(data_design = data_design2)#分析UHR变量在男样本的中介效应
Tables11[nrow(Tables11) + 1,] <- c(variable_sub,l1)#将数据写入Tables11
treat="Cd";mediator="UHR"; status="CVDmortstat";variable_sub="woman";l1 = Get_result_SEX(data_design = data_design3)#分析UHR变量女样本的中介效应
Tables11[nrow(Tables11) + 1,] <- c(variable_sub,l1)#将数据写入Tables11
#NLR
Tables11[nrow(Tables11) + 1,] <- c("NLR","NA","NA","NA","NA","NA","NA")
treat="Cd";mediator="NLR"; status="CVDmortstat";variable_sub="man";l1 = Get_result_SEX(data_design = data_design2)#分析NLR变量在男样本的中介效应
Tables11[nrow(Tables11) + 1,] <- c(variable_sub,l1)#将数据写入Tables11
treat="Cd";mediator="NLR"; status="CVDmortstat";variable_sub="woman";l1 = Get_result_SEX(data_design = data_design3)#分析NLR变量在女样本的中介效应
Tables11[nrow(Tables11) + 1,] <- c(variable_sub,l1)#将数据写入Tables11
#MLR
Tables11[nrow(Tables11) + 1,] <- c("MLR","NA","NA","NA","NA","NA","NA")
treat="Cd";mediator="MLR"; status="CVDmortstat";variable_sub="man";l1 = Get_result_SEX(data_design = data_design2)#分析MLR变量在男样本的中介效应
Tables11[nrow(Tables11) + 1,] <- c(variable_sub,l1)#将数据写入Tables11
treat="Cd";mediator="MLR"; status="CVDmortstat";variable_sub="woman";l1 = Get_result_SEX(data_design = data_design3)#分析MLR变量在女样本的中介效应
Tables11[nrow(Tables11) + 1,] <- c(variable_sub,l1)#将数据写入Tables11
#NMLR
Tables11[nrow(Tables11) + 1,] <- c("NMLR","NA","NA","NA","NA","NA","NA")
treat="Cd";mediator="NMLR"; status="CVDmortstat";variable_sub="man";l1 = Get_result_SEX(data_design = data_design2)#分析NMLR变量在男样本的中介效应
Tables11[nrow(Tables11) + 1,] <- c(variable_sub,l1)#将数据写入Tables11
treat="Cd";mediator="NMLR"; status="CVDmortstat";variable_sub="woman";l1 = Get_result_SEX(data_design = data_design3)#分析NMLR变量在女样本的中介效应
Tables11[nrow(Tables11) + 1,] <- c(variable_sub,l1)#将数据写入Tables11
#SIRI
Tables11[nrow(Tables11) + 1,] <- c("SIRI","NA","NA","NA","NA","NA","NA")
treat="Cd";mediator="SIRI"; status="CVDmortstat";variable_sub="man";l1 = Get_result_SEX(data_design = data_design2)#分析SIRI变量在男样本的中介效应
Tables11[nrow(Tables11) + 1,] <- c(variable_sub,l1)#将数据写入Tables11
treat="Cd";mediator="SIRI"; status="CVDmortstat";variable_sub="woman";l1 = Get_result_SEX(data_design = data_design3)#分析SIRI变量在女样本的中介效应
Tables11[nrow(Tables11) + 1,] <- c(variable_sub,l1)#将数据写入Tables11
#SII
Tables11[nrow(Tables11) + 1,] <- c("SII","NA","NA","NA","NA","NA","NA")
treat="Cd";mediator="SII"; status="CVDmortstat";variable_sub="man";l1 = Get_result_SEX(data_design = data_design2)#分析SII变量在男样本的中介效应
Tables11[nrow(Tables11) + 1,] <- c(variable_sub,l1)#将数据写入Tables11
treat="Cd";mediator="SII"; status="CVDmortstat";variable_sub="woman";l1 = Get_result_SEX(data_design = data_design3)#分析SII变量在女样本的中介效应
Tables11[nrow(Tables11) + 1,] <- c(variable_sub,l1)#将数据写入Tables11



# 保存为 CSV 格式文件
write.csv(Tables11, file = "Tables11.csv")








#生成表Tables12
Tables12 = data.frame(
  x1 = c("NA"),
  x2 = c("ACME"),
  x3 = c("ACME"),
  x4 = c("ADE"),
  x5 = c("ADE"),  
  x6 = c("PE"),
  x7 = c("PE")
)

#将数据写入Tables12
Tables12[nrow(Tables12) + 1,] <- c("NA","Estimate (95% CI)","P value","Estimate (95% CI)","P value","Estimate (95% CI)","P value")

#GGT
Tables12[nrow(Tables12) + 1,] <- c("GGT","NA","NA","NA","NA","NA","NA")
treat="Cd";mediator="GGT"; status="CANSERmortstat";variable_sub="man";l1 = Get_result_SEX(data_design = data_design2)#分析GGT变量在男样本的中介效应
Tables12[nrow(Tables12) + 1,] <- c(variable_sub,l1)#将数据写入Tables12
treat="Cd";mediator="GGT"; status="CANSERmortstat";variable_sub="woman";l1 = Get_result_SEX(data_design = data_design3)#分析GGT变量在女样本的中介效应
Tables12[nrow(Tables12) + 1,] <- c(variable_sub,l1)#将数据写入Tables12
#UA
Tables12[nrow(Tables12) + 1,] <- c("UA","NA","NA","NA","NA","NA","NA")
treat="Cd";mediator="UA"; status="CANSERmortstat";variable_sub="man";l1 = Get_result_SEX(data_design = data_design2)#分析UA变量在男样本的中介效应
Tables12[nrow(Tables12) + 1,] <- c(variable_sub,l1)#将数据写入Tables12
treat="Cd";mediator="UA"; status="CANSERmortstat";variable_sub="woman";l1 = Get_result_SEX(data_design = data_design3)#分析UA变量在女样本的中介效应
Tables12[nrow(Tables12) + 1,] <- c(variable_sub,l1)#将数据写入Tables12
#HDL
Tables12[nrow(Tables12) + 1,] <- c("HDL","NA","NA","NA","NA","NA","NA")
treat="Cd";mediator="HDL"; status="CANSERmortstat";variable_sub="man";l1 = Get_result_SEX(data_design = data_design2)#分析HDL变量在男样本的中介效应
Tables12[nrow(Tables12) + 1,] <- c(variable_sub,l1)#将数据写入Tables12
treat="Cd";mediator="HDL"; status="CANSERmortstat";variable_sub="woman";l1 = Get_result_SEX(data_design = data_design3)#分析HDL变量女样本的中介效应
Tables12[nrow(Tables12) + 1,] <- c(variable_sub,l1)#将数据写入Tables12
#UHR
Tables12[nrow(Tables12) + 1,] <- c("UHR","NA","NA","NA","NA","NA","NA")
treat="Cd";mediator="UHR"; status="CANSERmortstat";variable_sub="man";l1 = Get_result_SEX(data_design = data_design2)#分析UHR变量在男样本的中介效应
Tables12[nrow(Tables12) + 1,] <- c(variable_sub,l1)#将数据写入Tables12
treat="Cd";mediator="UHR"; status="CANSERmortstat";variable_sub="woman";l1 = Get_result_SEX(data_design = data_design3)#分析UHR变量女样本的中介效应
Tables12[nrow(Tables12) + 1,] <- c(variable_sub,l1)#将数据写入Tables12
#NLR
Tables12[nrow(Tables12) + 1,] <- c("NLR","NA","NA","NA","NA","NA","NA")
treat="Cd";mediator="NLR"; status="CANSERmortstat";variable_sub="man";l1 = Get_result_SEX(data_design = data_design2)#分析NLR变量在男样本的中介效应
Tables12[nrow(Tables12) + 1,] <- c(variable_sub,l1)#将数据写入Tables12
treat="Cd";mediator="NLR"; status="CANSERmortstat";variable_sub="woman";l1 = Get_result_SEX(data_design = data_design3)#分析NLR变量在女样本的中介效应
Tables12[nrow(Tables12) + 1,] <- c(variable_sub,l1)#将数据写入Tables12
#MLR
Tables12[nrow(Tables12) + 1,] <- c("MLR","NA","NA","NA","NA","NA","NA")
treat="Cd";mediator="MLR"; status="CANSERmortstat";variable_sub="man";l1 = Get_result_SEX(data_design = data_design2)#分析MLR变量在男样本的中介效应
Tables12[nrow(Tables12) + 1,] <- c(variable_sub,l1)#将数据写入Tables12
treat="Cd";mediator="MLR"; status="CANSERmortstat";variable_sub="woman";l1 = Get_result_SEX(data_design = data_design3)#分析MLR变量在女样本的中介效应
Tables12[nrow(Tables12) + 1,] <- c(variable_sub,l1)#将数据写入Tables12
#NMLR
Tables12[nrow(Tables12) + 1,] <- c("NMLR","NA","NA","NA","NA","NA","NA")
treat="Cd";mediator="NMLR"; status="CANSERmortstat";variable_sub="man";l1 = Get_result_SEX(data_design = data_design2)#分析NMLR变量在男样本的中介效应
Tables12[nrow(Tables12) + 1,] <- c(variable_sub,l1)#将数据写入Tables12
treat="Cd";mediator="NMLR"; status="CANSERmortstat";variable_sub="woman";l1 = Get_result_SEX(data_design = data_design3)#分析NMLR变量在女样本的中介效应
Tables12[nrow(Tables12) + 1,] <- c(variable_sub,l1)#将数据写入Tables12
#SIRI
Tables12[nrow(Tables12) + 1,] <- c("SIRI","NA","NA","NA","NA","NA","NA")
treat="Cd";mediator="SIRI"; status="CANSERmortstat";variable_sub="man";l1 = Get_result_SEX(data_design = data_design2)#分析SIRI变量在男样本的中介效应
Tables12[nrow(Tables12) + 1,] <- c(variable_sub,l1)#将数据写入Tables12
treat="Cd";mediator="SIRI"; status="CANSERmortstat";variable_sub="woman";l1 = Get_result_SEX(data_design = data_design3)#分析SIRI变量在女样本的中介效应
Tables12[nrow(Tables12) + 1,] <- c(variable_sub,l1)#将数据写入Tables12
#SII
Tables12[nrow(Tables12) + 1,] <- c("SII","NA","NA","NA","NA","NA","NA")
treat="Cd";mediator="SII"; status="CANSERmortstat";variable_sub="man";l1 = Get_result_SEX(data_design = data_design2)#分析SII变量在男样本的中介效应
Tables12[nrow(Tables12) + 1,] <- c(variable_sub,l1)#将数据写入Tables12
treat="Cd";mediator="SII"; status="CANSERmortstat";variable_sub="woman";l1 = Get_result_SEX(data_design = data_design3)#分析SII变量在女样本的中介效应
Tables12[nrow(Tables12) + 1,] <- c(variable_sub,l1)#将数据写入Tables12


# 保存为 CSV 格式文件
write.csv(Tables12, file = "Tables12.csv")





