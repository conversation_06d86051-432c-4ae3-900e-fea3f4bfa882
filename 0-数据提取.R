library(dplyr)
library(plyr)
library(foreign)
library(mice)
library(tableone)#画基线表
library(mlbench)
library(caret)
library(Boruta)
library(questionr)#计算OR和置信区间
library(plotRCS)#限制性立方样条绘图
library(pROC)
library(dcurves)
library(rms)
library(riskRegression)
library(rmda)
library(jstable)
library(forestplot)#画森林图
library(forestploter)
library(nricens)



#设置不用科学计数法显示数字，设置为0则为默认
options(scipen= 999)
options(digits = 3)


#读取人口学数据
library(dplyr)
library(haven)
demo1999 = read_xpt("变量/Demographic/DEMO.xpt")
demo1999 = demo1999 %>% 
  select("SEQN","RIDAGEYR","RIAGENDR","RIDRETH1","DMDEDUC2","INDFMPIR","DMDMARTL","RIDEXPRG",
         "WTINT2YR","WTINT4YR","WTMEC2YR","WTMEC4YR","SDMVPSU","SDMVSTRA","SDDSRVYR")

demo2001 = read_xpt("变量/Demographic/DEMO_B.xpt")
demo2001 = demo2001 %>% 
  select("SEQN","RIDAGEYR","RIAGENDR","RIDRETH1","DMDEDUC2","INDFMPIR","DMDMARTL","RIDEXPRG",
         "WTINT2YR","WTINT4YR","WTMEC2YR","WTMEC4YR","SDMVPSU","SDMVSTRA","SDDSRVYR")

demo2003 = read_xpt("变量/Demographic/DEMO_C.xpt")
demo2003 = demo2003 %>% 
  select("SEQN","RIDAGEYR","RIAGENDR","RIDRETH1","DMDEDUC2","INDFMPIR","DMDMARTL","RIDEXPRG",
         "WTINT2YR","WTMEC2YR","SDMVPSU","SDMVSTRA","SDDSRVYR")

demo2005 = read_xpt("变量/Demographic/DEMO_D.xpt")
demo2005 = demo2005 %>% 
  select("SEQN","RIDAGEYR","RIAGENDR","RIDRETH1","DMDEDUC2","INDFMPIR","DMDMARTL","RIDEXPRG",
         "WTINT2YR","WTMEC2YR","SDMVPSU","SDMVSTRA","SDDSRVYR")

demo2007 = read_xpt("变量/Demographic/DEMO_E.xpt")
demo2007 = demo2007 %>% 
  select("SEQN","RIDAGEYR","RIAGENDR","RIDRETH1","DMDEDUC2","INDFMPIR","DMDMARTL","RIDEXPRG",
         "WTINT2YR","WTMEC2YR","SDMVPSU","SDMVSTRA","SDDSRVYR")

demo2009 = read_xpt("变量/Demographic/DEMO_F.xpt")
demo2009 = demo2009 %>% 
  select("SEQN","RIDAGEYR","RIAGENDR","RIDRETH1","DMDEDUC2","INDFMPIR","DMDMARTL","RIDEXPRG",
         "WTINT2YR","WTMEC2YR","SDMVPSU","SDMVSTRA","SDDSRVYR")

demo2011 = read_xpt("变量/Demographic/DEMO_G.xpt")
demo2011 = demo2011 %>% 
  select("SEQN","RIDAGEYR","RIAGENDR","RIDRETH1","DMDEDUC2","INDFMPIR","DMDMARTL","RIDEXPRG",
         "WTINT2YR","WTMEC2YR","SDMVPSU","SDMVSTRA","SDDSRVYR")

demo2013 = read_xpt("变量/Demographic/DEMO_H.xpt")
demo2013 = demo2013 %>% 
  select("SEQN","RIDAGEYR","RIAGENDR","RIDRETH1","DMDEDUC2","INDFMPIR","DMDMARTL","RIDEXPRG",
         "WTINT2YR","WTMEC2YR","SDMVPSU","SDMVSTRA","SDDSRVYR")

demo2015 = read_xpt("变量/Demographic/DEMO_I.xpt")
demo2015 = demo2015 %>% 
  select("SEQN","RIDAGEYR","RIAGENDR","RIDRETH1","DMDEDUC2","INDFMPIR","DMDMARTL","RIDEXPRG",
         "WTINT2YR","WTMEC2YR","SDMVPSU","SDMVSTRA","SDDSRVYR")

demo2017 = read_xpt("变量/Demographic/DEMO_J.xpt")
demo2017 = demo2017 %>% 
  select("SEQN","RIDAGEYR","RIAGENDR","RIDRETH1","DMDEDUC2","INDFMPIR","DMDMARTL","RIDEXPRG",
         "WTINT2YR","WTMEC2YR","SDMVPSU","SDMVSTRA","SDDSRVYR")

#合并所有年份的人口数据
demo = join_all(list(demo1999,demo2001,demo2003,demo2005,demo2007,demo2009,demo2011,demo2013,demo2015,demo2017),
                           by = c("SEQN"), type="full")
#删除变量，释放内存
rm(demo1999,demo2001,demo2003,demo2005,demo2007,demo2009,demo2011,demo2013,demo2015,demo2017)



#读取职业变量
OCQ1999 = read_xpt("变量/Demographic/OCQ.xpt")
OCQ1999 = OCQ1999 %>% select("SEQN","OCQ260")

OCQ2001 = read_xpt("变量/Demographic/OCQ_B.xpt")
OCQ2001 = OCQ2001 %>% select("SEQN","OCQ260")

OCQ2003 = read_xpt("变量/Demographic/OCQ_C.xpt")
OCQ2003 = OCQ2003 %>% select("SEQN","OCQ260")

OCQ2005 = read_xpt("变量/Demographic/OCQ_D.xpt")
OCQ2005 = OCQ2005 %>% select("SEQN","OCQ260")

OCQ2007 = read_xpt("变量/Demographic/OCQ_E.xpt")
OCQ2007 = OCQ2007 %>% select("SEQN","OCQ260")

OCQ2009 = read_xpt("变量/Demographic/OCQ_F.xpt")
OCQ2009 = OCQ2009 %>% select("SEQN","OCQ260")

OCQ2011 = read_xpt("变量/Demographic/OCQ_G.xpt")
OCQ2011 = OCQ2011 %>% select("SEQN","OCQ260")

OCQ2013 = read_xpt("变量/Demographic/OCQ_H.xpt")
OCQ2013 = OCQ2013 %>% select("SEQN","OCQ260")

OCQ2015 = read_xpt("变量/Demographic/OCQ_I.xpt")
OCQ2015 = OCQ2015 %>% select("SEQN","OCQ260")

OCQ2017 = read_xpt("变量/Demographic/OCQ_J.xpt")
OCQ2017 = OCQ2017 %>% select("SEQN","OCQ260")

#合并所有年份的人口数据
OCQ = join_all(list(OCQ1999,OCQ2001,OCQ2003,OCQ2005,OCQ2007,OCQ2009,OCQ2011,OCQ2013,OCQ2015,OCQ2017),
                by = c("SEQN"), type="full")
#删除变量，释放内存
rm(OCQ1999,OCQ2001,OCQ2003,OCQ2005,OCQ2007,OCQ2009,OCQ2011,OCQ2013,OCQ2015,OCQ2017)



#读取BMI和腰围和体重数据
BMI1999 = read_xpt("变量/Examination/BMX.xpt")
BMI1999 = BMI1999 %>% select("SEQN","BMXBMI","BMXWAIST","BMXWT")

BMI2001 = read_xpt("变量/Examination/BMX_B.xpt")
BMI2001 = BMI2001 %>% select("SEQN","BMXBMI","BMXWAIST","BMXWT")

BMI2003 = read_xpt("变量/Examination/BMX_C.xpt")
BMI2003 = BMI2003 %>% select("SEQN","BMXBMI","BMXWAIST","BMXWT")

BMI2005 = read_xpt("变量/Examination/BMX_D.xpt")
BMI2005 = BMI2005 %>% select("SEQN","BMXBMI","BMXWAIST","BMXWT")

BMI2007 = read_xpt("变量/Examination/BMX_E.xpt")
BMI2007 = BMI2007 %>% select("SEQN","BMXBMI","BMXWAIST","BMXWT")

BMI2009 = read_xpt("变量/Examination/BMX_F.xpt")
BMI2009 = BMI2009 %>% select("SEQN","BMXBMI","BMXWAIST","BMXWT")

BMI2011 = read_xpt("变量/Examination/BMX_G.xpt")
BMI2011 = BMI2011 %>% select("SEQN","BMXBMI","BMXWAIST","BMXWT")

BMI2013 = read_xpt("变量/Examination/BMX_H.xpt")
BMI2013 = BMI2013 %>% select("SEQN","BMXBMI","BMXWAIST","BMXWT")

BMI2015 = read_xpt("变量/Examination/BMX_I.xpt")
BMI2015 = BMI2015 %>% select("SEQN","BMXBMI","BMXWAIST","BMXWT")

BMI2017 = read_xpt("变量/Examination/BMX_J.xpt")
BMI2017 = BMI2017 %>% select("SEQN","BMXBMI","BMXWAIST","BMXWT")

#合并所有年份的BMI和腰围数据
BMX = join_all(list(BMI1999,BMI2001,BMI2003,BMI2005,BMI2007,BMI2009,BMI2011,BMI2013,BMI2015,BMI2017),
                by = c("SEQN"), type="full")
BMX$WWI = BMX$BMXWAIST / sqrt(BMX$BMXWT)
BMX = BMX %>% select("SEQN","BMXBMI","WWI")

#删除变量，释放内存
rm(BMI1999,BMI2001,BMI2003,BMI2005,BMI2007,BMI2009,BMI2011,BMI2013,BMI2015,BMI2017)

#读取吸烟数据
SMQ1999 = read_xpt("变量/Questionnaire/SMQ.xpt")
SMQ1999 = SMQ1999 %>% select("SEQN","SMQ020","SMQ040")

SMQ2001 = read_xpt("变量/Questionnaire/SMQ_B.xpt")
SMQ2001 = SMQ2001 %>% select("SEQN","SMQ020","SMQ040")

SMQ2003 = read_xpt("变量/Questionnaire/SMQ_C.xpt")
SMQ2003 = SMQ2003 %>% select("SEQN","SMQ020","SMQ040")

SMQ2005 = read_xpt("变量/Questionnaire/SMQ_D.xpt")
SMQ2005 = SMQ2005 %>% select("SEQN","SMQ020","SMQ040")

SMQ2007 = read_xpt("变量/Questionnaire/SMQ_E.xpt")
SMQ2007 = SMQ2007 %>% select("SEQN","SMQ020","SMQ040")

SMQ2009 = read_xpt("变量/Questionnaire/SMQ_F.xpt")
SMQ2009 = SMQ2009 %>% select("SEQN","SMQ020","SMQ040")

SMQ2011 = read_xpt("变量/Questionnaire/SMQ_G.xpt")
SMQ2011 = SMQ2011 %>% select("SEQN","SMQ020","SMQ040")

SMQ2013 = read_xpt("变量/Questionnaire/SMQ_H.xpt")
SMQ2013 = SMQ2013 %>% select("SEQN","SMQ020","SMQ040")

SMQ2015 = read_xpt("变量/Questionnaire/SMQ_I.xpt")
SMQ2015 = SMQ2015 %>% select("SEQN","SMQ020","SMQ040")

SMQ2017 = read_xpt("变量/Questionnaire/SMQ_J.xpt")
SMQ2017 = SMQ2017 %>% select("SEQN","SMQ020","SMQ040")

#合并所有年份吸烟数据
SMQ = join_all(list(SMQ1999,SMQ2001,SMQ2003,SMQ2005,SMQ2007,SMQ2009,SMQ2011,SMQ2013,SMQ2015,SMQ2017),
               by = c("SEQN"), type="full")
#删除变量，释放内存
rm(SMQ1999,SMQ2001,SMQ2003,SMQ2005,SMQ2007,SMQ2009,SMQ2011,SMQ2013,SMQ2015,SMQ2017)

#读取高血压协变量
BPQ1999 = read_xpt("变量/Blood Pressure/BPQ.xpt")
BPQ1999 = BPQ1999 %>% select("SEQN","BPQ020")

BPQ2001 = read_xpt("变量/Blood Pressure/BPQ_B.xpt")
BPQ2001 = BPQ2001 %>% select("SEQN","BPQ020")

BPQ2003 = read_xpt("变量/Blood Pressure/BPQ_C.xpt")
BPQ2003 = BPQ2003 %>% select("SEQN","BPQ020")

BPQ2005 = read_xpt("变量/Blood Pressure/BPQ_D.xpt")
BPQ2005 = BPQ2005 %>% select("SEQN","BPQ020")

BPQ2007 = read_xpt("变量/Blood Pressure/BPQ_E.xpt")
BPQ2007 = BPQ2007 %>% select("SEQN","BPQ020")

BPQ2009 = read_xpt("变量/Blood Pressure/BPQ_F.xpt")
BPQ2009 = BPQ2009 %>% select("SEQN","BPQ020")

BPQ2011 = read_xpt("变量/Blood Pressure/BPQ_G.xpt")
BPQ2011 = BPQ2011 %>% select("SEQN","BPQ020")

BPQ2013 = read_xpt("变量/Blood Pressure/BPQ_H.xpt")
BPQ2013 = BPQ2013 %>% select("SEQN","BPQ020")

BPQ2015 = read_xpt("变量/Blood Pressure/BPQ_I.xpt")
BPQ2015 = BPQ2015 %>% select("SEQN","BPQ020")

BPQ2017 = read_xpt("变量/Blood Pressure/BPQ_J.xpt")
BPQ2017 = BPQ2017 %>% select("SEQN","BPQ020")

#合并所有年份高血压问卷数据
BPQ = join_all(list(BPQ1999,BPQ2001,BPQ2003,BPQ2005,BPQ2007,BPQ2009,BPQ2011,BPQ2013,BPQ2015,BPQ2017),
               by = c("SEQN"), type="full")
#删除变量，释放内存
rm(BPQ1999,BPQ2001,BPQ2003,BPQ2005,BPQ2007,BPQ2009,BPQ2011,BPQ2013,BPQ2015,BPQ2017)

#读取平均收缩压舒张压数据
BPX1999 = read_xpt("变量/Blood Pressure/BPX.xpt")
BPX1999 = BPX1999 %>% select("SEQN","BPXSY1","BPXSY2","BPXSY3","BPXSY4","BPXDI1","BPXDI2","BPXDI3","BPXDI4")

BPX2001 = read_xpt("变量/Blood Pressure/BPX_B.xpt")
BPX2001 = BPX2001 %>% select("SEQN","BPXSY1","BPXSY2","BPXSY3","BPXSY4","BPXDI1","BPXDI2","BPXDI3","BPXDI4")

BPX2003 = read_xpt("变量/Blood Pressure/BPX_C.xpt")
BPX2003 = BPX2003 %>% select("SEQN","BPXSY1","BPXSY2","BPXSY3","BPXSY4","BPXDI1","BPXDI2","BPXDI3","BPXDI4")

BPX2005 = read_xpt("变量/Blood Pressure/BPX_D.xpt")
BPX2005 = BPX2005 %>% select("SEQN","BPXSY1","BPXSY2","BPXSY3","BPXSY4","BPXDI1","BPXDI2","BPXDI3","BPXDI4")

BPX2007 = read_xpt("变量/Blood Pressure/BPX_E.xpt")
BPX2007 = BPX2007 %>% select("SEQN","BPXSY1","BPXSY2","BPXSY3","BPXSY4","BPXDI1","BPXDI2","BPXDI3","BPXDI4")

BPX2009 = read_xpt("变量/Blood Pressure/BPX_F.xpt")
BPX2009 = BPX2009 %>% select("SEQN","BPXSY1","BPXSY2","BPXSY3","BPXSY4","BPXDI1","BPXDI2","BPXDI3","BPXDI4")

BPX2011 = read_xpt("变量/Blood Pressure/BPX_G.xpt")
BPX2011 = BPX2011 %>% select("SEQN","BPXSY1","BPXSY2","BPXSY3","BPXSY4","BPXDI1","BPXDI2","BPXDI3","BPXDI4")

BPX2013 = read_xpt("变量/Blood Pressure/BPX_H.xpt")
BPX2013 = BPX2013 %>% select("SEQN","BPXSY1","BPXSY2","BPXSY3","BPXSY4","BPXDI1","BPXDI2","BPXDI3","BPXDI4")

BPX2015 = read_xpt("变量/Blood Pressure/BPX_I.xpt")
BPX2015 = BPX2015 %>% select("SEQN","BPXSY1","BPXSY2","BPXSY3","BPXSY4","BPXDI1","BPXDI2","BPXDI3","BPXDI4")

BPX2017 = read_xpt("变量/Blood Pressure/BPX_J.xpt")
BPX2017 = BPX2017 %>% select("SEQN","BPXSY1","BPXSY2","BPXSY3","BPXSY4","BPXDI1","BPXDI2","BPXDI3","BPXDI4")

#合并所有年份平均收缩压舒张压数据
BPX = join_all(list(BPX1999,BPX2001,BPX2003,BPX2005,BPX2007,BPX2009,BPX2011,BPX2013,BPX2015,BPX2017),
               by = c("SEQN"), type="full")
#删除变量，释放内存
rm(BPX1999,BPX2001,BPX2003,BPX2005,BPX2007,BPX2009,BPX2011,BPX2013,BPX2015,BPX2017)
BPX$SBP = rowMeans(subset(BPX, select = c("BPXSY1", "BPXSY2", "BPXSY3", "BPXSY4")), na.rm=TRUE)
BPX$DBP = rowMeans(subset(BPX, select = c("BPXDI1", "BPXDI2", "BPXDI3", "BPXDI4")), na.rm=TRUE)
BPX = BPX %>% select("SEQN","SBP","DBP")

#读取癌症数据
MCQ1999 = read_xpt("变量/Questionnaire/MCQ.xpt")
MCQ1999 = MCQ1999 %>% select("SEQN","MCQ220")

MCQ2001 = read_xpt("变量/Questionnaire/MCQ_B.xpt")
MCQ2001 = MCQ2001 %>% select("SEQN","MCQ220")

MCQ2003 = read_xpt("变量/Questionnaire/MCQ_C.xpt")
MCQ2003 = MCQ2003 %>% select("SEQN","MCQ220")

MCQ2005 = read_xpt("变量/Questionnaire/MCQ_D.xpt")
MCQ2005 = MCQ2005 %>% select("SEQN","MCQ220")

MCQ2007 = read_xpt("变量/Questionnaire/MCQ_E.xpt")
MCQ2007 = MCQ2007 %>% select("SEQN","MCQ220")

MCQ2009 = read_xpt("变量/Questionnaire/MCQ_F.xpt")
MCQ2009 = MCQ2009 %>% select("SEQN","MCQ220")

MCQ2011 = read_xpt("变量/Questionnaire/MCQ_G.xpt")
MCQ2011 = MCQ2011 %>% select("SEQN","MCQ220")

MCQ2013 = read_xpt("变量/Questionnaire/MCQ_H.xpt")
MCQ2013 = MCQ2013 %>% select("SEQN","MCQ220")

MCQ2015 = read_xpt("变量/Questionnaire/MCQ_I.xpt")
MCQ2015 = MCQ2015 %>% select("SEQN","MCQ220")

MCQ2017 = read_xpt("变量/Questionnaire/MCQ_J.xpt")
MCQ2017 = MCQ2017 %>% select("SEQN","MCQ220")

#合并癌症数据
MCQ = join_all(list(MCQ1999,MCQ2001,MCQ2003,MCQ2005,MCQ2007,MCQ2009,MCQ2011,MCQ2013,MCQ2015,MCQ2017),
               by = c("SEQN"), type="full")
#删除变量，释放内存
rm(MCQ1999,MCQ2001,MCQ2003,MCQ2005,MCQ2007,MCQ2009,MCQ2011,MCQ2013,MCQ2015,MCQ2017)

#读取糖尿病和糖尿病前期协变量
DIQ1999 = read_xpt("变量/Questionnaire/DIQ.xpt")
DIQ1999 = DIQ1999 %>% select("SEQN","DIQ010","DIQ050","DIQ070")

DIQ2001 = read_xpt("变量/Questionnaire/DIQ_B.xpt")
DIQ2001 = DIQ2001 %>% select("SEQN","DIQ010","DIQ050","DIQ070")

DIQ2003 = read_xpt("变量/Questionnaire/DIQ_C.xpt")
DIQ2003 = DIQ2003 %>% select("SEQN","DIQ010","DIQ050","DIQ070")
#该年份变量名字不一样
DIQ2005 = read_xpt("变量/Questionnaire/DIQ_D.xpt")
DIQ2005 = DIQ2005 %>% select("SEQN","DIQ010","DIQ050","DID070","DIQ160")
DIQ2005 = rename(DIQ2005, c("DID070"="DIQ070"))
#该年份变量名字不一样
DIQ2007 = read_xpt("变量/Questionnaire/DIQ_E.xpt")
DIQ2007 = DIQ2007 %>% select("SEQN","DIQ010","DIQ050","DID070","DIQ160")
DIQ2007 = rename(DIQ2007, c("DID070"="DIQ070"))

DIQ2009 = read_xpt("变量/Questionnaire/DIQ_F.xpt")
DIQ2009 = DIQ2009 %>% select("SEQN","DIQ010","DIQ050","DIQ070","DIQ160")

DIQ2011 = read_xpt("变量/Questionnaire/DIQ_G.xpt")
DIQ2011 = DIQ2011 %>% select("SEQN","DIQ010","DIQ050","DIQ070","DIQ160")

DIQ2013 = read_xpt("变量/Questionnaire/DIQ_H.xpt")
DIQ2013 = DIQ2013 %>% select("SEQN","DIQ010","DIQ050","DIQ070","DIQ160")

DIQ2015 = read_xpt("变量/Questionnaire/DIQ_I.xpt")
DIQ2015 = DIQ2015 %>% select("SEQN","DIQ010","DIQ050","DIQ070","DIQ160")

DIQ2017 = read_xpt("变量/Questionnaire/DIQ_J.xpt")
DIQ2017 = DIQ2017 %>% select("SEQN","DIQ010","DIQ050","DIQ070","DIQ160")

#合并糖尿病问卷数据
DIQ = join_all(list(DIQ1999,DIQ2001,DIQ2003,DIQ2005,DIQ2007,DIQ2009,DIQ2011,DIQ2013,DIQ2015,DIQ2017),
               by = c("SEQN"), type="full")
#删除变量，释放内存
rm(DIQ1999,DIQ2001,DIQ2003,DIQ2005,DIQ2007,DIQ2009,DIQ2011,DIQ2013,DIQ2015,DIQ2017)

#读取空腹血糖数据
LAB1999 = read_xpt("变量/Laboratory/LAB10AM.xpt")
LAB1999 = LAB1999 %>% select("SEQN","LBXGLU")

LAB2001 = read_xpt("变量/Laboratory/L10AM_B.xpt")
LAB2001 = LAB2001 %>% select("SEQN","LBXGLU")

LAB2003 = read_xpt("变量/Laboratory/L10AM_C.xpt")
LAB2003 = LAB2003 %>% select("SEQN","LBXGLU")

LAB2005 = read_xpt("变量/Laboratory/GLU_D.xpt")
LAB2005 = LAB2005 %>% select("SEQN","LBXGLU")

LAB2007 = read_xpt("变量/Laboratory/GLU_E.xpt")
LAB2007 = LAB2007 %>% select("SEQN","LBXGLU")

LAB2009 = read_xpt("变量/Laboratory/GLU_F.xpt")
LAB2009 = LAB2009 %>% select("SEQN","LBXGLU")

LAB2011 = read_xpt("变量/Laboratory/GLU_G.xpt")
LAB2011 = LAB2011 %>% select("SEQN","LBXGLU")

LAB2013 = read_xpt("变量/Laboratory/GLU_H.xpt")
LAB2013 = LAB2013 %>% select("SEQN","LBXGLU")

LAB2015 = read_xpt("变量/Laboratory/GLU_I.xpt")
LAB2015 = LAB2015 %>% select("SEQN","LBXGLU")

LAB2017 = read_xpt("变量/Laboratory/GLU_J.xpt")
LAB2017 = LAB2017 %>% select("SEQN","LBXGLU")

#合并空腹血糖数据
LAB = join_all(list(LAB1999,LAB2001,LAB2003,LAB2005,LAB2007,LAB2009,LAB2011,LAB2013,LAB2015,LAB2017),
               by = c("SEQN"), type="full")
#删除变量，释放内存
rm(LAB1999,LAB2001,LAB2003,LAB2005,LAB2007,LAB2009,LAB2011,LAB2013,LAB2015,LAB2017)

#读取糖化血红蛋白数据
GHB1999 = read_xpt("变量/Laboratory/LAB10.xpt")
GHB1999 = GHB1999 %>% select("SEQN","LBXGH")

GHB2001 = read_xpt("变量/Laboratory/L10_B.xpt")
GHB2001 = GHB2001 %>% select("SEQN","LBXGH")

GHB2003 = read_xpt("变量/Laboratory/L10_C.xpt")
GHB2003 = GHB2003 %>% select("SEQN","LBXGH")

GHB2005 = read_xpt("变量/Laboratory/GHB_D.xpt")
GHB2005 = GHB2005 %>% select("SEQN","LBXGH")

GHB2007 = read_xpt("变量/Laboratory/GHB_E.xpt")
GHB2007 = GHB2007 %>% select("SEQN","LBXGH")

GHB2009 = read_xpt("变量/Laboratory/GHB_F.xpt")
GHB2009 = GHB2009 %>% select("SEQN","LBXGH")

GHB2011 = read_xpt("变量/Laboratory/GHB_G.xpt")
GHB2011 = GHB2011 %>% select("SEQN","LBXGH")

GHB2013 = read_xpt("变量/Laboratory/GHB_H.xpt")
GHB2013 = GHB2013 %>% select("SEQN","LBXGH")

GHB2015 = read_xpt("变量/Laboratory/GHB_I.xpt")
GHB2015 = GHB2015 %>% select("SEQN","LBXGH")

GHB2017 = read_xpt("变量/Laboratory/GHB_J.xpt")
GHB2017 = GHB2017 %>% select("SEQN","LBXGH")

#合并糖化血红蛋白数据
GHB = join_all(list(GHB1999,GHB2001,GHB2003,GHB2005,GHB2007,GHB2009,GHB2011,GHB2013,GHB2015,GHB2017),
               by = c("SEQN"), type="full")
#删除变量，释放内存
rm(GHB1999,GHB2001,GHB2003,GHB2005,GHB2007,GHB2009,GHB2011,GHB2013,GHB2015,GHB2017)

#读取谷氨酰转移酶和尿酸数据
BIO1999 = read_xpt("变量/Laboratory/LAB18.xpt")
BIO1999 = BIO1999 %>% select("SEQN","LBXSGTSI","LBDSUASI","LBXSUA")

BIO2001 = read_xpt("变量/Laboratory/L40_B.xpt")
BIO2001 = BIO2001 %>% select("SEQN","LBXSGTSI","LBDSUASI","LBXSUA")

BIO2003 = read_xpt("变量/Laboratory/L40_C.xpt")
BIO2003 = BIO2003 %>% select("SEQN","LBXSGTSI","LBDSUASI","LBXSUA")

BIO2005 = read_xpt("变量/Laboratory/BIOPRO_D.xpt")
BIO2005 = BIO2005 %>% select("SEQN","LBXSGTSI","LBDSUASI","LBXSUA")

BIO2007 = read_xpt("变量/Laboratory/BIOPRO_E.xpt")
BIO2007 = BIO2007 %>% select("SEQN","LBXSGTSI","LBDSUASI","LBXSUA")

BIO2009 = read_xpt("变量/Laboratory/BIOPRO_F.xpt")
BIO2009 = BIO2009 %>% select("SEQN","LBXSGTSI","LBDSUASI","LBXSUA")

BIO2011 = read_xpt("变量/Laboratory/BIOPRO_G.xpt")
BIO2011 = BIO2011 %>% select("SEQN","LBXSGTSI","LBDSUASI","LBXSUA")

BIO2013 = read_xpt("变量/Laboratory/BIOPRO_H.xpt")
BIO2013 = BIO2013 %>% select("SEQN","LBXSGTSI","LBDSUASI","LBXSUA")

BIO2015 = read_xpt("变量/Laboratory/BIOPRO_I.xpt")
BIO2015 = BIO2015 %>% select("SEQN","LBXSGTSI","LBDSUASI","LBXSUA")

BIO2017 = read_xpt("变量/Laboratory/BIOPRO_J.xpt")
BIO2017 = BIO2017 %>% select("SEQN","LBXSGTSI","LBDSUASI","LBXSUA")

#合并谷氨酰转移酶数据
BIO = join_all(list(BIO1999,BIO2001,BIO2003,BIO2005,BIO2007,BIO2009,BIO2011,BIO2013,BIO2015,BIO2017),
               by = c("SEQN"), type="full")
BIO = rename(BIO, c("LBXSGTSI"="GGT", "LBDSUASI"="UA"))
BIO = BIO %>% select("SEQN","GGT","UA")

#删除变量，释放内存
rm(BIO1999,BIO2001,BIO2003,BIO2005,BIO2007,BIO2009,BIO2011,BIO2013,BIO2015,BIO2017)


#读取HDL数据
HDL1999 = read_xpt("变量/Laboratory/LAB13.xpt")
HDL1999 = HDL1999 %>% select("SEQN","LBDHDL","LBDHDLSI")
HDL1999 = rename(HDL1999, c("LBDHDL"="LBDHDD", "LBDHDLSI"="LBDHDDSI"))

HDL2001 = read_xpt("变量/Laboratory/L13_B.xpt")
HDL2001 = HDL2001 %>% select("SEQN","LBDHDL","LBDHDLSI")
HDL2001 = rename(HDL1999, c("LBDHDL"="LBDHDD", "LBDHDLSI"="LBDHDDSI"))

HDL2003 = read_xpt("变量/Laboratory/L13_C.xpt")
HDL2003 = HDL2003 %>% select("SEQN","LBXHDD","LBDHDDSI")
HDL2003 = rename(HDL2003, c("LBXHDD"="LBDHDD", "LBDHDDSI"="LBDHDDSI"))

HDL2005 = read_xpt("变量/Laboratory/HDL_D.xpt")
HDL2005 = HDL2005 %>% select("SEQN","LBDHDD","LBDHDDSI")

HDL2007 = read_xpt("变量/Laboratory/HDL_E.xpt")
HDL2007 = HDL2007 %>% select("SEQN","LBDHDD","LBDHDDSI")

HDL2009 = read_xpt("变量/Laboratory/HDL_F.xpt")
HDL2009 = HDL2009 %>% select("SEQN","LBDHDD","LBDHDDSI")

HDL2011 = read_xpt("变量/Laboratory/HDL_G.xpt")
HDL2011 = HDL2011 %>% select("SEQN","LBDHDD","LBDHDDSI")

HDL2013 = read_xpt("变量/Laboratory/HDL_H.xpt")
HDL2013 = HDL2013 %>% select("SEQN","LBDHDD","LBDHDDSI")

HDL2015 = read_xpt("变量/Laboratory/HDL_I.xpt")
HDL2015 = HDL2015 %>% select("SEQN","LBDHDD","LBDHDDSI")

HDL2017 = read_xpt("变量/Laboratory/HDL_J.xpt")
HDL2017 = HDL2017 %>% select("SEQN","LBDHDD","LBDHDDSI")

#合并HDL数据
HDL = join_all(list(HDL1999,HDL2001,HDL2003,HDL2005,HDL2007,HDL2009,HDL2011,HDL2013,HDL2015,HDL2017),
               by = c("SEQN"), type="full")
HDL = rename(HDL, c("LBDHDDSI"="HDL"))
HDL = HDL %>% select("SEQN","HDL")
#删除变量，释放内存
rm(HDL1999,HDL2001,HDL2003,HDL2005,HDL2007,HDL2009,HDL2011,HDL2013,HDL2015,HDL2017)

#提取炎症数据
INF1999 = read_xpt("变量/Laboratory/LAB25.xpt")
INF1999 = INF1999 %>% select("SEQN","LBDNENO","LBDLYMNO","LBDMONO","LBXPLTSI")

INF2001 = read_xpt("变量/Laboratory/L25_B.xpt")
INF2001 = INF2001 %>% select("SEQN","LBDNENO","LBDLYMNO","LBDMONO","LBXPLTSI")

INF2003 = read_xpt("变量/Laboratory/L25_C.xpt")
INF2003 = INF2003 %>% select("SEQN","LBDNENO","LBDLYMNO","LBDMONO","LBXPLTSI")

INF2005 = read_xpt("变量/Laboratory/CBC_D.xpt")
INF2005 = INF2005 %>% select("SEQN","LBDNENO","LBDLYMNO","LBDMONO","LBXPLTSI")

INF2007 = read_xpt("变量/Laboratory/CBC_E.xpt")
INF2007 = INF2007 %>% select("SEQN","LBDNENO","LBDLYMNO","LBDMONO","LBXPLTSI")

INF2009 = read_xpt("变量/Laboratory/CBC_F.xpt")
INF2009 = INF2009 %>% select("SEQN","LBDNENO","LBDLYMNO","LBDMONO","LBXPLTSI")

INF2011 = read_xpt("变量/Laboratory/CBC_G.xpt")
INF2011 = INF2011 %>% select("SEQN","LBDNENO","LBDLYMNO","LBDMONO","LBXPLTSI")

INF2013 = read_xpt("变量/Laboratory/CBC_H.xpt")
INF2013 = INF2013 %>% select("SEQN","LBDNENO","LBDLYMNO","LBDMONO","LBXPLTSI")

INF2015 = read_xpt("变量/Laboratory/CBC_I.xpt")
INF2015 = INF2015 %>% select("SEQN","LBDNENO","LBDLYMNO","LBDMONO","LBXPLTSI")

INF2017 = read_xpt("变量/Laboratory/CBC_J.xpt")
INF2017 = INF2017 %>% select("SEQN","LBDNENO","LBDLYMNO","LBDMONO","LBXPLTSI")

#合并分段中性粒细胞数量数据
INF = join_all(list(INF1999,INF2001,INF2003,INF2005,INF2007,INF2009,INF2011,INF2013,INF2015,INF2017),
               by = c("SEQN"), type="full")

#删除变量，释放内存
rm(INF1999,INF2001,INF2003,INF2005,INF2007,INF2009,INF2011,INF2013,INF2015,INF2017)

#提取血镉数据
CD1999 = read_xpt("变量/Laboratory/LAB06.xpt")
CD1999 = CD1999 %>% select("SEQN","LBXBCD")

CD2001 = read_xpt("变量/Laboratory/L06_B.xpt")
CD2001 = CD2001 %>% select("SEQN","LBXBCD")

CD2003 = read_xpt("变量/Laboratory/L06BMT_C.xpt")
CD2003 = CD2003 %>% select("SEQN","LBXBCD")

CD2005 = read_xpt("变量/Laboratory/PBCD_D.xpt")
CD2005 = CD2005 %>% select("SEQN","LBXBCD")

CD2007 = read_xpt("变量/Laboratory/PBCD_E.xpt")
CD2007 = CD2007 %>% select("SEQN","LBXBCD")

CD2009 = read_xpt("变量/Laboratory/PBCD_F.xpt")
CD2009 = CD2009 %>% select("SEQN","LBXBCD")

CD2011 = read_xpt("变量/Laboratory/PBCD_G.xpt")
CD2011 = CD2011 %>% select("SEQN","LBXBCD")

CD2013 = read_xpt("变量/Laboratory/PBCD_H.xpt")
CD2013 = CD2013 %>% select("SEQN","LBXBCD")

CD2015 = read_xpt("变量/Laboratory/PBCD_I.xpt")
CD2015 = CD2015 %>% select("SEQN","LBXBCD")

CD2017 = read_xpt("变量/Laboratory/PBCD_J.xpt")
CD2017 = CD2017 %>% select("SEQN","LBXBCD")

#合并分段中性粒细胞数量数据
CD = join_all(list(CD1999,CD2001,CD2003,CD2005,CD2007,CD2009,CD2011,CD2013,CD2015,CD2017),
               by = c("SEQN"), type="full")

#删除变量，释放内存
rm(CD1999,CD2001,CD2003,CD2005,CD2007,CD2009,CD2011,CD2013,CD2015,CD2017)


#读取死亡数据
library(readr)
death_1999<-c("变量/dead/NHANES_1999_2000_MORT_2019_PUBLIC.dat")#文件地址
death_1999 <- read_fwf(file=death_1999,#上面定义的名称
                  col_types = "iiiiiiii",
                  fwf_cols(seqn = c(1,6),eligstat = c(15,15),mortstat = c(16,16),ucod_leading = c(17,19),
                           diabetes = c(20,20),hyperten = c(21,21),permth_int = c(43,45),permth_exm = c(46,48)),
                  na = c("", "."))

death_2001<-c("变量/dead/NHANES_2001_2002_MORT_2019_PUBLIC.dat")#文件地址
death_2001 <- read_fwf(file=death_2001,#上面定义的名称
                     col_types = "iiiiiiii",
                     fwf_cols(seqn = c(1,6),eligstat = c(15,15),mortstat = c(16,16),ucod_leading = c(17,19),
                              diabetes = c(20,20),hyperten = c(21,21),permth_int = c(43,45),permth_exm = c(46,48)),
                     na = c("", "."))

death_2003<-c("变量/dead/NHANES_2003_2004_MORT_2019_PUBLIC.dat")#文件地址
death_2003 <- read_fwf(file=death_2003,#上面定义的名称
                     col_types = "iiiiiiii",
                     fwf_cols(seqn = c(1,6),eligstat = c(15,15),mortstat = c(16,16),ucod_leading = c(17,19),
                              diabetes = c(20,20),hyperten = c(21,21),permth_int = c(43,45),permth_exm = c(46,48)),
                     na = c("", "."))

death_2005<-c("变量/dead/NHANES_2005_2006_MORT_2019_PUBLIC.dat")#文件地址
death_2005 <- read_fwf(file=death_2005,#上面定义的名称
                     col_types = "iiiiiiii",
                     fwf_cols(seqn = c(1,6),eligstat = c(15,15),mortstat = c(16,16),ucod_leading = c(17,19),
                              diabetes = c(20,20),hyperten = c(21,21),permth_int = c(43,45),permth_exm = c(46,48)),
                     na = c("", "."))

death_2007<-c("变量/dead/NHANES_2007_2008_MORT_2019_PUBLIC.dat")#文件地址
death_2007 <- read_fwf(file=death_2007,#上面定义的名称
                       col_types = "iiiiiiii",
                       fwf_cols(seqn = c(1,6),eligstat = c(15,15),mortstat = c(16,16),ucod_leading = c(17,19),
                                diabetes = c(20,20),hyperten = c(21,21),permth_int = c(43,45),permth_exm = c(46,48)),
                       na = c("", "."))

death_2009<-c("变量/dead/NHANES_2009_2010_MORT_2019_PUBLIC.dat")#文件地址
death_2009 <- read_fwf(file=death_2009,#上面定义的名称
                       col_types = "iiiiiiii",
                       fwf_cols(seqn = c(1,6),eligstat = c(15,15),mortstat = c(16,16),ucod_leading = c(17,19),
                                diabetes = c(20,20),hyperten = c(21,21),permth_int = c(43,45),permth_exm = c(46,48)),
                       na = c("", "."))

death_2011<-c("变量/dead/NHANES_2011_2012_MORT_2019_PUBLIC.dat")#文件地址
death_2011 <- read_fwf(file=death_2011,#上面定义的名称
                       col_types = "iiiiiiii",
                       fwf_cols(seqn = c(1,6),eligstat = c(15,15),mortstat = c(16,16),ucod_leading = c(17,19),
                                diabetes = c(20,20),hyperten = c(21,21),permth_int = c(43,45),permth_exm = c(46,48)),
                       na = c("", "."))

death_2013<-c("变量/dead/NHANES_2013_2014_MORT_2019_PUBLIC.dat")#文件地址
death_2013 <- read_fwf(file=death_2013,#上面定义的名称
                       col_types = "iiiiiiii",
                       fwf_cols(seqn = c(1,6),eligstat = c(15,15),mortstat = c(16,16),ucod_leading = c(17,19),
                                diabetes = c(20,20),hyperten = c(21,21),permth_int = c(43,45),permth_exm = c(46,48)),
                       na = c("", "."))

death_2015<-c("变量/dead/NHANES_2015_2016_MORT_2019_PUBLIC.dat")#文件地址
death_2015 <- read_fwf(file=death_2015,#上面定义的名称
                       col_types = "iiiiiiii",
                       fwf_cols(seqn = c(1,6),eligstat = c(15,15),mortstat = c(16,16),ucod_leading = c(17,19),
                                diabetes = c(20,20),hyperten = c(21,21),permth_int = c(43,45),permth_exm = c(46,48)),
                       na = c("", "."))

death_2017<-c("变量/dead/NHANES_2017_2018_MORT_2019_PUBLIC.dat")#文件地址
death_2017 <- read_fwf(file=death_2017,#上面定义的名称
                       col_types = "iiiiiiii",
                       fwf_cols(seqn = c(1,6),eligstat = c(15,15),mortstat = c(16,16),ucod_leading = c(17,19),
                                diabetes = c(20,20),hyperten = c(21,21),permth_int = c(43,45),permth_exm = c(46,48)),
                       na = c("", "."))

#合并死亡数据
DEATH = join_all(list(death_1999,death_2001,death_2003,death_2005,death_2007,death_2009,death_2011,death_2013,death_2015,death_2017),
              by = c("seqn"), type="full")
DEATH = rename(DEATH, c("seqn"="SEQN"))
DEATH$CVDDTH = ifelse(DEATH$ucod_leading == 1 | DEATH$ucod_leading == 5,1,0)
DEATH$CANSERDTH = ifelse(DEATH$ucod_leading == 2,1,0)
DEATH = DEATH %>% select("SEQN","mortstat","CVDDTH","CANSERDTH","permth_int","permth_exm")

#删除变量，释放内存
rm(death_1999,death_2001,death_2003,death_2005,death_2007,death_2009,death_2011,death_2013,death_2015,death_2017)


#提取喝酒数据
ALQ1999 = read_xpt("变量/Examination/DRXTOT.xpt")
ALQ1999 = ALQ1999 %>% select("SEQN","DRXTALCO")

ALQ2001 = read_xpt("变量/Examination/DRXTOT_B.xpt")
ALQ2001 = ALQ2001 %>% select("SEQN","DRXTALCO")

ALQ2003 = read_xpt("变量/Examination/DR1TOT_C.xpt")
ALQ2003 = ALQ2003 %>% select("SEQN","DR1TALCO")
ALQ2003_2 = read_xpt("变量/Examination/DR2TOT_C.xpt")
ALQ2003_2 = ALQ2003_2 %>% select("SEQN","DR2TALCO")
ALQ2003 = join_all(list(ALQ2003,ALQ2003_2), by = c("SEQN"), type="full")
ALQ2003$DRXTALCO = apply(ALQ2003[,c(2,3)],1,mean,na.rm = T)
ALQ2003 = ALQ2003 %>% select("SEQN","DRXTALCO")

ALQ2005 = read_xpt("变量/Examination/DR1TOT_D.xpt")
ALQ2005 = ALQ2005 %>% select("SEQN","DR1TALCO")
ALQ2005_2 = read_xpt("变量/Examination/DR2TOT_D.xpt")
ALQ2005_2 = ALQ2005_2 %>% select("SEQN","DR2TALCO")
ALQ2005 = join_all(list(ALQ2005,ALQ2005_2), by = c("SEQN"), type="full")
ALQ2005$DRXTALCO = apply(ALQ2005[,c(2,3)],1,mean,na.rm = T)
ALQ2005 = ALQ2005 %>% select("SEQN","DRXTALCO")

ALQ2007 = read_xpt("变量/Examination/DR1TOT_E.xpt")
ALQ2007 = ALQ2007 %>% select("SEQN","DR1TALCO")
ALQ2007_2 = read_xpt("变量/Examination/DR2TOT_E.xpt")
ALQ2007_2 = ALQ2007_2 %>% select("SEQN","DR2TALCO")
ALQ2007 = join_all(list(ALQ2007,ALQ2007_2), by = c("SEQN"), type="full")
ALQ2007$DRXTALCO = apply(ALQ2007[,c(2,3)],1,mean,na.rm = T)
ALQ2007 = ALQ2007 %>% select("SEQN","DRXTALCO")

ALQ2009 = read_xpt("变量/Examination/DR1TOT_F.xpt")
ALQ2009 = ALQ2009 %>% select("SEQN","DR1TALCO")
ALQ2009_2 = read_xpt("变量/Examination/DR2TOT_F.xpt")
ALQ2009_2 = ALQ2009_2 %>% select("SEQN","DR2TALCO")
ALQ2009 = join_all(list(ALQ2009,ALQ2009_2), by = c("SEQN"), type="full")
ALQ2009$DRXTALCO = apply(ALQ2009[,c(2,3)],1,mean,na.rm = T)
ALQ2009 = ALQ2009 %>% select("SEQN","DRXTALCO")

ALQ2011 = read_xpt("变量/Examination/DR1TOT_G.xpt")
ALQ2011 = ALQ2011 %>% select("SEQN","DR1TALCO")
ALQ2011_2 = read_xpt("变量/Examination/DR2TOT_G.xpt")
ALQ2011_2 = ALQ2011_2 %>% select("SEQN","DR2TALCO")
ALQ2011 = join_all(list(ALQ2011,ALQ2011_2), by = c("SEQN"), type="full")
ALQ2011$DRXTALCO = apply(ALQ2011[,c(2,3)],1,mean,na.rm = T)
ALQ2011 = ALQ2011 %>% select("SEQN","DRXTALCO")

ALQ2013 = read_xpt("变量/Examination/DR1TOT_H.xpt")
ALQ2013 = ALQ2013 %>% select("SEQN","DR1TALCO")
ALQ2013_2 = read_xpt("变量/Examination/DR2TOT_H.xpt")
ALQ2013_2 = ALQ2013_2 %>% select("SEQN","DR2TALCO")
ALQ2013 = join_all(list(ALQ2013,ALQ2013_2), by = c("SEQN"), type="full")
ALQ2013$DRXTALCO = apply(ALQ2013[,c(2,3)],1,mean,na.rm = T)
ALQ2013 = ALQ2013 %>% select("SEQN","DRXTALCO")

ALQ2015 = read_xpt("变量/Examination/DR1TOT_I.xpt")
ALQ2015 = ALQ2015 %>% select("SEQN","DR1TALCO")
ALQ2015_2 = read_xpt("变量/Examination/DR2TOT_I.xpt")
ALQ2015_2 = ALQ2015_2 %>% select("SEQN","DR2TALCO")
ALQ2015 = join_all(list(ALQ2015,ALQ2015_2), by = c("SEQN"), type="full")
ALQ2015$DRXTALCO = apply(ALQ2015[,c(2,3)],1,mean,na.rm = T)
ALQ2015 = ALQ2015 %>% select("SEQN","DRXTALCO")

ALQ2017 = read_xpt("变量/Examination/DR1TOT_J.xpt")
ALQ2017 = ALQ2017 %>% select("SEQN","DR1TALCO")
ALQ2017_2 = read_xpt("变量/Examination/DR2TOT_J.xpt")
ALQ2017_2 = ALQ2017_2 %>% select("SEQN","DR2TALCO")
ALQ2017 = join_all(list(ALQ2017,ALQ2017_2), by = c("SEQN"), type="full")
ALQ2017$DRXTALCO = apply(ALQ2017[,c(2,3)],1,mean,na.rm = T)
ALQ2017 = ALQ2017 %>% select("SEQN","DRXTALCO")

#合并分段中性粒细胞数量数据
ALQ = join_all(list(ALQ1999,ALQ2001,ALQ2003,ALQ2005,ALQ2007,ALQ2009,ALQ2011,ALQ2013,ALQ2015,ALQ2017),
              by = c("SEQN"), type="full")

#删除变量，释放内存
rm(ALQ1999,ALQ2001,ALQ2003,ALQ2005,ALQ2007,ALQ2009,ALQ2011,ALQ2013,ALQ2015,ALQ2017,
   ALQ2003_2,ALQ2005_2,ALQ2007_2,ALQ2009_2,ALQ2011_2,ALQ2013_2,ALQ2015_2,ALQ2017_2)


#提取肾衰竭变量
KIQ1999 = read_xpt("变量/Examination/KIQ.xpt")
KIQ1999 = KIQ1999 %>% select("SEQN","KIQ020")
KIQ1999 = rename(KIQ1999, c("KIQ020"="KIQ022"))

KIQ2001 = read_xpt("变量/Examination/KIQ_U_B.xpt")
KIQ2001 = KIQ2001 %>% select("SEQN","KIQ022")

KIQ2003 = read_xpt("变量/Examination/KIQ_U_C.xpt")
KIQ2003 = KIQ2003 %>% select("SEQN","KIQ022")

KIQ2005 = read_xpt("变量/Examination/KIQ_U_D.xpt")
KIQ2005 = KIQ2005 %>% select("SEQN","KIQ022")

KIQ2007 = read_xpt("变量/Examination/KIQ_U_E.xpt")
KIQ2007 = KIQ2007 %>% select("SEQN","KIQ022")

KIQ2009 = read_xpt("变量/Examination/KIQ_U_F.xpt")
KIQ2009 = KIQ2009 %>% select("SEQN","KIQ022")

KIQ2011 = read_xpt("变量/Examination/KIQ_U_G.xpt")
KIQ2011 = KIQ2011 %>% select("SEQN","KIQ022")

KIQ2013 = read_xpt("变量/Examination/KIQ_U_H.xpt")
KIQ2013 = KIQ2013 %>% select("SEQN","KIQ022")

KIQ2015 = read_xpt("变量/Examination/KIQ_U_I.xpt")
KIQ2015 = KIQ2015 %>% select("SEQN","KIQ022")

KIQ2017 = read_xpt("变量/Examination/KIQ_U_J.xpt")
KIQ2017 = KIQ2017 %>% select("SEQN","KIQ022")

#合并分段中性粒细胞数量数据
KIQ = join_all(list(KIQ1999,KIQ2001,KIQ2003,KIQ2005,KIQ2007,KIQ2009,KIQ2011,KIQ2013,KIQ2015,KIQ2017),
              by = c("SEQN"), type="full")

#删除变量，释放内存
rm(KIQ1999,KIQ2001,KIQ2003,KIQ2005,KIQ2007,KIQ2009,KIQ2011,KIQ2013,KIQ2015,KIQ2017)


#提取PA数据
PAQ1999 = read_xpt("变量/Questionnaire/PAQ.xpt")
PAQ1999$PAQ050U[PAQ1999$PAQ050U == 1] = 1
PAQ1999$PAQ050U[PAQ1999$PAQ050U == 2] = 7
PAQ1999$PAQ050U[PAQ1999$PAQ050U == 3] = 30
PAQ1999$PAQ050U[PAQ1999$PAQ050U == 7] = NA
PAQ1999$PAQ050U[PAQ1999$PAQ050U == 9] = NA
PAQ1999$bike<-ifelse(PAQ1999$PAD080 <= 480,PAQ1999$PAQ050Q*PAQ1999$PAD080*4/PAQ1999$PAQ050U*7,NA)
PAQ1999$yard <- ifelse(PAQ1999$PAD160 <= 600,PAQ1999$PAD120*PAQ1999$PAD160*4.5/30*7,NA)
PAQ1999$muscle <- ifelse(PAQ1999$PAD460 <= 300,PAQ1999$PAD460*4/30*7,NA)
PAQ1999$MET = rowSums(cbind(PAQ1999$bike,PAQ1999$yard,PAQ1999$muscle), na.rm = TRUE)
PAQ1999$MET = ifelse((is.na(PAQ1999$bike) & is.na(PAQ1999$yard) & is.na(PAQ1999$muscle)),NA,PAQ1999$MET)
PAQ1999 = PAQ1999 %>% select("SEQN","MET")

PAQ2001 = read_xpt("变量/Questionnaire/PAQ_B.xpt")
PAQ2001$PAQ050U[PAQ2001$PAQ050U == 1] = 1
PAQ2001$PAQ050U[PAQ2001$PAQ050U == 2] = 7
PAQ2001$PAQ050U[PAQ2001$PAQ050U == 3] = 30
PAQ2001$PAQ050U[PAQ2001$PAQ050U == 7] = NA
PAQ2001$PAQ050U[PAQ2001$PAQ050U == 9] = NA
PAQ2001$bike<-ifelse(PAQ2001$PAD080 <= 600,PAQ2001$PAQ050Q*PAQ2001$PAD080*4/PAQ2001$PAQ050U*7,NA)
PAQ2001$yard <- ifelse(PAQ2001$PAD160 <= 600,PAQ2001$PAD120*PAQ2001$PAD160*4.5/30*7,NA)
PAQ2001$muscle <- ifelse(PAQ2001$PAD460 <= 300,PAQ2001$PAD460*4/30*7,NA)
PAQ2001$MET = rowSums(cbind(PAQ2001$bike,PAQ2001$yard,PAQ2001$muscle), na.rm = TRUE)
PAQ2001$MET = ifelse((is.na(PAQ2001$bike) & is.na(PAQ2001$yard) & is.na(PAQ2001$muscle)),NA,PAQ2001$MET)
PAQ2001 = PAQ2001 %>% select("SEQN","MET")

PAQ2003 = read_xpt("变量/Questionnaire/PAQ_C.xpt")
PAQ2003$PAQ050U[PAQ2003$PAQ050U == 1] = 1
PAQ2003$PAQ050U[PAQ2003$PAQ050U == 2] = 7
PAQ2003$PAQ050U[PAQ2003$PAQ050U == 3] = 30
PAQ2003$PAQ050U[PAQ2003$PAQ050U == 7] = NA
PAQ2003$PAQ050U[PAQ2003$PAQ050U == 9] = NA
PAQ2003$bike<-ifelse(PAQ2003$PAD080 <= 600,PAQ2003$PAQ050Q*PAQ2003$PAD080*4/PAQ2003$PAQ050U*7,NA)
PAQ2003$yard <- ifelse(PAQ2003$PAD160 <= 600,PAQ2003$PAD120*PAQ2003$PAD160*4.5/30*7,NA)
PAQ2003$muscle <- ifelse(PAQ2003$PAD460 <= 300,PAQ2003$PAD460*4/30*7,NA)
PAQ2003$MET = rowSums(cbind(PAQ2003$bike,PAQ2003$yard,PAQ2003$muscle), na.rm = TRUE)
PAQ2003$MET = ifelse((is.na(PAQ2003$bike) & is.na(PAQ2003$yard) & is.na(PAQ2003$muscle)),NA,PAQ2003$MET)
PAQ2003 = PAQ2003 %>% select("SEQN","MET")

PAQ2005 = read_xpt("变量/Questionnaire/PAQ_D.xpt")
PAQ2005$PAQ050U[PAQ2005$PAQ050U == 1] = 1
PAQ2005$PAQ050U[PAQ2005$PAQ050U == 2] = 7
PAQ2005$PAQ050U[PAQ2005$PAQ050U == 3] = 30
PAQ2005$PAQ050U[PAQ2005$PAQ050U == 7] = NA
PAQ2005$PAQ050U[PAQ2005$PAQ050U == 9] = NA
PAQ2005$bike<-ifelse(PAQ2005$PAD080 <= 600,PAQ2005$PAQ050Q*PAQ2005$PAD080*4/PAQ2005$PAQ050U*7,NA)
PAQ2005$yard <- ifelse(PAQ2005$PAD160 <= 600,PAQ2005$PAD120*PAQ2005$PAD160*4.5/30*7,NA)
PAQ2005$muscle <- ifelse(PAQ2005$PAD460 <= 300,PAQ2005$PAD460*4/30*7,NA)
PAQ2005$MET = rowSums(cbind(PAQ2005$bike,PAQ2005$yard,PAQ2005$muscle), na.rm = TRUE)
PAQ2005$MET = ifelse((is.na(PAQ2005$bike) & is.na(PAQ2005$yard) & is.na(PAQ2005$muscle)),NA,PAQ2005$MET)
PAQ2005 = PAQ2005 %>% select("SEQN","MET")

PAQ2007 = read_xpt("变量/Questionnaire/PAQ_E.xpt")
PAQ2007$vwork<-ifelse(PAQ2007$PAD615 <= 1440,PAQ2007$PAQ610*PAQ2007$PAD615*8,NA)
PAQ2007$mwork<-ifelse(PAQ2007$PAD630 <=1440,PAQ2007$PAQ625*PAQ2007$PAD630*4,NA)
PAQ2007$walk<-ifelse(PAQ2007$PAD645<= 1440,PAQ2007$PAQ640*PAQ2007$PAD645*4,NA)
PAQ2007$vrecreational<-ifelse(PAQ2007$PAD660 <= 990,PAQ2007$PAQ655*PAQ2007$PAD660*8,NA)
PAQ2007$mrecreational<-ifelse(PAQ2007$PAD675<= 1440,PAQ2007$PAQ670*PAQ2007$PAD675*4,NA)
PAQ2007$MET = rowSums(cbind(PAQ2007$vwork,PAQ2007$mwork,PAQ2007$walk,PAQ2007$vrecreational,PAQ2007$mrecreational), na.rm = TRUE)
PAQ2007$MET = ifelse((is.na(PAQ2007$vwork) & is.na(PAQ2007$mwork) & is.na(PAQ2007$walk) & is.na(PAQ2007$vrecreational) & is.na(PAQ2007$mrecreational)),NA,PAQ2007$MET)
PAQ2007 = PAQ2007 %>% select("SEQN","MET")

PAQ2009 = read_xpt("变量/Questionnaire/PAQ_F.xpt")
PAQ2009$vwork<-ifelse(PAQ2009$PAD615 <= 1440,PAQ2009$PAQ610*PAQ2009$PAD615*8,NA)
PAQ2009$mwork<-ifelse(PAQ2009$PAD630 <=1440,PAQ2009$PAQ625*PAQ2009$PAD630*4,NA)
PAQ2009$walk<-ifelse(PAQ2009$PAD645<= 1440,PAQ2009$PAQ640*PAQ2009$PAD645*4,NA)
PAQ2009$vrecreational<-ifelse(PAQ2009$PAD660 <= 1440,PAQ2009$PAQ655*PAQ2009$PAD660*8,NA)
PAQ2009$mrecreational<-ifelse(PAQ2009$PAD675<= 1440,PAQ2009$PAQ670*PAQ2009$PAD675*4,NA)
PAQ2009$MET = rowSums(cbind(PAQ2009$vwork,PAQ2009$mwork,PAQ2009$walk,PAQ2009$vrecreational,PAQ2009$mrecreational), na.rm = TRUE)
PAQ2009$MET = ifelse((is.na(PAQ2009$vwork) & is.na(PAQ2009$mwork) & is.na(PAQ2009$walk) & is.na(PAQ2009$vrecreational) & is.na(PAQ2009$mrecreational)),NA,PAQ2009$MET)
PAQ2009 = PAQ2009 %>% select("SEQN","MET")

PAQ2011 = read_xpt("变量/Questionnaire/PAQ_G.xpt")
PAQ2011$vwork<-ifelse(PAQ2011$PAD615 <= 1440,PAQ2011$PAQ610*PAQ2011$PAD615*8,NA)
PAQ2011$mwork<-ifelse(PAQ2011$PAD630 <=1440,PAQ2011$PAQ625*PAQ2011$PAD630*4,NA)
PAQ2011$walk<-ifelse(PAQ2011$PAD645<= 1440,PAQ2011$PAQ640*PAQ2011$PAD645*4,NA)
PAQ2011$vrecreational<-ifelse(PAQ2011$PAD660 <= 1440,PAQ2011$PAQ655*PAQ2011$PAD660*8,NA)
PAQ2011$mrecreational<-ifelse(PAQ2011$PAD675<= 1440,PAQ2011$PAQ670*PAQ2011$PAD675*4,NA)
PAQ2011$MET = rowSums(cbind(PAQ2011$vwork,PAQ2011$mwork,PAQ2011$walk,PAQ2011$vrecreational,PAQ2011$mrecreational), na.rm = TRUE)
PAQ2011$MET = ifelse((is.na(PAQ2011$vwork) & is.na(PAQ2011$mwork) & is.na(PAQ2011$walk) & is.na(PAQ2011$vrecreational) & is.na(PAQ2011$mrecreational)),NA,PAQ2011$MET)
PAQ2011 = PAQ2011 %>% select("SEQN","MET")

PAQ2013 = read_xpt("变量/Questionnaire/PAQ_H.xpt")
PAQ2013$vwork<-ifelse(PAQ2013$PAD615 <= 1080,PAQ2013$PAQ610*PAQ2013$PAD615*8,NA)
PAQ2013$mwork<-ifelse(PAQ2013$PAD630 <=1440,PAQ2013$PAQ625*PAQ2013$PAD630*4,NA)
PAQ2013$walk<-ifelse(PAQ2013$PAD645<= 1440,PAQ2013$PAQ640*PAQ2013$PAD645*4,NA)
PAQ2013$vrecreational<-ifelse(PAQ2013$PAD660 <= 1440,PAQ2013$PAQ655*PAQ2013$PAD660*8,NA)
PAQ2013$mrecreational<-ifelse(PAQ2013$PAD675<= 1440,PAQ2013$PAQ670*PAQ2013$PAD675*4,NA)
PAQ2013$MET = rowSums(cbind(PAQ2013$vwork,PAQ2013$mwork,PAQ2013$walk,PAQ2013$vrecreational,PAQ2013$mrecreational), na.rm = TRUE)
PAQ2013$MET = ifelse((is.na(PAQ2013$vwork) & is.na(PAQ2013$mwork) & is.na(PAQ2013$walk) & is.na(PAQ2013$vrecreational) & is.na(PAQ2013$mrecreational)),NA,PAQ2013$MET)
PAQ2013 = PAQ2013 %>% select("SEQN","MET")

PAQ2015 = read_xpt("变量/Questionnaire/PAQ_I.xpt")
PAQ2015$vwork<-ifelse(PAQ2015$PAD615 <= 1440,PAQ2015$PAQ610*PAQ2015$PAD615*8,NA)
PAQ2015$mwork<-ifelse(PAQ2015$PAD630 <=1440,PAQ2015$PAQ625*PAQ2015$PAD630*4,NA)
PAQ2015$walk<-ifelse(PAQ2015$PAD645<= 1440,PAQ2015$PAQ640*PAQ2015$PAD645*4,NA)
PAQ2015$vrecreational<-ifelse(PAQ2015$PAD660 <= 1440,PAQ2015$PAQ655*PAQ2015$PAD660*8,NA)
PAQ2015$mrecreational<-ifelse(PAQ2015$PAD675<= 1440,PAQ2015$PAQ670*PAQ2015$PAD675*4,NA)
PAQ2015$MET = rowSums(cbind(PAQ2015$vwork,PAQ2015$mwork,PAQ2015$walk,PAQ2015$vrecreational,PAQ2015$mrecreational), na.rm = TRUE)
PAQ2015$MET = ifelse((is.na(PAQ2015$vwork) & is.na(PAQ2015$mwork) & is.na(PAQ2015$walk) & is.na(PAQ2015$vrecreational) & is.na(PAQ2015$mrecreational)),NA,PAQ2015$MET)
PAQ2015 = PAQ2015 %>% select("SEQN","MET")

PAQ2017 = read_xpt("变量/Questionnaire/PAQ_J.xpt")
PAQ2017$vwork<-ifelse(PAQ2017$PAD615 <= 1440,PAQ2017$PAQ610*PAQ2017$PAD615*8,NA)
PAQ2017$mwork<-ifelse(PAQ2017$PAD630 <=1440,PAQ2017$PAQ625*PAQ2017$PAD630*4,NA)
PAQ2017$walk<-ifelse(PAQ2017$PAD645<= 1440,PAQ2017$PAQ640*PAQ2017$PAD645*4,NA)
PAQ2017$vrecreational<-ifelse(PAQ2017$PAD660 <= 1440,PAQ2017$PAQ655*PAQ2017$PAD660*8,NA)
PAQ2017$mrecreational<-ifelse(PAQ2017$PAD675<= 1440,PAQ2017$PAQ670*PAQ2017$PAD675*4,NA)
PAQ2017$MET = rowSums(cbind(PAQ2017$vwork,PAQ2017$mwork,PAQ2017$walk,PAQ2017$vrecreational,PAQ2017$mrecreational), na.rm = TRUE)
PAQ2017$MET = ifelse((is.na(PAQ2017$vwork) & is.na(PAQ2017$mwork) & is.na(PAQ2017$walk) & is.na(PAQ2017$vrecreational) & is.na(PAQ2017$mrecreational)),NA,PAQ2017$MET)
PAQ2017 = PAQ2017 %>% select("SEQN","MET")

#合并PA数据
PAQ = join_all(list(PAQ1999,PAQ2001,PAQ2003,PAQ2005,PAQ2007,PAQ2009,PAQ2011,PAQ2013,PAQ2015,PAQ2017),
               by = c("SEQN"), type="full")

#删除变量，释放内存
rm(PAQ1999,PAQ2001,PAQ2003,PAQ2005,PAQ2007,PAQ2009,PAQ2011,PAQ2013,PAQ2015,PAQ2017)




#合并所有数据
data = join_all(list(ALQ,BIO,BMX,BPQ,BPX,CD,DEATH,demo, DIQ, GHB,HDL,INF, KIQ, LAB, MCQ, PAQ, SMQ,OCQ),by = c("SEQN"), type="full")

#判断是否有糖尿病或者糖尿病前期，1代表糖尿病前期，2代表糖尿病
data$DIQ.pre = ifelse(((data$LBXGLU >= 100 & data$LBXGLU < 126) | (data$LBXGH >= 5.7 & data$LBXGH < 6.5)), 1, 0)
data$DIQ.pre = ifelse(is.na(data$DIQ.pre), 0, data$DIQ.pre)
data$DIQ = ifelse((data$LBXGLU >= 126 | data$LBXGH >= 6.5 | data$DIQ010 == 1 | data$DIQ050 == 1 | data$DIQ070 == 1), 2, 0)
data$DIQ = ifelse(is.na(data$DIQ), 0, data$DIQ)
data$DIQ = data$DIQ + data$DIQ.pre
data$DIQ[data$DIQ == 3] = 2
#删除糖尿病协变量
data = dplyr::select(data, select = -c("LBXGLU","LBXGH", "DIQ010", "DIQ050", "DIQ070","DIQ.pre","DIQ160"))

#新增是否高血压变量
data$HBP =  ifelse((data$SBP >= 140 | data$DBP >= 90 | ifelse(is.na(data$BPQ020), 0, data$BPQ020) == 1 ), 1, 0)
#删除高血压协变量
data = dplyr::select(data, select = -c("DBP", "SBP","BPQ020"))

#新增炎症指标
data$UHR = data$UA / data$HDL
data$NLR = data$LBDNENO / data$LBDLYMNO
data$MLR = data$LBDMONO / data$LBDLYMNO
data$NMLR = (data$LBDNENO + data$LBDMONO) / data$LBDLYMNO
data$SII = (data$LBXPLTSI + data$LBDNENO) / data$LBDLYMNO
data$SIRI = (data$LBDNENO * data$LBDMONO) / data$LBDLYMNO

#变量重命名
data = rename(data, c("LBDNENO"="Neutrophils","LBDLYMNO"="Lymphocyte", "LBDMONO"="Monocyte", "LBXPLTSI"="Platelet"))



#新增吸烟情况，1=不吸烟，2=曾经吸烟，3=现在吸烟
data$SMQ = NA
data$SMQ[data$SMQ020 == 2] = 1
data$SMQ[data$SMQ020 == 1 & data$SMQ040 == 3] = 2
data$SMQ[data$SMQ040 == 1 | data$SMQ040 == 2] = 3
#删除吸烟协变量
data = dplyr::select(data, select = -c("SMQ020", "SMQ040"))

#将回答不知道或者拒绝回答重新复制为NA
data$DMDEDUC2 = ifelse((data$DMDEDUC2 == 7 | data$DMDEDUC2 == 9), NA, data$DMDEDUC2)
data$DMDMARTL = ifelse((data$DMDMARTL == 77 | data$DMDMARTL == 99), NA, data$DMDMARTL)
data$KIQ022 = ifelse((data$KIQ022 == 7 | data$KIQ022 == 9), NA, data$KIQ022)
data$MCQ220 = ifelse((data$MCQ220 == 7 | data$MCQ220 == 9), NA, data$MCQ220)

#处理教育数据
data$DMDEDUC2 = ifelse((data$DMDEDUC2 == 2), 1, data$DMDEDUC2)
data$DMDEDUC2 = ifelse((data$DMDEDUC2 == 3), 2, data$DMDEDUC2)
data$DMDEDUC2 = ifelse((data$DMDEDUC2 == 4 | data$DMDEDUC2 == 5), 3, data$DMDEDUC2)

#处理婚姻数据
data$DMDMARTL = ifelse((data$DMDMARTL == 6), 1, data$DMDMARTL)
data$DMDMARTL = ifelse((data$DMDMARTL == 3 | data$DMDMARTL == 4), 2, data$DMDMARTL)
data$DMDMARTL = ifelse((data$DMDMARTL == 5), 3, data$DMDMARTL)

#处理喝酒数据，1不饮酒，2适度饮酒，3过量饮酒
data$Alcoholstatus = ifelse((data$DRXTALCO == 0), 1, NA)
data$Alcoholstatus = ifelse((data$DRXTALCO > 0 & data$DRXTALCO < 28 & data$RIAGENDR == 1), 2, data$Alcoholstatus)
data$Alcoholstatus = ifelse((data$DRXTALCO > 0 & data$DRXTALCO < 14 & data$RIAGENDR == 2), 2, data$Alcoholstatus)
data$Alcoholstatus = ifelse((data$DRXTALCO >= 28 & data$DRXTALCO < 99998 & data$RIAGENDR == 1), 3, data$Alcoholstatus)
data$Alcoholstatus = ifelse((data$DRXTALCO >= 14 & data$DRXTALCO < 99998 & data$RIAGENDR == 2), 3, data$Alcoholstatus)

#处理权重数据
data$WTMEC = ifelse((data$SDDSRVYR %in% c(1,2)), data$WTINT4YR*2/10, data$WTINT2YR/10)


#变量重命名
data_raw = rename(data, c("BMXBMI"="BMI","LBXBCD"="Cd","mortstat"="Allcausemortstat",
                      "CVDDTH"="CVDmortstat","CANSERDTH"="CANSERmortstat","RIDAGEYR"="Age",
                      "RIDRETH1"="Race","DMDEDUC2"="Educationlevel","INDFMPIR"="PIR","RIAGENDR"="Sex",
                      "DMDMARTL"="Maritalstatus","MET"="Physicalactivity","SMQ"="Smokingstatus",
                      "KIQ022"="Failingkidneys","HBP"="Hypertension"))

#处理死亡数据
data_raw$Allcausemortstat[is.na(data_raw$Allcausemortstat)] <- 0
data_raw$CVDmortstat[is.na(data_raw$CVDmortstat)] <- 0
data_raw$CANSERmortstat[is.na(data_raw$CANSERmortstat)] <- 0

#删除变量，释放内存
rm(ALQ,PAQ,BIO,BMX,BPQ,BPX,CD,DEATH,demo,DIQ,GHB,HDL,INF,KIQ,LAB,MCQ,PAQ,SMQ,data)

save(data_raw, file="data_raw.RData")

#summary(factor(data$SMQ040))



